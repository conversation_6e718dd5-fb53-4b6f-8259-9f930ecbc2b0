package com.example.gymbro.domain.coach.repository

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.CoachMessage

/**
 * 历史持久化接口
 *
 * 根据706任务保存.md规格设计，提供消息级别的历史保存功能
 * 支持两阶段提交机制：本地写入 + 远端同步
 *
 * 核心职责：
 * - 在ConversationScope内管理消息持久化
 * - 支持FinalMessage事件触发的自动保存
 * - 提供flush机制防止数据丢失
 * - 实现两阶段提交的可靠性保证
 */
interface HistoryPersister {

    /**
     * 持久化消息事件
     *
     * 根据706任务保存.md规格，在parseTokenStream的FinalMessage回调中触发
     * 实现两阶段提交：
     * 1. Stage-1 本地写：事务插入ConversationMeta + MessageEvent
     * 2. Stage-2 远端同步：后台Worker批量上传
     *
     * @param conversationId 会话ID
     * @param message 消息对象
     * @param userId 用户ID（隐私合规）
     * @return 持久化结果
     */
    suspend fun persistMessage(
        conversationId: String,
        message: CoachMessage,
        userId: String,
    ): ModernResult<Unit>

    /**
     * 批量持久化消息事件
     *
     * 用于批量处理多条消息，提高性能
     *
     * @param conversationId 会话ID
     * @param messages 消息列表
     * @param userId 用户ID
     * @return 持久化结果
     */
    suspend fun persistMessages(
        conversationId: String,
        messages: List<CoachMessage>,
        userId: String,
    ): ModernResult<Unit>

    /**
     * 刷新待处理的数据
     *
     * 根据706任务保存.md规格，在ConversationScope关闭时调用
     * 确保所有待持久化的数据都被写入，防止数据丢失
     *
     * @param conversationId 会话ID（可选，null时刷新所有）
     * @return 刷新结果
     */
    suspend fun flushPending(conversationId: String? = null): ModernResult<Unit>

    /**
     * 创建或更新会话元数据
     *
     * 在首次消息保存时自动创建ConversationMeta
     * 后续消息保存时更新活跃时间和消息计数
     *
     * @param conversationId 会话ID
     * @param title 会话标题
     * @param userId 用户ID
     * @return 操作结果
     */
    suspend fun ensureConversationMeta(
        conversationId: String,
        title: String,
        userId: String,
    ): ModernResult<Unit>

    /**
     * 更新会话标题
     *
     * 支持AI自动生成标题或用户自定义标题
     *
     * @param conversationId 会话ID
     * @param title 新标题
     * @return 更新结果
     */
    suspend fun updateConversationTitle(
        conversationId: String,
        title: String,
    ): ModernResult<Unit>

    /**
     * 更新会话概要
     *
     * 由AI生成的会话概要，用于历史记录列表显示
     *
     * @param conversationId 会话ID
     * @param summary 会话概要
     * @return 更新结果
     */
    suspend fun updateConversationSummary(
        conversationId: String,
        summary: String,
    ): ModernResult<Unit>

    /**
     * 标记消息为已同步
     *
     * 两阶段提交的第二阶段：远端同步成功后调用
     *
     * @param eventIds 事件ID列表
     * @return 标记结果
     */
    suspend fun markMessagesSynced(eventIds: List<String>): ModernResult<Unit>

    /**
     * 标记会话为已同步
     *
     * @param conversationId 会话ID
     * @return 标记结果
     */
    suspend fun markConversationSynced(conversationId: String): ModernResult<Unit>

    /**
     * 获取未同步的消息
     *
     * 用于WorkManager后台同步任务
     *
     * @param limit 限制数量
     * @return 未同步的消息列表
     */
    suspend fun getUnsyncedMessages(limit: Int = 100): ModernResult<List<CoachMessage>>

    /**
     * 获取未同步的会话
     *
     * @param limit 限制数量
     * @return 未同步的会话ID列表
     */
    suspend fun getUnsyncedConversations(limit: Int = 100): ModernResult<List<String>>

    /**
     * 清理过期数据
     *
     * 根据706任务保存.md防雷点，定期清理过期的已删除数据
     *
     * @param olderThanDays 保留天数
     * @return 清理结果
     */
    suspend fun cleanupExpiredData(olderThanDays: Int = 30): ModernResult<Int>

    /**
     * 🔥 【单一数据源修复】获取会话历史记录
     *
     * 从ChatRaw表查询会话的完整历史记录，包括thinking内容
     * 注意：已移除MessageEvent表查询，统一使用ChatRaw表作为单一数据源
     *
     * @param conversationId 会话ID
     * @param limit 限制数量（默认100）
     * @return 消息历史列表
     */
    suspend fun getConversationHistory(
        conversationId: String,
        limit: Int = 100,
    ): ModernResult<List<CoachMessage>>

    /**
     * 🔥 【多轮对话修复】获取用户的会话列表
     *
     * 从ConversationMeta表查询用户的所有会话
     *
     * @param userId 用户ID
     * @param limit 限制数量（默认50）
     * @return 会话列表
     */
    suspend fun getUserConversations(
        userId: String,
        limit: Int = 50,
    ): ModernResult<List<String>> // 返回conversationId列表

    /**
     * 获取持久化统计信息
     *
     * 用于监控和调试
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    suspend fun getPersistenceStats(userId: String): ModernResult<PersistenceStats>

    /**
     * 持久化统计信息数据类
     */
    data class PersistenceStats(
        val totalConversations: Int,
        val totalMessages: Int,
        val unsyncedMessages: Int,
        val unsyncedConversations: Int,
        val lastSyncTime: Long?,
    )
}

# ThinkingBox 双时序架构(Dual-Timing Architecture)

## 核心概念
**数据时序(Backend Processing)**: Token到达 → StreamingThinkingMLParser → SemanticEvent → DomainMapper → ThinkingEvent → ThinkingReducer → 状态更新
**UI时序(Frontend Rendering)**: 状态更新 → Contract.State → ThinkingBoxScreen → UI组件 → 动画渲染 → PhaseAnimFinished事件

## 双时序验证机制
1. **数据时序**: PhaseEnd事件更新状态 → phase.isComplete = true
2. **UI时序**: 动画完成后验证状态 → PhaseAnimFinished(phaseId) → 检查phase.isComplete → 验证通过 → 切换下一phase
3. **验证点**: PhaseAnimFinished时检查phase.isComplete状态，双握手机制确保切换安全

## Phase切换流程
- perthink立即显示，正式phase加入等待队列依次渲染
- 每个phase必须等待UI文本输出完整后才能切换下一个phase
- 双握手条件: phase.isComplete=true + PhaseAnimFinished事件 = 真正切换
- 15秒超时保护机制，防止卡死
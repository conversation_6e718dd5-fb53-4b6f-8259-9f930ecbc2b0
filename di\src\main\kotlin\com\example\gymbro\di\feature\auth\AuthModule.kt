package com.example.gymbro.di.feature.auth

import com.example.gymbro.data.remote.AuthApi
import com.example.gymbro.data.remote.firebase.auth.AccountLinkingService
import com.example.gymbro.data.remote.firebase.auth.AuthStateService
import com.example.gymbro.data.remote.firebase.auth.FirebaseAuthService
import com.example.gymbro.data.remote.firebase.auth.PhoneVerificationService
import com.example.gymbro.data.remote.firebase.auth.UserBackupService
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import retrofit2.Retrofit
import javax.inject.Singleton
// 移除废弃的Google Sign-In API导入
// import com.example.gymbro.data.service.auth.GoogleSignInService
// import com.google.android.gms.auth.api.signin.GoogleSignInClient

/**
 * 认证功能模块
 * 整合了所有与认证相关的依赖，包括API服务、Token管理、Firebase认证服务等
 */
@Module
@InstallIn(SingletonComponent::class)
object AuthModule {
    /**
     * 提供认证相关API服务
     */
    @Provides
    @Singleton
    fun provideAuthApi(retrofit: Retrofit): AuthApi {
        return retrofit.create(AuthApi::class.java)
    }

    // === Firebase认证服务相关提供者 ===
    // 注意：Firebase Auth和Firestore实例由FirebaseModule提供，避免重复定义

    /**
     * 提供Firebase核心认证服务
     */
    @Provides
    @Singleton
    fun provideFirebaseAuthService(
        auth: FirebaseAuth,
    ): FirebaseAuthService {
        return FirebaseAuthService(auth)
    }

    /**
     * 提供手机验证服务
     */
    @Provides
    @Singleton
    fun providePhoneVerificationService(
        auth: FirebaseAuth,
    ): PhoneVerificationService {
        return PhoneVerificationService(auth)
    }

    /**
     * 提供账户关联服务
     */
    @Provides
    @Singleton
    fun provideAccountLinkingService(
        auth: FirebaseAuth,
    ): AccountLinkingService {
        return AccountLinkingService(auth)
    }

    /**
     * 提供用户备份服务
     */
    @Provides
    @Singleton
    fun provideUserBackupService(
        auth: FirebaseAuth,
        firestore: FirebaseFirestore,
    ): UserBackupService {
        return UserBackupService(auth, firestore)
    }

    /**
     * 提供认证状态服务
     */
    @Provides
    @Singleton
    fun provideAuthStateService(
        auth: FirebaseAuth,
    ): AuthStateService {
        return AuthStateService(auth)
    }

    // Google Sign-In API已废弃，相关服务已移除
    // 如需Google登录功能，请使用GoogleCredentialService和Credential Manager API
}

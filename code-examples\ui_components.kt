package com.example.gymbro.examples

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Refresh
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.designSystem.components.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.core.ui.text.UiText

/**
 * UI组件最佳实践示例
 * 
 * 本示例展示了GymBro项目中Compose组件的标准实现模式：
 * 1. Box+LazyColumn+Surface标准布局模式
 * 2. Tokens系统的正确使用
 * 3. MaterialTheme.workoutColors.*主题色彩
 * 4. @GymBroPreview预览支持
 * 5. 无硬编码值，完全Token化
 * 
 * 参考标准：
 * - 使用Tokens.*而非硬编码值
 * - 使用MaterialTheme.workoutColors.*而非Material3直接颜色
 * - 遵循Box+LazyColumn+Surface模式
 * - 所有组件必须包含@GymBroPreview
 */

// ================================
// 1. 标准Box+LazyColumn+Surface模式
// ================================

/**
 * 示例列表屏幕 - 标准布局模式
 * 
 * 展示Box+LazyColumn+Surface的正确使用方式
 */
@Composable
fun ExampleListScreen(
    items: List<ExampleItem>,
    isLoading: Boolean = false,
    onItemClick: (ExampleItem) -> Unit = {},
    onRefresh: () -> Unit = {},
    onAddNew: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    // 🔥 Box作为根容器 - 支持层叠布局
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(MaterialTheme.workoutColors.cardBackground) // 🔥 使用主题背景色
    ) {
        // 🔥 主内容区域 - LazyColumn
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(Tokens.Spacing.Medium), // 🔥 使用Token间距
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small) // 🔥 使用Token间距
        ) {
            // 标题区域
            item {
                ExampleHeaderCard(
                    title = "示例列表",
                    subtitle = "共 ${items.size} 项",
                    onRefresh = onRefresh
                )
            }
            
            // 列表项
            items(items) { item ->
                ExampleItemCard(
                    item = item,
                    onClick = { onItemClick(item) }
                )
            }
            
            // 加载状态
            if (isLoading) {
                item {
                    ExampleLoadingCard()
                }
            }
        }
        
        // 🔥 浮动操作按钮 - 层叠在Box中
        FloatingActionButton(
            onClick = onAddNew,
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(Tokens.Spacing.Large), // 🔥 使用Token间距
            containerColor = MaterialTheme.workoutColors.accentPrimary, // 🔥 使用主题强调色
            contentColor = MaterialTheme.colorScheme.onPrimary
        ) {
            Icon(
                imageVector = Icons.Default.Add,
                contentDescription = "添加新项目",
                modifier = Modifier.size(Tokens.Icon.Medium) // 🔥 使用Token图标尺寸
            )
        }
    }
}

// ================================
// 2. Surface组件示例
// ================================

/**
 * 示例头部卡片 - Surface使用示例
 */
@Composable
fun ExampleHeaderCard(
    title: String,
    subtitle: String,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier
) {
    // 🔥 Surface提供Material Design卡片效果
    Surface(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(Tokens.Radius.Card), // 🔥 使用Token圆角
        color = MaterialTheme.workoutColors.cardBackground, // 🔥 使用主题卡片背景色
        shadowElevation = Tokens.Elevation.Card, // 🔥 使用Token阴影
        border = BorderStroke(
            width = Tokens.Border.Thin, // 🔥 使用Token边框宽度
            color = MaterialTheme.workoutColors.cardBorder // 🔥 使用主题边框色
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium), // 🔥 使用Token内边距
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier.weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.headlineSmall,
                    color = MaterialTheme.workoutColors.aiCoachText // 🔥 使用主题文本色
                )
                
                Spacer(modifier = Modifier.height(Tokens.Spacing.XSmall)) // 🔥 使用Token间距
                
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.workoutColors.accentSecondary // 🔥 使用主题次要色
                )
            }
            
            IconButton(
                onClick = onRefresh,
                modifier = Modifier.size(Tokens.Button.IconSize) // 🔥 使用Token按钮尺寸
            ) {
                Icon(
                    imageVector = Icons.Default.Refresh,
                    contentDescription = "刷新",
                    tint = MaterialTheme.workoutColors.accentPrimary, // 🔥 使用主题强调色
                    modifier = Modifier.size(Tokens.Icon.Small) // 🔥 使用Token图标尺寸
                )
            }
        }
    }
}

/**
 * 示例项目卡片 - 完整Token化组件
 */
@Composable
fun ExampleItemCard(
    item: ExampleItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Surface(
        onClick = onClick,
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(Tokens.Radius.Card), // 🔥 使用Token圆角
        color = MaterialTheme.workoutColors.cardBackground, // 🔥 使用主题卡片背景色
        shadowElevation = Tokens.Elevation.Small, // 🔥 使用Token阴影
        border = BorderStroke(
            width = Tokens.Border.Thin, // 🔥 使用Token边框宽度
            color = MaterialTheme.workoutColors.cardBorder // 🔥 使用主题边框色
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium) // 🔥 使用Token内边距
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = item.title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.workoutColors.aiCoachText, // 🔥 使用主题文本色
                    modifier = Modifier.weight(1f)
                )
                
                // 状态指示器
                Surface(
                    shape = RoundedCornerShape(Tokens.Radius.Small), // 🔥 使用Token圆角
                    color = if (item.isCompleted) {
                        MaterialTheme.workoutColors.completedState // 🔥 使用主题状态色
                    } else {
                        MaterialTheme.workoutColors.activeState // 🔥 使用主题状态色
                    }
                ) {
                    Text(
                        text = if (item.isCompleted) "已完成" else "进行中",
                        style = MaterialTheme.typography.labelSmall,
                        color = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier.padding(
                            horizontal = Tokens.Spacing.Small, // 🔥 使用Token间距
                            vertical = Tokens.Spacing.XSmall // 🔥 使用Token间距
                        )
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(Tokens.Spacing.Small)) // 🔥 使用Token间距
            
            // 描述文本
            Text(
                text = item.description,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.workoutColors.accentSecondary, // 🔥 使用主题次要色
                maxLines = 2
            )
        }
    }
}

/**
 * 加载状态卡片
 */
@Composable
fun ExampleLoadingCard(
    modifier: Modifier = Modifier
) {
    Surface(
        modifier = modifier.fillMaxWidth(),
        shape = RoundedCornerShape(Tokens.Radius.Card), // 🔥 使用Token圆角
        color = MaterialTheme.workoutColors.cardBackground, // 🔥 使用主题卡片背景色
        shadowElevation = Tokens.Elevation.Small // 🔥 使用Token阴影
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Large), // 🔥 使用Token内边距
            contentAlignment = Alignment.Center
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium) // 🔥 使用Token间距
            ) {
                CircularProgressIndicator(
                    modifier = Modifier.size(Tokens.Icon.Small), // 🔥 使用Token图标尺寸
                    color = MaterialTheme.workoutColors.accentPrimary, // 🔥 使用主题强调色
                    strokeWidth = Tokens.Border.Medium // 🔥 使用Token边框宽度
                )
                
                Text(
                    text = "加载中...",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.workoutColors.accentSecondary // 🔥 使用主题次要色
                )
            }
        }
    }
}

// ================================
// 3. 预览组件
// ================================

@GymBroPreview
@Composable
private fun ExampleListScreenPreview() {
    GymBroTheme {
        ExampleListScreen(
            items = listOf(
                ExampleItem(
                    id = "1",
                    title = "示例项目 1",
                    description = "这是一个示例项目的描述文本，展示了如何使用Token系统。",
                    isCompleted = true
                ),
                ExampleItem(
                    id = "2",
                    title = "示例项目 2",
                    description = "另一个示例项目，展示了不同的状态显示。",
                    isCompleted = false
                )
            ),
            isLoading = false
        )
    }
}

@GymBroPreview
@Composable
private fun ExampleHeaderCardPreview() {
    GymBroTheme {
        ExampleHeaderCard(
            title = "示例标题",
            subtitle = "共 5 项",
            onRefresh = {}
        )
    }
}

@GymBroPreview
@Composable
private fun ExampleItemCardPreview() {
    GymBroTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small)
        ) {
            ExampleItemCard(
                item = ExampleItem(
                    id = "1",
                    title = "已完成项目",
                    description = "这是一个已完成的项目示例",
                    isCompleted = true
                ),
                onClick = {}
            )
            
            ExampleItemCard(
                item = ExampleItem(
                    id = "2",
                    title = "进行中项目",
                    description = "这是一个正在进行中的项目示例",
                    isCompleted = false
                ),
                onClick = {}
            )
        }
    }
}

@GymBroPreview
@Composable
private fun ExampleLoadingCardPreview() {
    GymBroTheme {
        ExampleLoadingCard()
    }
}

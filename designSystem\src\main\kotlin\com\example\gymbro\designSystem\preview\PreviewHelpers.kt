package com.example.gymbro.designSystem.preview

import android.content.res.Configuration
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * Preview工具函数集合
 *
 * 提供标准化的预览包装函数，确保所有组件预览的一致性
 */

/**
 * 标准主题预览包装器
 *
 * @param darkTheme 是否使用深色主题
 * @param content 要预览的组件内容
 */
@Composable
fun GymBroPreviewWrapper(
    darkTheme: Boolean = false,
    content: @Composable () -> Unit,
) {
    GymBroTheme(darkTheme = darkTheme) {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = if (darkTheme) ColorTokens.Dark.Background else ColorTokens.Light.Background,
        ) {
            content()
        }
    }
}

/**
 * 带边距的预览包装器
 *
 * @param darkTheme 是否使用深色主题
 * @param padding 内边距
 * @param content 要预览的组件内容
 */
@Composable
fun GymBroPreviewWithPadding(
    darkTheme: Boolean = false,
    padding: PaddingValues = PaddingValues(Tokens.Spacing.Medium),
    content: @Composable () -> Unit,
) {
    GymBroPreviewWrapper(darkTheme = darkTheme) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding),
        ) {
            content()
        }
    }
}

/**
 * 组件状态展示预览器
 *
 * 在一个预览中展示组件的多种状态
 *
 * @param title 预览标题
 * @param darkTheme 是否使用深色主题
 * @param states 状态列表，包含状态名称和对应组件
 */
@Composable
fun GymBroStateShowcase(
    title: String,
    darkTheme: Boolean = false,
    states: List<Pair<String, @Composable () -> Unit>>,
) {
    GymBroPreviewWrapper(darkTheme = darkTheme) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            Text(
                text = title,
                style = androidx.compose.material3.MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = if (darkTheme) ColorTokens.Dark.OnBackground else ColorTokens.Light.OnBackground,
            )

            states.forEach { (stateName, stateContent) ->
                Column {
                    Text(
                        text = stateName,
                        style = androidx.compose.material3.MaterialTheme.typography.labelMedium,
                        color = if (darkTheme) ColorTokens.Dark.OnSurfaceVariant else ColorTokens.Light.OnSurfaceVariant,
                    )
                    Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
                    stateContent()
                    Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
                }
            }
        }
    }
}

/**
 * Token演示预览器
 *
 * 展示设计Token在不同主题下的效果
 *
 * @param title Token类别标题
 * @param lightExample 浅色主题示例
 * @param darkExample 深色主题示例
 */
@Composable
fun GymBroTokenShowcase(
    title: String,
    lightExample: @Composable () -> Unit,
    darkExample: @Composable () -> Unit,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
    ) {
        Text(
            text = title,
            style = androidx.compose.material3.MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.Bold,
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 浅色主题示例
            Column(
                modifier = Modifier
                    .weight(1f)
                    .background(
                        Color.White,
                        shape = androidx.compose.foundation.shape.RoundedCornerShape(Tokens.Radius.Card),
                    )
                    .padding(Tokens.Spacing.Medium),
            ) {
                Text(
                    text = "Light Theme",
                    style = androidx.compose.material3.MaterialTheme.typography.labelSmall,
                    color = Color.Black,
                )
                Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
                GymBroTheme(darkTheme = false) {
                    lightExample()
                }
            }

            // 深色主题示例
            Column(
                modifier = Modifier
                    .weight(1f)
                    .background(
                        Color.Black,
                        shape = androidx.compose.foundation.shape.RoundedCornerShape(Tokens.Radius.Card),
                    )
                    .padding(Tokens.Spacing.Medium),
            ) {
                Text(
                    text = "Dark Theme",
                    style = androidx.compose.material3.MaterialTheme.typography.labelSmall,
                    color = Color.White,
                )
                Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
                GymBroTheme(darkTheme = true) {
                    darkExample()
                }
            }
        }
    }
}

/**
 * 设备信息显示
 *
 * 在预览中显示当前设备配置信息
 */
@Composable
fun DeviceInfoOverlay() {
    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp
    val screenHeight = configuration.screenHeightDp
    val orientation = if (configuration.orientation == Configuration.ORIENTATION_LANDSCAPE) {
        "Landscape"
    } else {
        "Portrait"
    }
    val densityDpi = configuration.densityDpi
    val uiMode = configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
    val isNightMode = uiMode == Configuration.UI_MODE_NIGHT_YES

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.TopStart,
    ) {
        Surface(
            color = if (isNightMode) Color.Black.copy(alpha = 0.8f) else Color.White.copy(alpha = 0.8f),
            shape = androidx.compose.foundation.shape.RoundedCornerShape(Tokens.Radius.Small),
        ) {
            Column(
                modifier = Modifier.padding(Tokens.Spacing.Small),
            ) {
                Text(
                    text = "Device Info",
                    style = androidx.compose.material3.MaterialTheme.typography.labelSmall,
                    fontWeight = FontWeight.Bold,
                    color = if (isNightMode) Color.White else Color.Black,
                )
                Text(
                    text = "$screenWidth×$screenHeight dp",
                    style = androidx.compose.material3.MaterialTheme.typography.bodySmall,
                    color = if (isNightMode) Color.White else Color.Black,
                )
                Text(
                    text = "$orientation, $densityDpi DPI",
                    style = androidx.compose.material3.MaterialTheme.typography.bodySmall,
                    color = if (isNightMode) Color.White else Color.Black,
                )
                Text(
                    text = if (isNightMode) "Dark Mode" else "Light Mode",
                    style = androidx.compose.material3.MaterialTheme.typography.bodySmall,
                    color = if (isNightMode) Color.White else Color.Black,
                )
            }
        }
    }
}

/**
 * 无障碍信息显示
 *
 * 在预览中显示字体缩放和无障碍相关信息
 */
@Composable
fun AccessibilityInfoOverlay() {
    val configuration = LocalConfiguration.current
    val fontScale = configuration.fontScale

    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.TopEnd,
    ) {
        Surface(
            color = Color.Blue.copy(alpha = 0.8f),
            shape = androidx.compose.foundation.shape.RoundedCornerShape(Tokens.Radius.Small),
        ) {
            Column(
                modifier = Modifier.padding(Tokens.Spacing.Small),
            ) {
                Text(
                    text = "Accessibility",
                    style = androidx.compose.material3.MaterialTheme.typography.labelSmall,
                    fontWeight = FontWeight.Bold,
                    color = Color.White,
                )
                Text(
                    text = "Font Scale: ${fontScale}x",
                    style = androidx.compose.material3.MaterialTheme.typography.bodySmall,
                    color = Color.White,
                )
            }
        }
    }
}

// === 示例预览函数 ===

/**
 * 预览包装器使用示例
 */
@GymBroPreview
@Composable
private fun PreviewWrapperExample() {
    GymBroPreviewWithPadding {
        Text(
            text = "这是一个使用预览包装器的示例",
            color = ColorTokens.Light.OnBackground, // 正确使用Token
        )
    }
}

/**
 * 状态展示预览示例
 */
@GymBroStatePreview
@Composable
private fun StateShowcaseExample() {
    GymBroStateShowcase(
        title = "Button States",
        states = listOf(
            "Normal" to {
                Surface(
                    color = ColorTokens.CTAPrimary,
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(Tokens.Radius.Button),
                ) {
                    Text(
                        text = "Normal Button",
                        modifier = Modifier.padding(Tokens.Spacing.Medium),
                        color = Color.White,
                    )
                }
            },
            "Disabled" to {
                Surface(
                    color = ColorTokens.Component.ButtonDisabled,
                    shape = androidx.compose.foundation.shape.RoundedCornerShape(Tokens.Radius.Button),
                ) {
                    Text(
                        text = "Disabled Button",
                        modifier = Modifier.padding(Tokens.Spacing.Medium),
                        color = Color.White,
                    )
                }
            },
        ),
    )
}

/**
 * Token展示预览示例
 */
@GymBroTokenValidationPreview
@Composable
private fun TokenShowcaseExample() {
    GymBroTokenShowcase(
        title = "Color Tokens",
        lightExample = {
            Box(
                modifier = Modifier
                    .width(100.dp)
                    .height(50.dp)
                    .background(
                        ColorTokens.Light.Primary,
                        shape = androidx.compose.foundation.shape.RoundedCornerShape(Tokens.Radius.Small),
                    ),
            )
        },
        darkExample = {
            Box(
                modifier = Modifier
                    .width(100.dp)
                    .height(50.dp)
                    .background(
                        ColorTokens.Dark.Primary,
                        shape = androidx.compose.foundation.shape.RoundedCornerShape(Tokens.Radius.Small),
                    ),
            )
        },
    )
}

/**
 * 设备信息叠加预览示例
 */
@Preview(
    name = "Device Info Overlay",
    group = "Preview Helpers",
    showBackground = true,
)
@Composable
private fun DeviceInfoOverlayExample() {
    GymBroPreviewWrapper {
        Box(modifier = Modifier.fillMaxSize()) {
            Text(
                text = "主要内容区域",
                modifier = Modifier.align(Alignment.Center),
            )
            DeviceInfoOverlay()
            AccessibilityInfoOverlay()
        }
    }
}

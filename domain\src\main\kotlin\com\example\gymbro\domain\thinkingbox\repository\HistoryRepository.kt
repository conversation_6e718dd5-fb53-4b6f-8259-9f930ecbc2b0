package com.example.gymbro.domain.thinkingbox.repository

import com.example.gymbro.domain.thinkingbox.model.ThinkingFinalEntity
import com.example.gymbro.domain.thinkingbox.model.ThinkingHistoryComplete
import com.example.gymbro.domain.thinkingbox.model.ThinkingMessageEntity
import com.example.gymbro.domain.thinkingbox.model.ThinkingPhaseEntity

/**
 * HistoryRepository - ThinkingBox历史记录存储接口
 *
 * 🔥 根据71thinkbox-choach.md方案定义的数据库表结构：
 * - thinking_message: 消息级别的元数据
 * - thinking_phase: 每个阶段的详细内容
 * - thinking_final: 最终的markdown内容
 */
interface HistoryRepository {

    /**
     * 插入思考消息记录
     */
    suspend fun insertThinkingMessage(
        messageId: String,
        status: String,
        startedAt: Long,
        finishedAt: Long? = null,
        durationMs: Long? = null,
        tokenCount: Int? = null,
    )

    /**
     * 更新思考消息记录
     */
    suspend fun updateThinkingMessage(
        messageId: String,
        status: String,
        finishedAt: Long,
        durationMs: Long,
        tokenCount: Int,
    )

    /**
     * 插入思考阶段记录
     */
    suspend fun insertThinkingPhase(
        messageId: String,
        phaseId: String,
        title: String,
        content: String,
        complete: Boolean,
    )

    /**
     * 插入最终内容记录
     */
    suspend fun insertThinkingFinal(
        messageId: String,
        markdown: String,
    )

    /**
     * 查询思考消息记录
     */
    suspend fun getThinkingMessage(messageId: String): ThinkingMessageEntity?

    /**
     * 查询思考阶段记录
     */
    suspend fun getThinkingPhases(messageId: String): List<ThinkingPhaseEntity>

    /**
     * 查询最终内容记录
     */
    suspend fun getThinkingFinal(messageId: String): ThinkingFinalEntity?

    /**
     * 查询完整的思考历史（用于回放）
     */
    suspend fun getCompleteThinkingHistory(messageId: String): ThinkingHistoryComplete?
}
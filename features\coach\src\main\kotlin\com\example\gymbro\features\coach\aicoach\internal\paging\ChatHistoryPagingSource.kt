package com.example.gymbro.features.coach.aicoach.internal.paging

import androidx.paging.PagingSource
import androidx.paging.PagingState
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.session.mapper.ChatSessionMapper.toDomainMessage
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.aicoach.AiCoachMapper
import timber.log.Timber

/**
 * ChatHistoryPagingSource - 聊天历史分页数据源
 *
 * 实现基于ChatRawDao的直接分页加载
 * 配合ChatHistoryRemoteMediator提供完整的分页体验
 *
 * @param sessionId 会话ID
 * @param chatRawDao 数据访问层
 */
internal class ChatHistoryPagingSource(
    private val sessionId: String,
    private val chatRawDao: ChatRawDao,
) : PagingSource<Int, AiCoachContract.MessageUi>() {

    companion object {
        private const val TAG = "ChatHistoryPagingSource"
        private const val STARTING_PAGE_INDEX = 0
    }

    override suspend fun load(params: LoadParams<Int>): LoadResult<Int, AiCoachContract.MessageUi> {
        return try {
            val pageIndex = params.key ?: STARTING_PAGE_INDEX
            val offset = pageIndex * params.loadSize

            Timber.tag(
                TAG,
            ).d(
                "🔥 加载分页数据: sessionId=$sessionId, pageIndex=$pageIndex, offset=$offset, loadSize=${params.loadSize}",
            )

            // 🔥 使用降序分页方法，实现ChatGPT式体验
            val rawMessages = chatRawDao.getMessagesBySessionPagedDesc(
                sessionId = sessionId,
                limit = params.loadSize,
                offset = offset,
            )

            Timber.tag(TAG).d("🔥 加载完成: ${rawMessages.size} 条原始消息")

            // 🔥 数据转换：ChatRaw -> CoachMessage -> MessageUi
            val domainMessages = rawMessages.map { it.toDomainMessage() }
            val messageUiList = AiCoachMapper.convertCoachMessagesToMessageUi(domainMessages)

            Timber.tag(TAG).d("🔥 转换完成: ${messageUiList.size} 条UI消息")

            // 🔥 计算分页键
            val prevKey = if (pageIndex == STARTING_PAGE_INDEX) null else pageIndex - 1
            val nextKey = if (rawMessages.isEmpty() || rawMessages.size < params.loadSize) null else pageIndex + 1

            LoadResult.Page(
                data = messageUiList,
                prevKey = prevKey,
                nextKey = nextKey,
            )
        } catch (exception: Exception) {
            Timber.tag(TAG).e(exception, "🔥 分页加载失败: sessionId=$sessionId")
            LoadResult.Error(exception)
        }
    }

    override fun getRefreshKey(state: PagingState<Int, AiCoachContract.MessageUi>): Int? {
        // 🔥 刷新时回到最近访问的页面附近
        return state.anchorPosition?.let { anchorPosition ->
            val anchorPage = state.closestPageToPosition(anchorPosition)
            anchorPage?.prevKey?.plus(1) ?: anchorPage?.nextKey?.minus(1)
        }
    }
}

/**
 * ChatRaw到Domain模型的转换扩展函数
 *
 * 与ChatHistoryRemoteMediator中的转换函数保持一致
 */
private fun com.example.gymbro.data.local.entity.ChatRaw.toDomainMessage(): com.example.gymbro.domain.coach.model.CoachMessage {
    return when (role) {
        com.example.gymbro.data.local.entity.ChatRaw.ROLE_USER -> {
            com.example.gymbro.domain.coach.model.CoachMessage.UserMessage(
                id = messageId,
                timestamp = timestamp,
                content = content,
            )
        }
        com.example.gymbro.data.local.entity.ChatRaw.ROLE_ASSISTANT -> {
            com.example.gymbro.domain.coach.model.CoachMessage.AiMessage(
                id = messageId,
                timestamp = timestamp,
                content = content,
                // 🔥 TODO: 后续添加finalMarkdown字段支持
                // finalMarkdown = finalMarkdown,
                functionCall = metadata["functionCall"] as? String,
                mcp = metadata["mcp"] as? String,
                summary = metadata["summary"] as? String,
                rawTokens = metadata["rawTokens"] as? String,
            )
        }
        else -> {
            // 🔥 兜底处理：默认当作AI消息
            com.example.gymbro.domain.coach.model.CoachMessage.AiMessage(
                id = messageId,
                timestamp = timestamp,
                content = content,
            )
        }
    }
}

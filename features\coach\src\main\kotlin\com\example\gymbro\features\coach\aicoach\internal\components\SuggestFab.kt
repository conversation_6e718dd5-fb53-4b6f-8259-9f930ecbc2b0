package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.*
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ui.text.asString
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.coachTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.shared.models.coach.SuggestionConfig

/**
 * 建议项数据类
 */
private data class SuggestionItem(
    val text: String,
    val icon: ImageVector,
    val prompt: String = text,
)

/**
 * 悬浮建议标签 - ChatGPT风格的简洁实现
 *
 * 🎯 核心功能：
 * - 展示基础建议标签
 * - "更多"按钮展开额外标签
 * - 点击标签触发onSuggestionClick
 */
@Composable
internal fun WelcomeScreen(
    suggestionConfig: SuggestionConfig?,
    suggestionChipsVisible: Boolean,
    onSuggestionClick: (String) -> Unit,
    onHideSuggestionChips: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(Tokens.Spacing.Large),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
    ) {
        Text(
            text = com.example.gymbro.features.coach.aicoach.internal.components.AiCoachStrings.welcomeQuestion.asString(),
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Medium,
            color = MaterialTheme.coachTheme.textPrimary, // 🔥 【主题统一】使用Coach主题主文本色
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = Tokens.Spacing.XLarge),
        )

        SuggestionTags(
            suggestionConfig = suggestionConfig,
            suggestionChipsVisible = suggestionChipsVisible,
            onSuggestionClick = onSuggestionClick,
            onHideSuggestionChips = onHideSuggestionChips,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

@Composable
private fun SuggestionTags(
    suggestionConfig: SuggestionConfig?,
    suggestionChipsVisible: Boolean,
    onSuggestionClick: (String) -> Unit,
    onHideSuggestionChips: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var expanded by remember { mutableStateOf(false) }

    // 🔥 【预设数据清理】移除硬编码建议列表，完全依赖配置数据
    val allSuggestions = remember(suggestionConfig) {
        val suggestions = if (suggestionConfig != null) {
            buildSuggestionsFromConfig(suggestionConfig)
        } else {
            // 🔥 无配置时显示空列表，避免硬编码预设数据
            emptyList()
        }
        // 🔥 确保最多显示10个建议项
        suggestions.take(10)
    }

    // 🔥 修复：水平卡片样式 - 基础标签（前5个）
    val baseSuggestions = allSuggestions.take(5)
    // 🔥 修复：水平卡片样式 - 扩展标签（剩余的，最多5个）
    val extendedSuggestions = allSuggestions.drop(5).take(5)

    // 🔥 【SuggestionChip交互】使用AnimatedVisibility实现fadeOut动画
    AnimatedVisibility(
        visible = suggestionChipsVisible,
        exit = fadeOut(
            animationSpec = tween(
                durationMillis = 250,
                easing = FastOutSlowInEasing,
            ),
        ),
        modifier = modifier,
    ) {
        // 🎨 优化：简洁流畅的卡片展开动画
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 基础建议标签卡片
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.coachTheme.backgroundElevated, // 🔥 【主题统一】使用Coach主题卡片背景色
                ),
                shape = RoundedCornerShape(Tokens.Radius.Large),
                elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Small),
            ) {
                Column(
                    modifier = Modifier.padding(Tokens.Spacing.Large),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
                ) {
                    // 基础建议标签
                    SuggestionFlowRow(
                        suggestions = baseSuggestions,
                        onSuggestionClick = onSuggestionClick,
                        onHideSuggestionChips = onHideSuggestionChips,
                    )
                }
            }

            // "更多"按钮
            if (extendedSuggestions.isNotEmpty()) {
                MoreButton(
                    expanded = expanded,
                    onClick = { expanded = !expanded },
                )
            }

            // 🎯 优化的扩展动画：简单平滑的展开效果
            AnimatedVisibility(
                visible = expanded,
                enter = fadeIn(
                    animationSpec = tween(
                        durationMillis = 250,
                        easing = FastOutSlowInEasing,
                    ),
                ) + expandVertically(
                    animationSpec = tween(
                        durationMillis = 250,
                        easing = FastOutSlowInEasing,
                    ),
                ),
                exit = fadeOut(
                    animationSpec = tween(
                        durationMillis = 200,
                        easing = FastOutSlowInEasing,
                    ),
                ) + shrinkVertically(
                    animationSpec = tween(
                        durationMillis = 200,
                        easing = FastOutSlowInEasing,
                    ),
                ),
            ) {
                // 扩展建议标签卡片
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.coachTheme.backgroundElevated, // 🔥 【主题统一】使用Coach主题卡片背景色
                    ),
                    shape = RoundedCornerShape(Tokens.Radius.Large),
                    elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.Small),
                ) {
                    Column(
                        modifier = Modifier.padding(Tokens.Spacing.Large),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        SuggestionFlowRow(
                            suggestions = extendedSuggestions,
                            onSuggestionClick = onSuggestionClick,
                            onHideSuggestionChips = onHideSuggestionChips,
                        )
                    }
                }
            }
        }
    }
}

/**
 * 🎨 建议标签流式布局
 */
@Composable
private fun SuggestionFlowRow(
    suggestions: List<SuggestionItem>,
    onSuggestionClick: (String) -> Unit,
    onHideSuggestionChips: () -> Unit,
    modifier: Modifier = Modifier,
) {
    SimpleFlowRow(
        modifier = modifier,
        horizontalSpacing = Tokens.Spacing.Small + Tokens.Spacing.XSmall, // 12dp
        verticalSpacing = Tokens.Spacing.Small + Tokens.Spacing.XSmall, // 12dp
    ) {
        suggestions.forEach { suggestion ->
            SuggestionChip(
                text = suggestion.text,
                icon = suggestion.icon,
                onClick = {
                    // 🔥 【SuggestionChip交互】先隐藏建议标签，然后处理点击
                    onHideSuggestionChips()
                    onSuggestionClick(suggestion.prompt)
                },
            )
        }
    }
}

/**
 * 🎨 简单的FlowRow实现
 */
@Composable
private fun SimpleFlowRow(
    modifier: Modifier = Modifier,
    horizontalSpacing: Dp = 8.dp,
    verticalSpacing: Dp = 8.dp,
    content: @Composable () -> Unit,
) {
    Layout(
        content = content,
        modifier = modifier,
    ) { measurables, constraints ->
        val placeables = measurables.map { it.measure(constraints) }

        // 计算布局
        var currentRowMaxHeight = 0
        var totalHeight = 0
        var currentX = 0
        val positions = mutableListOf<Pair<Int, Int>>()

        for (placeable in placeables) {
            // 如果当前行放不下，换行
            if (currentX + placeable.width > constraints.maxWidth && currentX > 0) {
                totalHeight += currentRowMaxHeight + verticalSpacing.roundToPx()
                currentX = 0
                currentRowMaxHeight = 0
            }

            positions.add(Pair(currentX, totalHeight))
            currentX += placeable.width + horizontalSpacing.roundToPx()
            currentRowMaxHeight = maxOf(currentRowMaxHeight, placeable.height)
        }

        totalHeight += currentRowMaxHeight

        layout(constraints.maxWidth, totalHeight) {
            placeables.forEachIndexed { index, placeable ->
                val (x, y) = positions[index]
                placeable.placeRelative(x, y)
            }
        }
    }
}

/**
 * 🎨 建议芯片 - ChatGPT风格
 */
@Composable
private fun SuggestionChip(
    text: String,
    icon: ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape(Tokens.Spacing.Large - Tokens.Spacing.XSmall), // 20dp
        color = MaterialTheme.coachTheme.backgroundSecondary.copy(alpha = 0.7f), // 🔥 【主题统一】使用Coach主题次要背景色
        contentColor = MaterialTheme.coachTheme.textSecondary, // 🔥 【主题统一】使用Coach主题次要文本色
        shadowElevation = Tokens.Elevation.None,
    ) {
        Row(
            modifier = Modifier.padding(
                horizontal = Tokens.Spacing.Medium,
                vertical = Tokens.Spacing.Small + Tokens.Spacing.XSmall, // 12dp
            ),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier = Modifier.size(Tokens.Spacing.Medium + Tokens.Spacing.Tiny), // 18dp
                tint = MaterialTheme.coachTheme.accentPrimary.copy(alpha = 0.8f), // 🔥 【主题统一】使用Coach主题强调色
            )

            Text(
                text = text,
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.coachTheme.textPrimary, // 🔥 【主题统一】使用Coach主题主文本色
                fontSize = Tokens.Typography.Body,
            )
        }
    }
}

/**
 * 🎨 "更多"按钮
 */
@Composable
private fun MoreButton(
    expanded: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        onClick = onClick,
        modifier = modifier,
        shape = RoundedCornerShape(20.dp),
        color = MaterialTheme.coachTheme.accentPrimary.copy(alpha = 0.1f), // 🔥 【主题统一】使用Coach主题强调色
        contentColor = MaterialTheme.coachTheme.accentPrimary, // 🔥 【主题统一】使用Coach主题强调色
        shadowElevation = 0.dp,
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 20.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            Icon(
                imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                contentDescription = null,
                modifier = Modifier.size(18.dp),
            )

            Text(
                text = if (expanded) "收起" else "更多",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
            )
        }
    }
}

/**
 * 🔧 从配置构建建议项列表
 */
private fun buildSuggestionsFromConfig(config: SuggestionConfig): List<SuggestionItem> {
    val quickSuggestions = config.quickSuggestions.map { suggestion ->
        SuggestionItem(
            text = suggestion.title,
            icon = mapSuggestionToIcon(suggestion.title),
            prompt = suggestion.prompt,
        )
    }

    val extendedSuggestions = config.extendedSuggestions.values
        .flatten()
        .map { suggestion ->
            SuggestionItem(
                text = suggestion.title,
                icon = mapSuggestionToIcon(suggestion.title),
                prompt = suggestion.prompt,
            )
        }

    return quickSuggestions + extendedSuggestions
}

/**
 * 🔧 统一的关键字匹配逻辑 - 消除重复代码
 *
 * 用于替代ExpandedSuggestionPanel和ChatInputContainer中的重复逻辑
 */
internal object SuggestionMatcher {
    /**
     * 检查输入文本是否匹配建议配置中的关键字
     */
    fun findMatchingKeyword(
        inputText: String,
        suggestionConfig: SuggestionConfig?,
    ): String? {
        if (suggestionConfig == null || inputText.isBlank()) return null

        return suggestionConfig.extendedSuggestions.keys.find { keyword ->
            inputText.contains(keyword, ignoreCase = true)
        }
    }

    /**
     * 获取匹配关键字的建议列表
     */
    fun getMatchingSuggestions(
        inputText: String,
        suggestionConfig: SuggestionConfig?,
        maxCount: Int = 4,
    ): List<com.example.gymbro.shared.models.coach.ExtendedSuggestion> {
        val matchingKey = findMatchingKeyword(inputText, suggestionConfig)
        return if (matchingKey != null) {
            suggestionConfig?.extendedSuggestions?.get(matchingKey)?.take(maxCount) ?: emptyList()
        } else {
            emptyList()
        }
    }

    /**
     * 检查是否应该显示建议面板
     */
    fun shouldShowSuggestions(
        inputText: String,
        suggestionConfig: SuggestionConfig?,
    ): Boolean {
        return getMatchingSuggestions(inputText, suggestionConfig).isNotEmpty()
    }
}

/**
 * 🎨 智能映射建议标题到图标
 */
private fun mapSuggestionToIcon(title: String): ImageVector {
    return when {
        title.contains("创建图片", ignoreCase = true) ||
            title.contains("图片", ignoreCase = true) -> Icons.Default.Image

        title.contains("代码", ignoreCase = true) -> Icons.Default.Code

        title.contains("分析数据", ignoreCase = true) ||
            title.contains("数据", ignoreCase = true) -> Icons.Default.Analytics

        title.contains("总结", ignoreCase = true) -> Icons.Default.Description

        title.contains("分析图片", ignoreCase = true) -> Icons.Default.Visibility

        title.contains("减脂", ignoreCase = true) -> Icons.AutoMirrored.Filled.TrendingDown

        title.contains("有氧", ignoreCase = true) -> Icons.AutoMirrored.Filled.DirectionsRun

        title.contains("力量", ignoreCase = true) -> Icons.Default.FitnessCenter

        title.contains("瑜伽", ignoreCase = true) -> Icons.Default.SelfImprovement

        title.contains("饮食", ignoreCase = true) -> Icons.Default.Restaurant

        else -> Icons.AutoMirrored.Filled.Help
    }
}

// === 预览组件 ===

@GymBroPreview
@Composable
private fun WelcomeScreenPreview() {
    GymBroTheme {
        WelcomeScreen(
            suggestionConfig = null,
            suggestionChipsVisible = true,
            onSuggestionClick = {},
            onHideSuggestionChips = {},
        )
    }
}

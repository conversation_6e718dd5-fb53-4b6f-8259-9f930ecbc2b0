package com.example.gymbro.domain.profile.usecase

// StringResource已移除，使用UiText.DynamicString替代
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.NetworkTimeoutHandler
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.shared.base.modern.ModernUseCaseNoParams
import com.example.gymbro.domain.user.service.UserDataService
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 获取当前用户资料的用例
 *
 * 重构后使用 UserDataService 作为统一的用户数据管理接口。
 * 该用例封装了从 UserDataService 获取用户资料的业务逻辑，处理可能的错误并返回格式化的结果。
 *
 * 核心变更：
 * - 使用 UserDataService 接口替代 UserAggregateRepository
 * - 遵循 Clean Architecture 依赖倒置原则
 * - 确保数据的唯一真实来源（SSOT）
 *
 * @property userDataService 用户数据服务接口
 * @property authRepository 认证仓库，用于获取当前用户
 * @property timeoutHandler 网络超时处理器
 * @property dispatcher 协程调度器
 * @property logger 日志记录器
 */
@Singleton
class GetUserProfileUseCase
@Inject
constructor(
    private val userDataService: UserDataService,
    private val authRepository: AuthRepository,
    private val timeoutHandler: NetworkTimeoutHandler,
    @IoDispatcher dispatcher: CoroutineDispatcher,
    logger: Logger,
) : ModernUseCaseNoParams<UserProfile?>(dispatcher, logger) {
    /**
     * 执行获取用户资料的操作
     *
     * @return 包含用户资料的ModernResult，成功时包含UserProfile，失败时包含错误
     */
    override suspend fun execute(): ModernResult<UserProfile?> =
        timeoutHandler.executeWithNetworkTimeout("getUserProfile") {
            try {
                // 🔥 关键修复：直接使用 UserDataService 获取当前用户资料
                val profileResult = userDataService.getCurrentUserProfile()

                when (profileResult) {
                    is ModernResult.Success -> {
                        val profile = profileResult.data
                        if (profile != null) {
                            // 🔥 增强调试：验证获取的用户资料数据
                            println("🔥 GetUserProfileUseCase: 成功从 UserDataService 获取用户资料")
                            println("  - userId: ${profile.userId}")
                            println("  - displayName: ${profile.displayName}")
                            println("  - email: ${profile.email}")
                            println("  - gender: ${profile.gender}")
                            println("  - height: ${profile.height}")
                            println("  - weight: ${profile.weight}")
                            println("  - fitnessLevel: ${profile.fitnessLevel}")
                            println("  - fitnessGoals: ${profile.fitnessGoals}")
                        }
                        ModernResult.Success(profile)
                    }
                    is ModernResult.Error -> {
                        println("🔥 GetUserProfileUseCase: 获取用户资料失败: ${profileResult.error}")
                        ModernResult.Error(profileResult.error)
                    }
                    is ModernResult.Loading -> {
                        val error =
                            BusinessErrors.BusinessError.rule(
                                operationName = "getUserProfile",
                                message = UiText.DynamicString("用户数据加载中，请稍后重试"),
                                recoverable = true,
                            )
                        ModernResult.Error(error)
                    }
                }
            } catch (e: Exception) {
                // 处理可能的异常
                if (e is CancellationException) throw e
                val error =
                    BusinessErrors.BusinessError.rule(
                        operationName = "getUserProfile",
                        message = UiText.DynamicString("获取用户资料时发生异常: ${e.message}"),
                        recoverable = true,
                    )
                ModernResult.Error(error)
            }
        }
}

package com.example.gymbro.di.domain

import com.example.gymbro.data.workout.usecase.PlanProgressUseCaseImpl
import com.example.gymbro.domain.workout.repository.StatsRepository
import com.example.gymbro.domain.workout.usecase.PlanProgressUseCase
import com.example.gymbro.domain.workout.usecase.stats.CreateDailyStatsUseCase
import com.example.gymbro.domain.workout.usecase.stats.GetStatsUseCase
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * Workout UseCase 依赖注入模块
 *
 * 负责绑定Workout相关UseCase接口到实现
 * 遵循Clean Architecture和Hilt最佳实践
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class WorkoutUseCaseModule {

    /**
     * 绑定 PlanProgressUseCase 接口实现
     * 负责训练计划进度管理
     */
    @Binds
    @Singleton
    abstract fun bindPlanProgressUseCase(
        impl: PlanProgressUseCaseImpl,
    ): PlanProgressUseCase

    companion object {
        /**
         * 提供 GetStatsUseCase 实例
         * Stats统计数据查询用例
         */
        @Provides
        @Singleton
        fun provideGetStatsUseCase(
            statsRepository: StatsRepository,
            @IoDispatcher ioDispatcher: CoroutineDispatcher,
        ): GetStatsUseCase {
            return GetStatsUseCase(
                statsRepository = statsRepository,
                ioDispatcher = ioDispatcher,
            )
        }

        /**
         * 提供 CreateDailyStatsUseCase 实例
         * 日级统计数据创建用例
         */
        @Provides
        @Singleton
        fun provideCreateDailyStatsUseCase(
            statsRepository: StatsRepository,
            getWorkoutSessionsUseCase:
            com.example.gymbro.domain.workout.usecase.session.GetWorkoutSessionsUseCase,
            @IoDispatcher ioDispatcher: CoroutineDispatcher,
        ): CreateDailyStatsUseCase {
            return CreateDailyStatsUseCase(
                statsRepository = statsRepository,
                getWorkoutSessionsUseCase = getWorkoutSessionsUseCase,
                ioDispatcher = ioDispatcher,
            )
        }

        /**
         * 提供 IO调度器
         */
        @Provides
        @IoDispatcher
        fun provideIoDispatcher(): CoroutineDispatcher = Dispatchers.IO
    }

    // 其他workout相关UseCase绑定将在此处添加
    // 例如：TemplateManagementUseCase, SessionManagementUseCase等
}

/**
 * IO调度器限定符
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class IoDispatcher

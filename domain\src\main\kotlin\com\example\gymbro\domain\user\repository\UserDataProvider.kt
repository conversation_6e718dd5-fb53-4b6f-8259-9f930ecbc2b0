package com.example.gymbro.domain.user.repository

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.ai.UserAiContext
import kotlinx.coroutines.flow.Flow

/**
 * 用户数据提供者接口
 *
 * 遵循Clean Architecture原则，在domain层定义接口，由外层模块实现。
 * 这样避免了domain层对具体实现的依赖，保持了架构的纯净性。
 *
 * 主要用途：
 * - 为Coach模块的UseCase提供用户数据访问能力
 * - 避免domain层直接依赖core-user-data-center模块
 * - 保持依赖倒置原则
 */
interface UserDataProvider {

    /**
     * 获取当前用户的AI上下文数据
     *
     * 返回用于AI个性化的用户上下文信息，包括：
     * - 基础信息（姓名、性别等）
     * - 身体数据（身高、体重等）
     * - 健身偏好（目标、训练日、健身水平等）
     * - 统计数据（活动次数、活跃时间等）
     *
     * @return ModernResult<UserAiContext?> 用户AI上下文，可能为null（用户未登录）
     */
    suspend fun getCurrentUserAiContext(): ModernResult<UserAiContext?>

    /**
     * 观察用户AI上下文数据的变化
     *
     * 提供响应式的用户数据流，当用户数据发生变化时自动更新。
     * 适用于需要实时用户数据的场景。
     *
     * @return Flow<UserAiContext?> 用户AI上下文数据流
     */
    fun observeUserAiContext(): Flow<UserAiContext?>
}

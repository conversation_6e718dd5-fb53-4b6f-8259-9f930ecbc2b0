package com.example.gymbro.features.coach.aicoach.internal.monitoring

import android.os.SystemClock
import com.example.gymbro.domain.coach.model.PhaseKind
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import timber.log.Timber

/**
 * 🔍 AI对话监控系统 - 分层监控架构
 *
 * 监控层级：
 * 1. 会话层 - 整个对话会话的生命周期
 * 2. 消息层 - 单条消息的处理流程
 * 3. 流式层 - Token流的解析和状态变化
 * 4. 组件层 - ThinkingBox组件状态变化
 */
internal object AiConversationMonitor {

    // 会话追踪
    private var currentSessionId: String? = null
    private var sessionStartTime: Long = 0L
    private var messageCount: Int = 0

    // 消息追踪
    private var currentMessageId: String? = null
    private var messageStartTime: Long = 0L
    private var tokenCount: Int = 0

    // 流式追踪
    private var streamBuffer = StringBuilder()
    private var detectedPhases = mutableListOf<PhaseKind>()
    private var phaseTransitions = mutableListOf<PhaseTransition>()

    /**
     * 🔍 会话层监控 - 开始新会话
     */
    fun startSession(sessionId: String) {
        currentSessionId = sessionId
        sessionStartTime = SystemClock.elapsedRealtime()
        messageCount = 0

        Timber.i("🎯 [会话层] 开始新会话: sessionId=$sessionId")
    }

    /**
     * 🔍 会话层监控 - 结束会话
     */
    fun endSession() {
        val duration = SystemClock.elapsedRealtime() - sessionStartTime
        Timber.i("🎯 [会话层] 会话结束: sessionId=$currentSessionId, 持续时间=${duration}ms, 消息数=$messageCount")

        // 重置状态
        currentSessionId = null
        sessionStartTime = 0L
        messageCount = 0
    }

    /**
     * 🔍 消息层监控 - 开始处理新消息
     */
    fun startMessage(messageId: String, userInput: String) {
        currentMessageId = messageId
        messageStartTime = SystemClock.elapsedRealtime()
        tokenCount = 0
        messageCount++

        // 重置流式追踪状态
        streamBuffer.clear()
        detectedPhases.clear()
        phaseTransitions.clear()

        Timber.i("📝 [消息层] 开始处理消息: messageId=$messageId")
        Timber.d("📝 [消息层] 用户输入: '$userInput' (${userInput.length}字符)")
    }

    /**
     * 🔍 消息层监控 - 消息处理完成
     */
    fun endMessage(finalContent: String) {
        val duration = SystemClock.elapsedRealtime() - messageStartTime
        val finalLength = finalContent.length

        Timber.i("📝 [消息层] 消息处理完成: messageId=$currentMessageId")
        Timber.i("📝 [消息层] 统计: 持续时间=${duration}ms, tokens=$tokenCount, 最终长度=${finalLength}字符")
        Timber.i("📝 [消息层] 阶段转换: ${phaseTransitions.size}次 ${phaseTransitions.map { "${it.from}→${it.to}" }}")

        // 验证最终内容格式
        validateFinalContent(finalContent)

        // 重置状态
        currentMessageId = null
        messageStartTime = 0L
        tokenCount = 0
    }

    /**
     * 🔍 流式层监控 - 处理单个token
     */
    fun processToken(token: String, detectedPhase: PhaseKind? = null) {
        tokenCount++
        streamBuffer.append(token)

        // 🔥 蓝图修复：简化监控日志，避免与RawTokenBus重复
        if (tokenCount <= 5 || tokenCount % 100 == 0) {
            Timber.d("🌊 [监控] Token #$tokenCount (${token.length}字符)")
        }

        // 检测阶段标记
        if (token.contains("<phase:")) {
            val phaseMatch = Regex("<phase:(\\w+)>").find(token)
            if (phaseMatch != null) {
                val phaseName = phaseMatch.groupValues[1]
                Timber.i("🏷️ [流式层] 检测到阶段标记: <phase:$phaseName> (Token #$tokenCount)")
            }
        }

        // 🔥 【移除】<think>标签检测，避免重复处理
        // 标签检测现在由ThinkingBox模块统一处理

        // 记录阶段转换
        if (detectedPhase != null && (detectedPhases.isEmpty() || detectedPhases.last() != detectedPhase)) {
            val previousPhase = detectedPhases.lastOrNull()
            detectedPhases.add(detectedPhase)
            phaseTransitions.add(
                PhaseTransition(
                    from = previousPhase,
                    to = detectedPhase,
                    tokenIndex = tokenCount,
                    timestamp = SystemClock.elapsedRealtime(),
                ),
            )
            Timber.i("🔄 [流式层] 阶段转换: ${previousPhase ?: "null"} → $detectedPhase (Token #$tokenCount)")
        }
    }

    // 🔥 移除：ThinkingBox监控逻辑已剔除，由ThinkingBox模块独立处理

    /**
     * 🔍 系统提示词监控
     */
    fun monitorSystemPrompt(systemPrompt: String, model: String? = null) {
        val hash = systemPrompt.hashCode()
        Timber.i("🔧 [系统层] 系统提示词: hash=$hash, model=$model")
        Timber.d("🔧 [系统层] 提示词长度: ${systemPrompt.length}字符")

        // 检查关键指令
        val hasPhaseInstructions = systemPrompt.contains("<phase:")
        val hasThinkTags = systemPrompt.contains("<think>")
        val hasThinkingInstructions = systemPrompt.contains("思考") || systemPrompt.contains("thinking")

        Timber.i(
            "🔧 [系统层] 指令检查: phase指令=$hasPhaseInstructions, think标签=$hasThinkTags, 思考指令=$hasThinkingInstructions",
        )

        if (hasThinkTags) {
            Timber.i("🔧 [系统层] ℹ️ 系统提示词包含<think>标签，现已支持解析处理")
        }

        if (!hasPhaseInstructions) {
            Timber.w("🔧 [系统层] ⚠️ 系统提示词缺少<phase:XXX>指令")
        }

        // 打印关键部分（用于调试）
        val lines = systemPrompt.lines()
        val keyLines = lines.filter { line ->
            line.contains("phase", ignoreCase = true) ||
                line.contains("think", ignoreCase = true) ||
                line.contains("标记", ignoreCase = true)
        }
        if (keyLines.isNotEmpty()) {
            Timber.d("🔧 [系统层] 关键指令行:")
            keyLines.forEach { line ->
                Timber.d("🔧 [系统层]   - $line")
            }
        }
    }

    /**
     * 🔍 最终内容验证
     */
    private fun validateFinalContent(content: String) {
        val hasMarkdownStructure = content.startsWith("#") && content.contains("##")
        val hasUnfilteredTags = Regex("<[^>]+>").findAll(content).any()
        val contentLength = content.length

        Timber.i("📋 [验证层] 最终内容验证:")
        Timber.i("📋 [验证层]   - Markdown结构: $hasMarkdownStructure")
        Timber.i("📋 [验证层]   - 未过滤标签: $hasUnfilteredTags")
        Timber.i("📋 [验证层]   - 内容长度: ${contentLength}字符")

        if (hasUnfilteredTags) {
            val tags = Regex("<[^>]+>").findAll(content).map { it.value }.toList()
            Timber.w("📋 [验证层] ⚠️ 发现未过滤的标签: $tags")
        }

        if (contentLength < 10) {
            Timber.w("📋 [验证层] ⚠️ 内容过短，可能处理异常")
        }
    }

    /**
     * 🔍 创建监控装饰器 - 为Token流添加监控
     */
    fun <T> Flow<T>.withMonitoring(
        messageId: String,
        userInput: String,
    ): Flow<T> {
        return this
            .onStart {
                startMessage(messageId, userInput)
                Timber.d("🌊 [流式层] Token流开始")
            }
            .onEach { token ->
                if (token is String) {
                    processToken(token)
                }
            }
            .onCompletion { cause ->
                if (cause == null) {
                    Timber.d("🌊 [流式层] Token流正常结束")
                } else {
                    Timber.e("🌊 [流式层] Token流异常结束: ${cause.message}")
                }
            }
    }
}

/**
 * 阶段转换记录
 */
data class PhaseTransition(
    val from: PhaseKind?,
    val to: PhaseKind,
    val tokenIndex: Int,
    val timestamp: Long,
)

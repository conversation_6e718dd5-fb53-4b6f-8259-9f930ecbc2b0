package com.example.gymbro.data.workout.stats.di

import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Stats模块依赖注入配置
 *
 * 注意：为了避免重复绑定错误，Stats相关的依赖项已移至：
 * - StatsDatabase: di/data/DatabaseModule.kt
 * - DailyStatsDao: di/data/DatabaseModule.kt
 * - StatsRepository: di/data/repository/WorkoutRepositoryModule.kt
 *
 * 此模块保留用于未来可能的Stats模块特定配置
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class StatsModule {
    // 所有绑定已移至其他模块以避免重复绑定错误
    // 如需添加Stats模块特定的依赖项，请在此处添加
}

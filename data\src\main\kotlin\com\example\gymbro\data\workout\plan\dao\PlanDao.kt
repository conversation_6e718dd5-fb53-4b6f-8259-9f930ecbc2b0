package com.example.gymbro.data.workout.plan.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.data.workout.plan.entity.PlanEntity
import kotlinx.coroutines.flow.Flow

/**
 * 计划数据访问对象 - PlanDB DAO
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 提供训练计划管理功能
 */
@Dao
interface PlanDao {

    // ==================== 基础 CRUD ====================

    @Query("SELECT * FROM workout_plans WHERE id = :planId")
    suspend fun getPlanById(planId: String): PlanEntity?

    @Query("SELECT * FROM workout_plans WHERE id = :planId")
    fun observePlanById(planId: String): Flow<PlanEntity?>

    @Query("SELECT * FROM workout_plans WHERE userId = :userId ORDER BY updatedAt DESC")
    fun getPlansByUser(userId: String): Flow<List<PlanEntity>>

    @Query("SELECT * FROM workout_plans WHERE isPublic = 1 ORDER BY updatedAt DESC")
    fun getPublicPlans(): Flow<List<PlanEntity>>

    // ==================== ✅ Phase 2新增：数据迁移支持 ====================

    /**
     * 获取所有Plan用于数据迁移
     * Phase 2: Plan层TemplateVersion适配所需
     */
    @Query("SELECT * FROM workout_plans ORDER BY updatedAt DESC")
    suspend fun getAllPlans(): List<PlanEntity>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlan(plan: PlanEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlans(plans: List<PlanEntity>)

    @Update
    suspend fun updatePlan(plan: PlanEntity)

    @Query("DELETE FROM workout_plans WHERE id = :planId")
    suspend fun deletePlan(planId: String)

    // ==================== 特定查询 ====================

    @Query("SELECT * FROM workout_plans WHERE userId = :userId AND isFavorite = 1 ORDER BY updatedAt DESC")
    fun getFavoritePlans(userId: String): Flow<List<PlanEntity>>

    @Query("SELECT * FROM workout_plans WHERE userId = :userId AND isAIGenerated = 1 ORDER BY updatedAt DESC")
    fun getAIGeneratedPlans(userId: String): Flow<List<PlanEntity>>

    @Query("SELECT * FROM workout_plans WHERE isTemplate = 1 ORDER BY updatedAt DESC")
    fun getTemplatePlans(): Flow<List<PlanEntity>>

    @Query("SELECT * FROM workout_plans WHERE name LIKE '%' || :query || '%' ORDER BY name ASC")
    fun searchPlansByName(query: String): Flow<List<PlanEntity>>

    @Query(
        "SELECT * FROM workout_plans WHERE userId = :userId AND name LIKE '%' || :query || '%' ORDER BY name ASC",
    )
    fun searchUserPlansByName(userId: String, query: String): Flow<List<PlanEntity>>

    // ==================== 统计查询 ====================

    @Query("SELECT COUNT(*) FROM workout_plans WHERE userId = :userId")
    suspend fun getUserPlanCount(userId: String): Int

    @Query("SELECT COUNT(*) FROM workout_plans WHERE isPublic = 1")
    suspend fun getPublicPlanCount(): Int

    @Query("SELECT COUNT(*) FROM workout_plans WHERE userId = :userId AND isFavorite = 1")
    suspend fun getFavoritePlanCount(userId: String): Int

    @Query("SELECT COUNT(*) FROM workout_plans WHERE userId = :userId AND isAIGenerated = 1")
    suspend fun getAIGeneratedPlanCount(userId: String): Int

    @Query("SELECT COUNT(*) FROM workout_plans WHERE isTemplate = 1")
    suspend fun getTemplatePlanCount(): Int

    // ==================== 批量操作 ====================

    @Query("UPDATE workout_plans SET isFavorite = :isFavorite WHERE id = :planId")
    suspend fun updatePlanFavoriteStatus(planId: String, isFavorite: Boolean)

    @Query("UPDATE workout_plans SET isPublic = :isPublic WHERE id = :planId")
    suspend fun updatePlanPublicStatus(planId: String, isPublic: Boolean)

    @Query("UPDATE workout_plans SET isAIGenerated = :isAIGenerated WHERE id = :planId")
    suspend fun updatePlanAIGeneratedStatus(planId: String, isAIGenerated: Boolean)

    @Query("SELECT * FROM workout_plans WHERE updatedAt > :timestamp")
    suspend fun getPlansUpdatedAfter(timestamp: Long): List<PlanEntity>
}

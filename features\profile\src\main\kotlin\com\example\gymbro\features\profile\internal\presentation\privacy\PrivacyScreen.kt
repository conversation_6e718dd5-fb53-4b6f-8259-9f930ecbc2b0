package com.example.gymbro.features.profile.internal.presentation.privacy

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.hapticfeedback.HapticFeedbackType
import androidx.compose.ui.platform.LocalHapticFeedback
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.style.TextAlign
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.GymBroSettingsGroup
import com.example.gymbro.designSystem.components.base.scaffold.ProfileScaffold
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.profile.internal.presentation.base.PrivacyViewModelMVI
import com.example.gymbro.features.profile.internal.presentation.contract.PrivacyContract.Effect
import com.example.gymbro.features.profile.internal.presentation.contract.PrivacyContract.Intent
import com.example.gymbro.features.profile.internal.presentation.util.ProfileStrings
import kotlinx.coroutines.launch

/**
 * 隐私设置屏幕 - 使用ProfileScaffold统一布局
 * 采用MVI架构模式，遵循UI统一4.0规范
 */
@Composable
internal fun PrivacyScreen(
    viewModel: PrivacyViewModelMVI,
    onNavigateBack: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val uiState by viewModel.state.collectAsStateWithLifecycle()
    val hapticFeedback = LocalHapticFeedback.current
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()

    // State投射 - 为独立的开关组件创建单独的状态投射
    val isLoading: Boolean by remember { derivedStateOf { uiState.isLoading } }
    val hasError: Boolean by remember { derivedStateOf { uiState.error != null } }

    // 隐私设置相关的状态投射
    val isProfilePublic: Boolean by remember { derivedStateOf { uiState.isProfilePublic } }
    val isContactRecommendEnabled: Boolean by remember { derivedStateOf { uiState.isContactRecommendEnabled } }
    val isFriendsVisibilityEnabled: Boolean by remember { derivedStateOf { uiState.isFriendsVisibilityEnabled } }
    val showInNearbyUsers: Boolean by remember { derivedStateOf { uiState.showInNearbyUsers } }
    val showActivity: Boolean by remember { derivedStateOf { uiState.showActivity } }

    // 使用VM状态，避免配置更改时丢失
    val isExporting: Boolean by remember { derivedStateOf { uiState.isExporting } }
    val showClearCacheDialog: Boolean by remember { derivedStateOf { uiState.showClearCacheDialog } }
    val showExportDataDialog: Boolean by remember { derivedStateOf { uiState.showExportDataDialog } }

    // Effect处理 - 正确的MVI副作用处理方式
    LaunchedEffect(Unit) {
        viewModel.effects.collect { effect ->
            when (effect) {
                is Effect.ShowError -> {
                    scope.launch {
                        val message =
                            when (effect.message) {
                                is UiText.DynamicString -> effect.message.value
                                is UiText.StringResource -> "发生错误"
                                is UiText.Empty -> "发生未知错误"
                                is UiText.ErrorCode -> "系统错误: ${effect.message.errorCode.code}"
                            }
                        snackbarHostState.showSnackbar(message)
                    }
                }
                is Effect.ShowExportSuccess -> {
                    scope.launch {
                        snackbarHostState.showSnackbar("数据导出成功")
                    }
                }
                is Effect.HapticFeedback -> {
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.LongPress)
                }
                is Effect.ShowSuccess -> {
                    hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                }
                else -> {
                    // 其他Effect处理
                }
            }
        }
    }

    ProfileScaffold(
        title = "隐私设置",
        onNavigateBack = onNavigateBack,
        modifier = modifier,
        isLoading = isLoading,
        snackbarHostState = snackbarHostState,
    ) {
        Column(
            modifier =
            Modifier
                .fillMaxSize()
                .padding(horizontal = Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 性能优化: 拆分为独立的设置组件，减少重组范围

            // 个人资料隐私设置
            ProfilePrivacySettingsSection(
                isProfilePublic = isProfilePublic,
                isContactRecommendEnabled = isContactRecommendEnabled,
                isFriendsVisibilityEnabled = isFriendsVisibilityEnabled,
                hapticFeedback = hapticFeedback,
                onProfilePublicChange = { viewModel.dispatch(Intent.SetProfilePublic(it)) },
                onContactRecommendChange = { viewModel.dispatch(Intent.SetContactRecommend(it)) },
                onFriendsVisibilityChange = { viewModel.dispatch(Intent.SetFriendsVisibility(it)) },
            )

            // 位置和活动隐私设置
            LocationActivitySettingsSection(
                showInNearbyUsers = showInNearbyUsers,
                showActivity = showActivity,
                hapticFeedback = hapticFeedback,
                onUpdateLocationSettings = { enabled ->
                    viewModel.dispatch(Intent.UpdateLocationSettings(enabled))
                },
                onUpdateActivitySettings = { enabled ->
                    viewModel.dispatch(Intent.UpdateActivitySettings(enabled))
                },
            )

            // 数据管理设置
            DataManagementSection(
                isExporting = isExporting,
                onClearCache = { viewModel.dispatch(Intent.ShowClearCacheDialog) },
                onExportData = { viewModel.dispatch(Intent.ShowExportDataDialog) },
            )

            Spacer(modifier = Modifier.height(Tokens.Spacing.XLarge))

            // 底部版权信息
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = "隐私设置会实时保存",
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    style = MaterialTheme.typography.bodySmall,
                    textAlign = TextAlign.Center,
                )
                Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))
            }
        }
    }

    // 使用VM状态管理对话框，避免配置更改时丢失
    if (showClearCacheDialog) {
        AlertDialog(
            onDismissRequest = { viewModel.dispatch(Intent.DismissAllDialogs) },
            title = { Text("确认清除缓存") },
            text = {
                Text("此操作将清除所有本地缓存数据，包括未同步的训练记录和草稿。清除后无法恢复，是否继续？")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.dispatch(Intent.ConfirmClearCache)
                    },
                ) {
                    Text("确认")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { viewModel.dispatch(Intent.DismissAllDialogs) },
                ) {
                    Text("取消")
                }
            },
        )
    }

    if (showExportDataDialog) {
        AlertDialog(
            onDismissRequest = { viewModel.dispatch(Intent.DismissAllDialogs) },
            title = { Text("导出数据") },
            text = {
                Text("将导出您的所有个人数据，包括训练记录、设置和偏好。是否继续？")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        viewModel.dispatch(Intent.ExportData)
                    },
                ) {
                    Text("导出")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { viewModel.dispatch(Intent.DismissAllDialogs) },
                ) {
                    Text("取消")
                }
            },
        )
    }
}

/**
 * 🎯 性能优化: 个人资料隐私设置组件 - 独立重组域
 */
@Composable
private fun ProfilePrivacySettingsSection(
    isProfilePublic: Boolean,
    isContactRecommendEnabled: Boolean,
    isFriendsVisibilityEnabled: Boolean,
    hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback,
    // ⭐3 使用原子操作回调，避免竞态条件
    onProfilePublicChange: (Boolean) -> Unit,
    onContactRecommendChange: (Boolean) -> Unit,
    onFriendsVisibilityChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    GymBroSettingsGroup(modifier = modifier) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            Text(
                text = ProfileStrings.privacyProfileTitle.asString(),
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
            )

            // 公开个人资料
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = ProfileStrings.privacyProfilePublic.asString(),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                    )
                    Text(
                        text = ProfileStrings.privacyProfilePublicDesc.asString(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
                Switch(
                    checked = isProfilePublic,
                    onCheckedChange = { enabled ->
                        // 🎯 性能优化: 设置变更触觉反馈
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        // ⭐3 使用原子操作
                        onProfilePublicChange(enabled)
                    },
                )
            }

            HorizontalDivider()

            // 联系人推荐
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = ProfileStrings.privacyContactRecommend.asString(),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                    )
                    Text(
                        text = ProfileStrings.privacyContactRecommendDesc.asString(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
                Switch(
                    checked = isContactRecommendEnabled,
                    onCheckedChange = { enabled ->
                        // 🎯 性能优化: 设置变更触觉反馈
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        // ⭐3 使用原子操作
                        onContactRecommendChange(enabled)
                    },
                )
            }
        }
    }
}

/**
 * 🎯 性能优化: 位置和活动设置组件 - 独立重组域
 */
@Composable
private fun LocationActivitySettingsSection(
    showInNearbyUsers: Boolean,
    showActivity: Boolean,
    hapticFeedback: androidx.compose.ui.hapticfeedback.HapticFeedback,
    onUpdateLocationSettings: (Boolean) -> Unit,
    onUpdateActivitySettings: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    GymBroSettingsGroup(modifier = modifier) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            Text(
                text = ProfileStrings.privacyLocationTitle.asString(),
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
            )

            // 位置显示
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = ProfileStrings.privacyLocationNearby.asString(),
                        style = MaterialTheme.typography.bodyLarge,
                    )
                    Text(
                        text = ProfileStrings.privacyLocationNearbyDesc.asString(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
                Switch(
                    checked = showInNearbyUsers,
                    onCheckedChange = { enabled ->
                        // 🎯 性能优化: 设置变更触觉反馈
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        onUpdateLocationSettings(enabled)
                    },
                )
            }

            HorizontalDivider()

            // 公开训练统计
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = ProfileStrings.privacyActivityPublic.asString(),
                        style = MaterialTheme.typography.bodyLarge,
                    )
                    Text(
                        text = ProfileStrings.privacyActivityPublicDesc.asString(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
                Switch(
                    checked = showActivity,
                    onCheckedChange = { enabled ->
                        // 🎯 性能优化: 设置变更触觉反馈
                        hapticFeedback.performHapticFeedback(HapticFeedbackType.TextHandleMove)
                        onUpdateActivitySettings(enabled)
                    },
                )
            }
        }
    }
}

/**
 * 🎯 性能优化: 数据管理组件 - 独立重组域
 */
@Composable
private fun DataManagementSection(
    isExporting: Boolean,
    onClearCache: () -> Unit,
    onExportData: () -> Unit,
    modifier: Modifier = Modifier,
) {
    GymBroSettingsGroup(modifier = modifier) {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            Text(
                text = ProfileStrings.privacyDataTitle.asString(),
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
            )

            HorizontalDivider()

            // 清除缓存按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        role = Role.Button,
                        onClick = onClearCache,
                    ).padding(vertical = Tokens.Spacing.Small),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = ProfileStrings.privacyClearCache.asString(),
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                    )
                    Text(
                        text = "清除应用缓存数据，释放存储空间",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
                Icon(
                    imageVector = Icons.Default.ChevronRight,
                    contentDescription = "清除缓存",
                    tint = MaterialTheme.colorScheme.onSurfaceVariant,
                )
            }

            HorizontalDivider()

            // 导出数据按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable(
                        role = Role.Button,
                        onClick = onExportData,
                        enabled = !isExporting,
                    ).padding(vertical = Tokens.Spacing.Small),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = ProfileStrings.privacyExportData.asString(),
                        style = MaterialTheme.typography.bodyLarge,
                        color = if (isExporting) {
                            MaterialTheme.colorScheme.onSurfaceVariant
                        } else {
                            MaterialTheme.colorScheme.onSurface
                        },
                    )
                    Text(
                        text = if (isExporting) "正在导出数据..." else "导出您的个人数据和训练记录",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }

                if (isExporting) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(Tokens.Icon.Standard),
                        strokeWidth = Tokens.Elevation.XSmall,
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.ChevronRight,
                        contentDescription = "导出数据",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                    )
                }
            }

            // 清除缓存
            OutlinedButton(
                onClick = onClearCache,
                modifier = Modifier.fillMaxWidth(),
            ) {
                Icon(
                    imageVector = Icons.Default.CleaningServices,
                    contentDescription = null,
                    modifier = Modifier.size(Tokens.Icon.Small),
                )
                Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
                Text(ProfileStrings.privacyClearCache.asString())
            }

            // 导出数据
            OutlinedButton(
                onClick = onExportData,
                modifier = Modifier.fillMaxWidth(),
                enabled = !isExporting,
            ) {
                if (isExporting) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(Tokens.Icon.Small),
                        strokeWidth = Tokens.Elevation.XSmall,
                    )
                } else {
                    Icon(
                        imageVector = Icons.Default.Download,
                        contentDescription = null,
                        modifier = Modifier.size(Tokens.Icon.Small),
                    )
                }
                Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
                Text(
                    if (isExporting) {
                        "导出中..."
                    } else {
                        ProfileStrings.privacyExportData.asString()
                    },
                )
            }
        }
    }
}

@Composable
@GymBroPreview
internal fun PrivacyScreenPreview() {
    GymBroTheme {
        Column(
            modifier =
            Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
        ) {
            Text(
                text = "隐私设置预览",
                style = MaterialTheme.typography.headlineMedium,
            )

            Spacer(modifier = Modifier.height(Tokens.Spacing.Medium))

            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = Tokens.Elevation.XSmall),
            ) {
                Column(
                    modifier = Modifier.padding(Tokens.Spacing.Medium),
                    verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
                ) {
                    Text(
                        text = "位置隐私",
                        style = MaterialTheme.typography.titleMedium,
                    )

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        Text("隐藏我的位置")
                        Switch(checked = false, onCheckedChange = {})
                    }
                }
            }
        }
    }
}

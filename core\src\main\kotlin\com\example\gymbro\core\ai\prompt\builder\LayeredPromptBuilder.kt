package com.example.gymbro.core.ai.prompt.builder

import com.example.gymbro.core.ai.prompt.memory.MemoryContext
import com.example.gymbro.core.ai.prompt.memory.MemoryContextBuilder
import com.example.gymbro.core.ai.prompt.memory.MemoryContextType
import com.example.gymbro.core.ai.prompt.memory.MemoryIntegrator
import com.example.gymbro.core.ai.prompt.model.AiContextData
import com.example.gymbro.core.ai.prompt.registry.PromptRegistry
import com.example.gymbro.core.ai.prompt.structure.SystemLayer
import com.example.gymbro.core.ai.tokenizer.ModelTypes
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.serialization.Serializable
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Core层的ChatMessage定义
 * 仅存于Core层，避免与UI/网络层耦合
 */
@Serializable
data class CoreChatMessage(
    val role: String,
    val content: String,
)

/**
 * 分层Prompt构建器 - 统一智能构建器
 *
 * 负责将各种数据源整合成标准的ChatMessage列表：
 * - System: 系统提示词（支持4种模式切换）
 * - Tool: Memory记忆、用户档案、训练数据等
 * - Conversation: 对话历史
 * - User: 用户当前输入
 *
 * 🔥 修复：遵循Clean Architecture，通过参数传入userId而不是内部获取
 * 🆕 支持 DeepSeek 模型专用提示词
 *
 * @since 618重构 - 完全重写，集成Memory系统
 */
@Singleton
class LayeredPromptBuilder
@Inject
constructor(
    private val tokenizer: TokenizerService,
    private val promptRegistry: PromptRegistry,
    private val memoryIntegrator: MemoryIntegrator,
    private val memoryContextBuilder: MemoryContextBuilder,
) : PromptBuilder {
    companion object {
        private const val DEFAULT_TOKEN_BUDGET = 3000
        private const val MEMORY_TOKEN_BUDGET = 1000
        private const val FALLBACK_USER_ID = "anonymous_user" // 🔥 fallback用户ID
        private const val PROMPT_CHECK_INTERVAL_MS = 30_000L // 🔥 新增：30秒检查一次
        private const val MIN_VALID_PROMPT_LENGTH = 500 // 🔥 新增：最小有效提示词长度

        // 🔥 新增：RAW内容输出控制开关
        private const val ENABLE_RAW_PROMPT_LOGGING = true // 设为false可关闭RAW输出
    }

    // 🔥 新增：提示词变更检测机制
    private var lastPromptCheckTime = 0L
    private var lastPromptHash = 0
    private var consecutiveFailures = 0

    /**
     * 实现PromptBuilder接口 - 标准方法
     */
    override suspend fun buildChatMessages(
        systemLayer: SystemLayer?,
        userInput: String,
        history: List<ConversationTurn>,
        model: String?, // 🆕 新增模型参数
    ): List<CoreChatMessage> {
        // 🔥 新增：追踪接口方法调用
        Timber.tag("PROMPT-BUILDER").e("🚀 [ENTRY] LayeredPromptBuilder.buildChatMessages(接口方法) 被调用")
        Timber.tag("PROMPT-BUILDER").e("🚀 [ENTRY] 接口参数: model=$model")

        return buildChatMessages(
            systemLayer = systemLayer,
            userInput = userInput,
            history = history,
            userId = null,
            forceOmitSystemPrompt = false,
            model = model, // 🆕 传递模型参数
        )
    }

    /**
     * 构建ChatMessage列表 - 增强版本，支持更多参数
     *
     * @param systemLayer 系统层配置（可选，null时使用当前模式）
     * @param userInput 用户输入
     * @param history 对话历史
     * @param userId 用户ID（由调用者提供）
     * @param forceOmitSystemPrompt 强制忽略系统提示词
     * @param model 模型名称（用于统一提示词系统）🆕
     * @return ChatMessage列表
     *
     * @since 618重构
     */
    suspend fun buildChatMessages(
        systemLayer: SystemLayer?,
        userInput: String,
        history: List<ConversationTurn>,
        userId: String? = null,
        forceOmitSystemPrompt: Boolean = false,
        model: String? = null, // 🆕 新增模型参数
    ): List<CoreChatMessage> {
        // 🔥 新增：追踪重载方法调用
        Timber.tag("PROMPT-BUILDER").e("🚀 [ENTRY] LayeredPromptBuilder.buildChatMessages(6参数) 被调用")
        Timber
            .tag("PROMPT-BUILDER")
            .e("🚀 [ENTRY] 参数: userId=$userId, forceOmit=$forceOmitSystemPrompt, model=$model")

        // 使用传入的userId或fallback
        val effectiveUserId = userId.takeIf { !it.isNullOrBlank() } ?: FALLBACK_USER_ID

        // 构建Memory上下文
        val memoryContext =
            memoryContextBuilder.buildContext(
                userId = effectiveUserId,
                userInput = userInput,
                history = history,
            )

        // 委托给增强版方法
        return buildChatMessagesWithMemory(
            systemLayer = systemLayer,
            userInput = userInput,
            history = history,
            memoryContext = memoryContext,
            forceOmitSystemPrompt = forceOmitSystemPrompt,
            model = model, // 🆕 传递模型参数
        )
    }

    /**
     * 构建带Memory集成的ChatMessage列表
     *
     * @param systemLayer 系统层配置
     * @param userInput 用户输入
     * @param history 对话历史
     * @param memoryContext Memory上下文
     * @param tokenBudget 总token预算
     * @param forceOmitSystemPrompt 强制忽略系统提示词
     * @param model 模型名称（用于统一提示词系统）🆕
     * @return ChatMessage列表
     *
     * @since 618重构
     */
    suspend fun buildChatMessagesWithMemory(
        systemLayer: SystemLayer? = null,
        userInput: String,
        history: List<ConversationTurn>,
        memoryContext: MemoryContext? = null,
        tokenBudget: Int = DEFAULT_TOKEN_BUDGET,
        forceOmitSystemPrompt: Boolean = false,
        model: String? = null, // 🆕 新增模型参数
    ): List<CoreChatMessage> =
        buildList {
            Timber
                .tag(
                    "PROMPT-BUILDER",
                ).e("🔧 开始构建消息，Memory=${memoryContext != null}, Model=${model ?: "default"}")

            // 🔥 新增：强制输出当前PromptRegistry状态
            Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] PromptRegistry状态检查开始...")
            val currentMode = promptRegistry.getCurrentMode()
            val registryStatus = promptRegistry.getLoadedConfigsStatus()
            Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] 当前模式: $currentMode")
            Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] Registry状态: $registryStatus")

            // 1. System消息 - 根据forceOmitSystemPrompt条件性添加
            if (!forceOmitSystemPrompt) {
                // 🔥 新增：主动检测提示词变更
                checkAndRefreshPromptIfNeeded()

                // 🔥 强制修复：直接使用PromptRegistry，确保JSON配置是唯一来源
                Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] 准备获取系统提示词...")

                // 🔥 新增：强制清除缓存，确保获取最新版本
                promptRegistry.clearAllCaches()
                Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] 已强制清除PromptRegistry缓存")

                val systemPrompt = promptRegistry.getSystemPrompt()
                Timber.tag("PROMPT-BUILDER").e("🔍 [DEBUG] 系统提示词获取完成，长度: ${systemPrompt.length}")

                // 🔥 立即输出系统提示词RAW内容
                logSystemPromptRaw(systemPrompt)

                // 🔥 调试日志：加强状态检查
                val registryStatus = promptRegistry.getLoadedConfigsStatus()
                Timber.tag("PROMPT-BUILDER").e("🔍 使用PromptRegistry配置:")
                Timber.tag("PROMPT-BUILDER").e("📊 Registry状态: $registryStatus")
                Timber.tag("PROMPT-BUILDER").e("🎯 当前配置ID: ${promptRegistry.getCurrentMode()}")
                Timber.tag("PROMPT-BUILDER").e("📄 系统提示词长度: ${systemPrompt.length}字符")
                Timber.tag("PROMPT-BUILDER").e("📄 提示词前200字符: ${systemPrompt.take(200)}")
                Timber.tag("PROMPT-BUILDER").e("📄 提示词哈希值: ${systemPrompt.hashCode()}")

                // 🔥 新增：如果检测到可能的问题，执行全面诊断和修复
                if (systemPrompt.length < 500) { // 正常的standard.json应该有几千字符
                    Timber
                        .tag("PROMPT-BUILDER")
                        .w("⚠️ 系统提示词异常短(${systemPrompt.length}字符)，可能使用了fallback配置")

                    // 第一步：检查版本一致性并更新
                    Timber.i("🔄 第一步：检查并更新配置文件版本...")
                    try {
                        val updateResult = promptRegistry.checkAndUpdateAllConfigs()
                        Timber.i("🔄 版本检查结果:\n$updateResult")

                        // 检查是否有更新
                        if (updateResult.contains("更新文件数: 0")) {
                            Timber.d("📄 配置文件版本已是最新")
                        } else {
                            Timber.i("📄 有配置文件被更新，重新获取系统提示词")
                            val updatedSystemPrompt = promptRegistry.getSystemPrompt()
                            Timber.i("📄 更新后系统提示词长度: ${updatedSystemPrompt.length}字符")

                            if (updatedSystemPrompt.length > systemPrompt.length && updatedSystemPrompt.length > 500) {
                                Timber.i("✅ 版本更新成功，使用最新的系统提示词")
                                add(CoreChatMessage("system", updatedSystemPrompt))
                                return@buildList
                            }
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "❌ 版本检查失败: ${e.message}")
                    }

                    // 第二步：尝试强制重新加载
                    Timber.i("🔄 第二步：尝试强制重新加载standard配置...")
                    try {
                        val reloadedConfig = promptRegistry.reloadConfig("standard")
                        val newSystemPrompt = reloadedConfig.systemPrompt
                        Timber.i("📄 重新加载后长度: ${newSystemPrompt.length}字符")
                        if (newSystemPrompt.length > systemPrompt.length && newSystemPrompt.length > 500) {
                            Timber.i("✅ 重新加载成功，使用新的系统提示词")
                            add(CoreChatMessage("system", newSystemPrompt))
                            return@buildList
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "❌ 重新加载失败: ${e.message}")
                    }

                    // 第三步：强制同步assets文件
                    Timber.w("🔄 第三步：强制同步assets文件...")
                    try {
                        val syncResult = promptRegistry.forceSyncAllAssets()
                        Timber.i("🔄 同步结果:\n$syncResult")

                        // 重新获取系统提示词
                        val finalSystemPrompt = promptRegistry.getSystemPrompt()
                        Timber.i("📄 同步后系统提示词长度: ${finalSystemPrompt.length}字符")

                        if (finalSystemPrompt.length > 500) {
                            Timber.i("✅ 同步成功，使用修复后的系统提示词")
                            add(CoreChatMessage("system", finalSystemPrompt))
                            return@buildList
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "❌ 强制同步失败: ${e.message}")
                    }

                    Timber.w("⚠️ 所有自动修复尝试均未成功，继续使用当前提示词")
                }

                // 验证系统提示词
                validateSystemPrompt(systemPrompt)

                add(CoreChatMessage("system", systemPrompt))
            }

            // 2. Memory消息 - 集成4层记忆系统
            if (memoryContext != null) {
                val memoryMessages =
                    memoryIntegrator.buildMemoryMessages(
                        context = memoryContext,
                        tokenBudget = MEMORY_TOKEN_BUDGET,
                    )
                addAll(memoryMessages)
                Timber.d("LayeredPromptBuilder: 添加${memoryMessages.size}条Memory消息")
            }

            // 3. 对话历史
            history.forEach { turn ->
                add(CoreChatMessage("user", turn.user))
                add(CoreChatMessage("assistant", turn.assistant))
            }

            // 4. 用户输入
            add(CoreChatMessage("user", userInput))

            // 5. 验证消息结构
            if (!forceOmitSystemPrompt) {
                validateMessages(this)
            }

            Timber.tag("PROMPT-BUILDER").e("✅ 消息构建完成，总数=${this.size}条消息")

            // 🔥 输出构建的消息摘要
            this.forEachIndexed { index, message ->
                val preview = message.content.take(100).replace("\n", "\\n")
                Timber.tag("PROMPT-BUILDER").e("消息[$index] ${message.role}: $preview...")
            }

            // 🔥 新增：输出完整的RAW内容用于调试
            logCompletePromptRaw(this)

            // 🔥 额外：简化版RAW输出，确保可见性
            logSimpleRawPrompt(this)
        }

    /**
     * 从AiContextData构建消息（兼容旧接口）
     *
     * @param userId 用户ID（由调用者提供）
     * @since 618重构
     */
    suspend fun buildMessagesFromContext(
        ctx: AiContextData,
        userInput: String,
        userId: String? = null,
        tokenBudget: Int = DEFAULT_TOKEN_BUDGET,
    ): List<CoreChatMessage> {
        // 使用传入的userId
        val effectiveUserId = userId.takeIf { !it.isNullOrBlank() } ?: FALLBACK_USER_ID

        // 从AiContextData提取Memory上下文
        val memoryContext = extractMemoryContext(ctx, userInput, effectiveUserId)

        // 转换对话历史格式
        val history =
            ctx.recentHistory
                .map { message ->
                    // 简化处理：将连续的消息配对为对话轮次
                    ConversationTurn(
                        user = message.content,
                        assistant = "已处理", // 临时占位符，实际应该从历史中提取
                    )
                }.take(5) // 限制历史数量

        return buildChatMessagesWithMemory(
            userInput = userInput,
            history = history,
            memoryContext = memoryContext,
            tokenBudget = tokenBudget,
        )
    }

    /**
     * 估算Token数量
     */
    override fun estimateTokens(prompt: String): Int = tokenizer.countTokens(prompt, ModelTypes.GPT_4)

    /**
     * 获取当前提示词模式
     *
     * @since 618重构
     */
    fun getCurrentMode(): String = promptRegistry.currentId.value

    /**
     * 切换提示词模式
     *
     * @since 618重构
     */
    fun switchMode(mode: String) {
        promptRegistry.switch(mode)
        Timber.d("LayeredPromptBuilder: 切换到模式 $mode")
    }

    // =============== 私有辅助方法 ===============

    /**
     * 🔥 新增：输出完整的RAW Prompt内容用于调试
     *
     * 这个方法会将所有消息按照发送给AI的格式输出，
     * 便于调试ThinkingBox和其他AI相关问题
     */
    private fun logCompletePromptRaw(messages: List<CoreChatMessage>) {
        // 检查是否启用RAW输出
        if (!ENABLE_RAW_PROMPT_LOGGING) {
            Timber.tag("PROMPT-RAW").d("RAW Prompt输出已禁用")
            return
        }

        try {
            Timber.tag("PROMPT-RAW").e("🚀 ========== 完整RAW Prompt开始 ==========")

            // 输出消息统计
            val systemCount = messages.count { it.role == "system" }
            val userCount = messages.count { it.role == "user" }
            val assistantCount = messages.count { it.role == "assistant" }
            val toolCount = messages.count { it.role == "tool" }

            Timber
                .tag("PROMPT-RAW")
                .e(
                    "📊 消息统计: system=$systemCount, user=$userCount, assistant=$assistantCount, tool=$toolCount",
                )

            // 🔥 强制输出：使用多个标签确保可见性
            messages.forEachIndexed { index, message ->
                val roleTag =
                    when (message.role) {
                        "system" -> "🔧 SYSTEM"
                        "user" -> "👤 USER"
                        "assistant" -> "🤖 ASSISTANT"
                        "tool" -> "🛠️ TOOL"
                        else -> "❓ ${message.role.uppercase()}"
                    }

                // 使用多个日志标签确保可见性
                Timber.tag("PROMPT-RAW").e("$roleTag [$index] ==================")
                Timber.tag("PROMPT-BUILDER").e("$roleTag [$index] ==================")
                Timber.e("$roleTag [$index] ==================")

                // 强制输出内容，使用更小的块避免截断
                val content = message.content
                val chunks = content.chunked(500) // 减小到500字符避免截断

                if (chunks.size == 1) {
                    // 内容较短，多标签输出
                    Timber.tag("PROMPT-RAW").e(content)
                    Timber.tag("PROMPT-BUILDER").e("RAW内容: $content")
                    Timber.e("RAW内容: $content")
                } else {
                    // 内容较长，分块输出
                    Timber.tag("PROMPT-RAW").e("📄 内容长度: ${content.length}字符，分为${chunks.size}块")
                    Timber.tag("PROMPT-BUILDER").e("📄 内容长度: ${content.length}字符，分为${chunks.size}块")

                    chunks.forEachIndexed { chunkIndex, chunk ->
                        val chunkInfo = "📄 第${chunkIndex + 1}/${chunks.size}块"
                        Timber.tag("PROMPT-RAW").e("$chunkInfo: $chunk")
                        Timber.tag("PROMPT-BUILDER").e("$chunkInfo: $chunk")
                        Timber.e("$chunkInfo: $chunk")
                    }
                }

                Timber.tag("PROMPT-RAW").e("") // 空行分隔
                Timber.tag("PROMPT-BUILDER").e("") // 空行分隔
            }

            // 输出总体统计
            val totalLength = messages.sumOf { it.content.length }
            val estimatedTokens =
                estimateTokens(messages.joinToString("\n") { "${it.role}: ${it.content}" })

            Timber.tag("PROMPT-RAW").e("📊 总体统计:")
            Timber.tag("PROMPT-RAW").e("📊 - 总消息数: ${messages.size}")
            Timber.tag("PROMPT-RAW").e("📊 - 总字符数: $totalLength")
            Timber.tag("PROMPT-RAW").e("📊 - 估算Token数: $estimatedTokens")

            Timber.tag("PROMPT-RAW").e("🏁 ========== 完整RAW Prompt结束 ==========")

            // 🔥 额外输出：JSON格式，便于复制到其他工具测试
            logPromptAsJson(messages)
        } catch (e: Exception) {
            Timber.tag("PROMPT-RAW").e(e, "❌ 输出RAW Prompt失败")
        }
    }

    /**
     * 🔥 新增：输出JSON格式的Prompt，便于复制到其他工具测试
     */
    private fun logPromptAsJson(messages: List<CoreChatMessage>) {
        try {
            Timber.tag("PROMPT-JSON").e("🔧 ========== JSON格式Prompt开始 ==========")

            // 构建JSON格式的消息数组
            val jsonMessages =
                messages.map { message ->
                    """{"role": "${message.role}", "content": ${escapeJsonString(message.content)}}"""
                }

            val jsonArray = "[\n  ${jsonMessages.joinToString(",\n  ")}\n]"

            // 分块输出JSON（避免单条日志过长）
            val jsonChunks = jsonArray.chunked(2000)
            jsonChunks.forEachIndexed { index, chunk ->
                Timber.tag("PROMPT-JSON").e("JSON第${index + 1}/${jsonChunks.size}块: $chunk")
            }

            Timber.tag("PROMPT-JSON").e("🔧 ========== JSON格式Prompt结束 ==========")
        } catch (e: Exception) {
            Timber.tag("PROMPT-JSON").e(e, "❌ 输出JSON格式失败")
        }
    }

    /**
     * 🔥 新增：立即输出系统提示词RAW内容
     */
    private fun logSystemPromptRaw(systemPrompt: String) {
        if (!ENABLE_RAW_PROMPT_LOGGING) return

        try {
            println("🔧🔧🔧 ========== 系统提示词RAW内容 ==========")
            Timber.e("🔧🔧🔧 ========== 系统提示词RAW内容 ==========")

            println("长度: ${systemPrompt.length}字符")
            Timber.e("长度: ${systemPrompt.length}字符")

            // 分段输出完整系统提示词
            val segments = systemPrompt.chunked(400)
            segments.forEachIndexed { index, segment ->
                val header = "系统提示词段落${index + 1}/${segments.size}:"
                println("$header")
                Timber.e("$header")
                println(segment)
                Timber.e(segment)
                println("---")
                Timber.e("---")
            }

            println("🔧🔧🔧 ========== 系统提示词RAW结束 ==========")
            Timber.e("🔧🔧🔧 ========== 系统提示词RAW结束 ==========")
        } catch (e: Exception) {
            println("❌ 系统提示词RAW输出失败: ${e.message}")
            Timber.e(e, "❌ 系统提示词RAW输出失败")
        }
    }

    /**
     * 🔥 新增：简化版RAW输出，确保可见性
     */
    private fun logSimpleRawPrompt(messages: List<CoreChatMessage>) {
        if (!ENABLE_RAW_PROMPT_LOGGING) return

        try {
            // 使用最基本的日志输出，确保可见
            println("🚀🚀🚀 ========== 简化RAW Prompt开始 ==========")
            Timber.e("🚀🚀🚀 ========== 简化RAW Prompt开始 ==========")

            messages.forEachIndexed { index, message ->
                val header = "【${message.role.uppercase()}消息-$index】"
                println("$header")
                Timber.e("$header")

                // 直接输出前200字符，避免截断
                val preview = message.content.take(200)
                println("内容预览: $preview")
                Timber.e("内容预览: $preview")

                // 如果内容较长，输出完整长度信息
                if (message.content.length > 200) {
                    val lengthInfo = "完整长度: ${message.content.length}字符"
                    println(lengthInfo)
                    Timber.e(lengthInfo)

                    // 分段输出完整内容
                    val segments = message.content.chunked(300)
                    segments.forEachIndexed { segIndex, segment ->
                        val segmentHeader = "段落${segIndex + 1}/${segments.size}:"
                        println("$segmentHeader $segment")
                        Timber.e("$segmentHeader $segment")
                    }
                }

                println("---")
                Timber.e("---")
            }

            println("🚀🚀🚀 ========== 简化RAW Prompt结束 ==========")
            Timber.e("🚀🚀🚀 ========== 简化RAW Prompt结束 ==========")
        } catch (e: Exception) {
            println("❌ 简化RAW输出失败: ${e.message}")
            Timber.e(e, "❌ 简化RAW输出失败")
        }
    }

    /**
     * 转义JSON字符串
     */
    private fun escapeJsonString(str: String): String =
        "\"" +
            str
                .replace("\\", "\\\\")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t") + "\""

    /**
     * 🔥 新增：主动检测提示词变更并刷新
     *
     * 检测机制：
     * 1. 定时检查（30秒间隔）
     * 2. 内容哈希比较
     * 3. 文件长度验证
     * 4. 强制刷新机制
     */
    private suspend fun checkAndRefreshPromptIfNeeded() {
        val currentTime = System.currentTimeMillis()

        // 检查是否需要定时检查
        val shouldCheck = (currentTime - lastPromptCheckTime) > PROMPT_CHECK_INTERVAL_MS

        if (shouldCheck) {
            Timber.tag("PROMPT-BUILDER").i("🔍 开始定时检查提示词变更...")

            try {
                // 获取当前提示词
                val currentPrompt = promptRegistry.getSystemPrompt()
                val currentHash = currentPrompt.hashCode()

                // 检查长度是否异常
                val isLengthValid = currentPrompt.length >= MIN_VALID_PROMPT_LENGTH

                // 检查哈希是否变化
                val isHashChanged = lastPromptHash != 0 && lastPromptHash != currentHash

                Timber
                    .tag("PROMPT-BUILDER")
                    .i("🔍 检查结果: 长度=${currentPrompt.length}, 哈希=$currentHash, 上次哈希=$lastPromptHash")

                // 如果长度异常或者是第一次检查，尝试强制刷新
                if (!isLengthValid || lastPromptHash == 0) {
                    Timber
                        .tag("PROMPT-BUILDER")
                        .w("⚠️ 提示词异常，尝试强制刷新: 长度=${currentPrompt.length}, 最小要求=$MIN_VALID_PROMPT_LENGTH")

                    // 执行强制刷新
                    val refreshResult = forceRefreshPrompt()

                    if (refreshResult) {
                        consecutiveFailures = 0
                        Timber.tag("PROMPT-BUILDER").i("✅ 强制刷新成功")
                    } else {
                        consecutiveFailures++
                        Timber.tag("PROMPT-BUILDER").e("❌ 强制刷新失败，连续失败次数: $consecutiveFailures")
                    }
                } else if (isHashChanged) {
                    Timber
                        .tag("PROMPT-BUILDER")
                        .i("🔄 检测到提示词内容变更，哈希: $lastPromptHash → $currentHash")
                    consecutiveFailures = 0
                }

                // 更新检查状态
                lastPromptCheckTime = currentTime
                lastPromptHash = currentHash
            } catch (e: Exception) {
                consecutiveFailures++
                Timber.tag("PROMPT-BUILDER").e(e, "❌ 检查提示词变更失败，连续失败次数: $consecutiveFailures")
            }
        }
    }

    /**
     * 🔥 【并发控制修复】强制刷新提示词
     *
     * @return 是否刷新成功
     */
    private suspend fun forceRefreshPrompt(): Boolean =
        try {
            Timber.tag("PROMPT-BUILDER").i("🔄 开始强制刷新提示词...")

            // 第一步：检查并更新所有配置文件
            val updateResult = promptRegistry.checkAndUpdateAllConfigs()
            Timber.tag("PROMPT-BUILDER").i("📄 配置文件检查结果:\n$updateResult")

            // 第二步：如果没有更新，尝试强制同步
            if (updateResult.contains("更新文件数: 0")) {
                Timber.tag("PROMPT-BUILDER").i("🔄 配置文件无更新，尝试强制同步...")
                val syncResult = promptRegistry.forceSyncAllAssets()
                Timber.tag("PROMPT-BUILDER").i("📄 强制同步结果:\n$syncResult")
            }

            // 第三步：重新加载当前配置
            val currentMode = promptRegistry.getCurrentMode()
            val refreshedConfig = promptRegistry.reloadConfig(currentMode)

            // 第四步：验证刷新结果
            val newPrompt = refreshedConfig.systemPrompt
            val isValid = newPrompt.length >= MIN_VALID_PROMPT_LENGTH

            if (isValid) {
                Timber
                    .tag("PROMPT-BUILDER")
                    .i("✅ 强制刷新成功: 新长度=${newPrompt.length}, 配置=${refreshedConfig.displayName}")
                true
            } else {
                Timber.tag("PROMPT-BUILDER").w("⚠️ 刷新后提示词仍然异常: 长度=${newPrompt.length}")
                false
            }
        } catch (e: Exception) {
            Timber.tag("PROMPT-BUILDER").e(e, "❌ 强制刷新提示词失败")
            false
        }

    /**
     * 从AiContextData提取Memory上下文
     */
    private fun extractMemoryContext(
        ctx: AiContextData,
        userInput: String,
        userId: String, // 使用传入的userId
    ): MemoryContext? {
        // 判断上下文类型
        val contextType =
            when {
                ctx.relevantTemplates.isNotEmpty() -> MemoryContextType.TRAINING
                ctx.userProfile != null -> MemoryContextType.PROFILE
                ctx.recentHistory.isNotEmpty() -> MemoryContextType.CONVERSATION
                else -> MemoryContextType.GENERAL
            }

        // 如果是简单问候，不需要Memory
        if (isSimpleGreeting(userInput)) {
            return null
        }

        return MemoryContext(
            userId = userId, // 使用传入的userId
            query = userInput,
            contextType = contextType,
            metadata =
            buildMap {
                if (ctx.relevantTemplates.isNotEmpty()) {
                    put("has_relevant_templates", true)
                }
                if (ctx.recentHistory.isNotEmpty()) {
                    put("has_recent_history", true)
                }
            },
        )
    }

    /**
     * 判断是否为简单问候
     */
    private fun isSimpleGreeting(input: String): Boolean {
        val greetings = listOf("hi", "hello", "你好", "嗨", "hey")
        val normalized = input.lowercase().trim()
        return greetings.any { normalized == it || normalized.startsWith("$it ") }
    }

    /**
     * 系统提示词验证器
     * 根据ThinkingBox简化重构完成总结.md的建议实现
     */
    private fun validateSystemPrompt(systemPrompt: String) {
        val hash = systemPrompt.hashCode()
        Timber.i("🔍 systemPromptHash=$hash")

        // 🔥 强制输出系统提示词内容用于调试（分块显示）
        val chunks = systemPrompt.chunked(500)
        chunks.forEachIndexed { index, chunk ->
            Timber.tag("PROMPT-BUILDER").e("📄 系统提示词-第${index + 1}块/${chunks.size}: $chunk")
        }

        // 检查是否包含<think>示例
        if (systemPrompt.contains("<think>")) {
            Timber.w("⚠️ 系统提示词包含<think>示例，可能导致AI输出原始标签")
            // 打印具体的<think>内容位置
            val thinkIndex = systemPrompt.indexOf("<think>")
            val thinkEndIndex = systemPrompt.indexOf("</think>", thinkIndex)
            if (thinkEndIndex != -1) {
                val thinkContent = systemPrompt.substring(thinkIndex, thinkEndIndex + 8)
                Timber.w("🚨 发现<think>内容: $thinkContent")
            }
        }

        // 检查是否包含思考相关指令
        if (systemPrompt.contains("思考标签") || systemPrompt.contains("思考过程")) {
            Timber.w("⚠️ 系统提示词包含思考相关指令，可能导致AI输出标签")
        }

        // 检查是否包含<phase:XXX>指令
        if (systemPrompt.contains("<phase:")) {
            Timber.d("✅ 系统提示词包含<phase:XXX>指令")
        }

        // 检查长度
        Timber.d("📏 系统提示词长度: ${systemPrompt.length}字符")
    }

    /**
     * 验证消息结构
     */
    private fun validateMessages(messages: List<CoreChatMessage>) {
        // 必须有且只有一条system消息
        val systemCount = messages.count { it.role == "system" }
        require(systemCount == 1) {
            "必须有且只有一条system消息，实际: $systemCount"
        }

        // system消息必须在第一条
        require(messages.first().role == "system") {
            "第一条消息必须是system消息"
        }

        // 最后一条必须是user消息
        require(messages.last().role == "user") {
            "最后一条消息必须是user消息"
        }

        Timber.d("LayeredPromptBuilder: 消息验证通过 ✅")
    }

    // =============== 已废弃的方法 ===============

    @Deprecated("改用 buildChatMessages", level = DeprecationLevel.ERROR)
    override suspend fun buildPrompt(
        context: AiContextData,
        userMessage: String,
        tokenBudget: Int,
    ): String = throw UnsupportedOperationException("请使用 buildChatMessages")

    @Deprecated("增量模式已下线", level = DeprecationLevel.ERROR)
    override suspend fun buildIncrementalPrompt(
        context: AiContextData,
        previousContext: AiContextData?,
        userMessage: String,
    ): String = throw UnsupportedOperationException("增量模式已下线")

    @Deprecated("Pipeline已移除", level = DeprecationLevel.ERROR)
    override fun executeSteps(
        userPrompt: String,
        systemLayer: SystemLayer,
        enableFunctions: Boolean,
    ): Flow<PipelineEvent> = emptyFlow()

    @Deprecated("Pipeline已移除", level = DeprecationLevel.ERROR)
    override fun executeStepsIntelligently(
        userPrompt: String,
        systemLayer: SystemLayer,
        enableFunctions: Boolean,
        forceSimpleMode: Boolean,
    ): Flow<PipelineEvent> = emptyFlow()

    /**
     * 获取当前激活的 SystemLayer
     * 用于 SendChatMessageAndGetResponseUseCase 中的 Prompt 切换功能
     */
    fun getCurrentSystemLayer(): SystemLayer {
        val currentPromptSuite = promptRegistry.getSuite()
        return currentPromptSuite.systemLayer
    }
}

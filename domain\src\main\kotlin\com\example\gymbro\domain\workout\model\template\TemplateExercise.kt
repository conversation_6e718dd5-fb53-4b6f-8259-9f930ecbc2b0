package com.example.gymbro.domain.workout.model.template

import com.example.gymbro.domain.workout.model.RepsUnit
import kotlinx.serialization.Serializable
import java.util.UUID

/**
 * 模板运动项目类
 * 表示锻炼模板中的单个运动项目配置
 *
 * @property id 唯一标识符
 * @property exerciseId 关联的运动项目ID
 * @property name 运动项目名称
 * @property order 在模板中的顺序
 * @property sets 建议的组数
 * @property reps 建议的每组重复次数
 * @property restSeconds 建议的休息时间（秒）
 * @property weight 建议的重量（可选）
 * @property notes 相关注释（可选）
 * @property repsUnit 重复次数的单位（例如"次"、"秒"）
 * @property customSets 🔥 新增：详细的组数配置，支持每组独立参数
 */
@Serializable
data class TemplateExercise(
    val id: String = UUID.randomUUID().toString(),
    val exerciseId: String,
    val name: String,
    val order: Int = 0,
    val sets: Int = 3,
    val reps: Int = 10,
    val restSeconds: Int = 60,
    val weight: Float? = null,
    val notes: String? = null,
    val repsUnit: RepsUnit = RepsUnit.REPS,
    // 🔥 关键修复：添加 customSets 字段，支持每组独立配置
    val customSets: List<TemplateSet> = emptyList(),
    // 🔥 关键修复：添加动作库JSON数据字段（imageUrl, videoUrl等）
    val imageUrl: String? = null,
    val videoUrl: String? = null,
) {
    /**
     * 检查模板运动项目的必要属性是否有效
     * @return 如果有效返回true
     */
    fun isValid(): Boolean {
        return exerciseId.isNotBlank() && name.isNotBlank() && sets > 0
    }
}

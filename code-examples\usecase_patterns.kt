package com.example.gymbro.examples

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.common.Logger
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.withContext
import kotlinx.coroutines.Dispatchers
import javax.inject.Inject

/**
 * UseCase模式实现示例
 *
 * 本示例展示了GymBro项目中Domain层UseCase的完整实现模式：
 * 1. Clean Architecture业务逻辑封装
 * 2. 单一职责原则和组合模式
 * 3. ModernResult错误处理
 * 4. 响应式编程和协程集成
 * 5. 依赖注入和测试支持
 *
 * 架构特点：
 * - 业务逻辑集中：所有业务规则在Domain层
 * - 可组合设计：UseCase可以组合调用
 * - 类型安全：强类型参数和返回值
 * - 错误透明：统一的错误处理和传播
 */

// ========================================
// 1. UseCase基础接口定义
// ========================================

/**
 * 基础UseCase接口
 * 
 * 设计原则：
 * - 单一职责：每个UseCase只做一件事
 * - 无状态：UseCase不应该持有状态
 * - 可测试：纯函数便于单元测试
 */
interface UseCase<in Params, out Result> {
    suspend operator fun invoke(params: Params): Result
}

/**
 * 流式UseCase接口
 * 
 * 适用于需要响应式数据流的场景
 */
interface FlowUseCase<in Params, out Result> {
    operator fun invoke(params: Params): Flow<Result>
}

/**
 * 无参数UseCase接口
 */
interface NoParamsUseCase<out Result> {
    suspend operator fun invoke(): Result
}

/**
 * 无参数流式UseCase接口
 */
interface NoParamsFlowUseCase<out Result> {
    operator fun invoke(): Flow<Result>
}

// ========================================
// 2. 数据模型
// ========================================

/**
 * 训练统计数据
 */
data class WorkoutStats(
    val totalWorkouts: Int,
    val totalDuration: Long, // 总时长(分钟)
    val totalCalories: Int,
    val averageDuration: Double,
    val averageCalories: Double,
    val mostUsedTags: List<String>,
    val progressTrend: ProgressTrend
)

/**
 * 进度趋势
 */
data class ProgressTrend(
    val direction: TrendDirection,
    val percentage: Double,
    val period: String
)

/**
 * 趋势方向
 */
enum class TrendDirection {
    UP, DOWN, STABLE
}

/**
 * 搜索参数
 */
data class SearchParams(
    val query: String,
    val tags: List<String> = emptyList(),
    val difficultyRange: IntRange? = null,
    val dateRange: LongRange? = null,
    val sortBy: SortField = SortField.DATE,
    val sortOrder: SortOrder = SortOrder.DESC
)

/**
 * 排序字段
 */
enum class SortField {
    DATE, DURATION, CALORIES, DIFFICULTY, TITLE
}

/**
 * 排序顺序
 */
enum class SortOrder {
    ASC, DESC
}

// ========================================
// 3. 简单UseCase实现
// ========================================

/**
 * 获取训练记录列表UseCase
 * 
 * 职责：
 * - 从Repository获取所有训练记录
 * - 提供响应式数据流
 * - 处理加载状态和错误
 */
class GetWorkoutRecordsUseCase @Inject constructor(
    private val repository: WorkoutRecordRepository,
    private val logger: Logger
) : NoParamsFlowUseCase<ModernResult<List<WorkoutRecord>>> {

    override fun invoke(): Flow<ModernResult<List<WorkoutRecord>>> {
        logger.d(TAG, "开始获取训练记录列表")
        
        return repository.getAllRecords()
            .onStart {
                logger.d(TAG, "发出Loading状态")
                emit(ModernResult.Loading())
            }
            .catch { throwable ->
                logger.e(TAG, "获取训练记录失败", throwable)
                emit(ModernResult.Error(throwable.toUseCaseError("获取训练记录列表")))
            }
    }

    companion object {
        private const val TAG = "GetWorkoutRecordsUseCase"
    }
}

/**
 * 根据ID获取训练记录UseCase
 * 
 * 特点：
 * - 单一参数UseCase
 * - 参数验证和业务规则
 * - 详细的错误处理
 */
class GetWorkoutRecordByIdUseCase @Inject constructor(
    private val repository: WorkoutRecordRepository,
    private val logger: Logger
) : UseCase<String, ModernResult<WorkoutRecord>> {

    override suspend fun invoke(params: String): ModernResult<WorkoutRecord> {
        logger.d(TAG, "获取训练记录: $params")
        
        // 参数验证
        if (params.isBlank()) {
            logger.w(TAG, "训练记录ID为空")
            return ModernResult.Error(
                ModernDataError(
                    operationName = "获取训练记录",
                    errorType = GlobalErrorType.Data.ValidationError,
                    category = ErrorCategory.DATA,
                    uiMessage = UiText.DynamicString("训练记录ID不能为空")
                )
            )
        }
        
        return try {
            withContext(Dispatchers.IO) {
                val result = repository.getRecordById(params)
                logger.d(TAG, "获取训练记录完成: ${result.javaClass.simpleName}")
                result
            }
        } catch (throwable: Throwable) {
            logger.e(TAG, "获取训练记录异常", throwable)
            ModernResult.Error(throwable.toUseCaseError("获取训练记录"))
        }
    }

    companion object {
        private const val TAG = "GetWorkoutRecordByIdUseCase"
    }
}

// ========================================
// 4. 复杂UseCase实现
// ========================================

/**
 * 创建训练记录UseCase
 * 
 * 业务逻辑：
 * - 数据验证和清理
 * - 自动生成ID和时间戳
 * - 标签标准化处理
 * - 难度等级校验
 */
class CreateWorkoutRecordUseCase @Inject constructor(
    private val repository: WorkoutRecordRepository,
    private val idGenerator: IdGenerator,
    private val logger: Logger
) : UseCase<CreateWorkoutRecordUseCase.Params, ModernResult<WorkoutRecord>> {

    data class Params(
        val title: String,
        val description: String,
        val duration: Long,
        val calories: Int,
        val difficulty: Int,
        val tags: List<String>
    )

    override suspend fun invoke(params: Params): ModernResult<WorkoutRecord> {
        logger.d(TAG, "开始创建训练记录: ${params.title}")
        
        // 业务规则验证
        val validationResult = validateParams(params)
        if (validationResult is ModernResult.Error) {
            return validationResult
        }
        
        return try {
            withContext(Dispatchers.IO) {
                val record = WorkoutRecord(
                    id = idGenerator.generateId(),
                    title = params.title.trim(),
                    description = params.description.trim(),
                    duration = params.duration,
                    calories = params.calories,
                    difficulty = params.difficulty,
                    tags = normalizeTags(params.tags),
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                    isSynced = false
                )
                
                logger.d(TAG, "创建训练记录对象完成，准备保存到Repository")
                repository.createRecord(record)
            }
        } catch (throwable: Throwable) {
            logger.e(TAG, "创建训练记录异常", throwable)
            ModernResult.Error(throwable.toUseCaseError("创建训练记录"))
        }
    }

    private fun validateParams(params: Params): ModernResult<Unit> {
        when {
            params.title.isBlank() -> {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "创建训练记录",
                        errorType = GlobalErrorType.Data.ValidationError,
                        category = ErrorCategory.DATA,
                        uiMessage = UiText.DynamicString("训练标题不能为空")
                    )
                )
            }
            params.duration <= 0 -> {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "创建训练记录",
                        errorType = GlobalErrorType.Data.ValidationError,
                        category = ErrorCategory.DATA,
                        uiMessage = UiText.DynamicString("训练时长必须大于0")
                    )
                )
            }
            params.calories < 0 -> {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "创建训练记录",
                        errorType = GlobalErrorType.Data.ValidationError,
                        category = ErrorCategory.DATA,
                        uiMessage = UiText.DynamicString("消耗卡路里不能为负数")
                    )
                )
            }
            params.difficulty !in 1..5 -> {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "创建训练记录",
                        errorType = GlobalErrorType.Data.ValidationError,
                        category = ErrorCategory.DATA,
                        uiMessage = UiText.DynamicString("难度等级必须在1-5之间")
                    )
                )
            }
        }
        return ModernResult.Success(Unit)
    }

    private fun normalizeTags(tags: List<String>): List<String> {
        return tags
            .map { it.trim().lowercase() }
            .filter { it.isNotBlank() }
            .distinct()
            .take(10) // 限制最多10个标签
    }

    companion object {
        private const val TAG = "CreateWorkoutRecordUseCase"
    }
}

/**
 * 搜索训练记录UseCase
 * 
 * 复杂业务逻辑：
 * - 多条件搜索
 * - 结果排序和分页
 * - 搜索结果优化
 */
class SearchWorkoutRecordsUseCase @Inject constructor(
    private val repository: WorkoutRecordRepository,
    private val logger: Logger
) : FlowUseCase<SearchParams, ModernResult<List<WorkoutRecord>>> {

    override fun invoke(params: SearchParams): Flow<ModernResult<List<WorkoutRecord>>> {
        logger.d(TAG, "开始搜索训练记录: ${params.query}")
        
        return if (params.query.isBlank()) {
            // 空查询返回所有记录
            repository.getAllRecords()
        } else {
            // 文本搜索
            repository.searchRecords(params.query)
        }.map { result ->
            when (result) {
                is ModernResult.Success -> {
                    val filteredRecords = applyFilters(result.data, params)
                    val sortedRecords = applySorting(filteredRecords, params)
                    ModernResult.Success(sortedRecords)
                }
                is ModernResult.Error -> result
                is ModernResult.Loading -> result
            }
        }.catch { throwable ->
            logger.e(TAG, "搜索训练记录异常", throwable)
            emit(ModernResult.Error(throwable.toUseCaseError("搜索训练记录")))
        }
    }

    private fun applyFilters(records: List<WorkoutRecord>, params: SearchParams): List<WorkoutRecord> {
        var filtered = records
        
        // 标签过滤
        if (params.tags.isNotEmpty()) {
            filtered = filtered.filter { record ->
                params.tags.any { tag -> record.tags.contains(tag.lowercase()) }
            }
        }
        
        // 难度过滤
        params.difficultyRange?.let { range ->
            filtered = filtered.filter { record ->
                record.difficulty in range
            }
        }
        
        // 日期范围过滤
        params.dateRange?.let { range ->
            filtered = filtered.filter { record ->
                record.createdAt in range
            }
        }
        
        return filtered
    }

    private fun applySorting(records: List<WorkoutRecord>, params: SearchParams): List<WorkoutRecord> {
        val comparator = when (params.sortBy) {
            SortField.DATE -> compareBy<WorkoutRecord> { it.createdAt }
            SortField.DURATION -> compareBy<WorkoutRecord> { it.duration }
            SortField.CALORIES -> compareBy<WorkoutRecord> { it.calories }
            SortField.DIFFICULTY -> compareBy<WorkoutRecord> { it.difficulty }
            SortField.TITLE -> compareBy<WorkoutRecord> { it.title }
        }
        
        return if (params.sortOrder == SortOrder.DESC) {
            records.sortedWith(comparator.reversed())
        } else {
            records.sortedWith(comparator)
        }
    }

    companion object {
        private const val TAG = "SearchWorkoutRecordsUseCase"
    }
}

// ========================================
// 5. 组合UseCase实现
// ========================================

/**
 * 计算训练统计UseCase
 * 
 * 组合模式：
 * - 调用其他UseCase获取数据
 * - 复杂的业务计算逻辑
 * - 聚合多个数据源
 */
class CalculateWorkoutStatsUseCase @Inject constructor(
    private val getWorkoutRecordsUseCase: GetWorkoutRecordsUseCase,
    private val logger: Logger
) : NoParamsUseCase<ModernResult<WorkoutStats>> {

    override suspend fun invoke(): ModernResult<WorkoutStats> {
        logger.d(TAG, "开始计算训练统计")
        
        return try {
            // 组合调用其他UseCase
            val recordsFlow = getWorkoutRecordsUseCase()
            var latestResult: ModernResult<List<WorkoutRecord>>? = null
            
            recordsFlow.collect { result ->
                latestResult = result
            }
            
            when (val result = latestResult) {
                is ModernResult.Success -> {
                    val stats = calculateStats(result.data)
                    logger.d(TAG, "统计计算完成: 总计${stats.totalWorkouts}次训练")
                    ModernResult.Success(stats)
                }
                is ModernResult.Error -> {
                    logger.e(TAG, "获取训练记录失败，无法计算统计")
                    result as ModernResult<WorkoutStats>
                }
                is ModernResult.Loading -> {
                    logger.d(TAG, "等待训练记录加载")
                    ModernResult.Loading()
                }
                null -> {
                    logger.e(TAG, "未获取到训练记录数据")
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "计算训练统计",
                            errorType = GlobalErrorType.System.General,
                            category = ErrorCategory.SYSTEM,
                            uiMessage = UiText.DynamicString("无法获取训练数据")
                        )
                    )
                }
            }
        } catch (throwable: Throwable) {
            logger.e(TAG, "计算训练统计异常", throwable)
            ModernResult.Error(throwable.toUseCaseError("计算训练统计"))
        }
    }

    private fun calculateStats(records: List<WorkoutRecord>): WorkoutStats {
        if (records.isEmpty()) {
            return WorkoutStats(
                totalWorkouts = 0,
                totalDuration = 0,
                totalCalories = 0,
                averageDuration = 0.0,
                averageCalories = 0.0,
                mostUsedTags = emptyList(),
                progressTrend = ProgressTrend(TrendDirection.STABLE, 0.0, "无数据")
            )
        }
        
        val totalDuration = records.sumOf { it.duration }
        val totalCalories = records.sumOf { it.calories }
        val averageDuration = totalDuration.toDouble() / records.size
        val averageCalories = totalCalories.toDouble() / records.size
        
        // 计算最常用标签
        val tagCounts = records
            .flatMap { it.tags }
            .groupingBy { it }
            .eachCount()
        val mostUsedTags = tagCounts
            .toList()
            .sortedByDescending { it.second }
            .take(5)
            .map { it.first }
        
        // 计算进度趋势（简化版）
        val progressTrend = calculateProgressTrend(records)
        
        return WorkoutStats(
            totalWorkouts = records.size,
            totalDuration = totalDuration,
            totalCalories = totalCalories,
            averageDuration = averageDuration,
            averageCalories = averageCalories,
            mostUsedTags = mostUsedTags,
            progressTrend = progressTrend
        )
    }

    private fun calculateProgressTrend(records: List<WorkoutRecord>): ProgressTrend {
        if (records.size < 2) {
            return ProgressTrend(TrendDirection.STABLE, 0.0, "数据不足")
        }
        
        val sortedRecords = records.sortedBy { it.createdAt }
        val recentRecords = sortedRecords.takeLast(7) // 最近7次训练
        val previousRecords = sortedRecords.dropLast(7).takeLast(7) // 之前7次训练
        
        if (previousRecords.isEmpty()) {
            return ProgressTrend(TrendDirection.STABLE, 0.0, "历史数据不足")
        }
        
        val recentAverage = recentRecords.map { it.duration }.average()
        val previousAverage = previousRecords.map { it.duration }.average()
        
        val changePercentage = ((recentAverage - previousAverage) / previousAverage) * 100
        
        val direction = when {
            changePercentage > 5 -> TrendDirection.UP
            changePercentage < -5 -> TrendDirection.DOWN
            else -> TrendDirection.STABLE
        }
        
        return ProgressTrend(
            direction = direction,
            percentage = kotlin.math.abs(changePercentage),
            period = "近7次训练"
        )
    }

    companion object {
        private const val TAG = "CalculateWorkoutStatsUseCase"
    }
}

// ========================================
// 6. 批量操作UseCase
// ========================================

/**
 * 批量删除训练记录UseCase
 * 
 * 特点：
 * - 批量操作处理
 * - 事务性保证
 * - 部分失败处理
 */
class DeleteWorkoutRecordsBatchUseCase @Inject constructor(
    private val repository: WorkoutRecordRepository,
    private val logger: Logger
) : UseCase<List<String>, ModernResult<DeleteWorkoutRecordsBatchUseCase.Result>> {

    data class Result(
        val successCount: Int,
        val failureCount: Int,
        val failedIds: List<String>,
        val errors: List<String>
    )

    override suspend fun invoke(params: List<String>): ModernResult<Result> {
        logger.d(TAG, "开始批量删除训练记录，数量: ${params.size}")
        
        if (params.isEmpty()) {
            return ModernResult.Success(
                Result(
                    successCount = 0,
                    failureCount = 0,
                    failedIds = emptyList(),
                    errors = emptyList()
                )
            )
        }
        
        return try {
            withContext(Dispatchers.IO) {
                val results = params.map { id ->
                    try {
                        val result = repository.deleteRecord(id)
                        if (result is ModernResult.Success) {
                            logger.d(TAG, "删除成功: $id")
                            BatchResult.Success(id)
                        } else {
                            val error = (result as ModernResult.Error).error
                            logger.w(TAG, "删除失败: $id, 错误: ${error.uiMessage}")
                            BatchResult.Failure(id, error.uiMessage.toString())
                        }
                    } catch (throwable: Throwable) {
                        logger.e(TAG, "删除异常: $id", throwable)
                        BatchResult.Failure(id, throwable.message ?: "未知错误")
                    }
                }
                
                val successResults = results.filterIsInstance<BatchResult.Success>()
                val failureResults = results.filterIsInstance<BatchResult.Failure>()
                
                val finalResult = Result(
                    successCount = successResults.size,
                    failureCount = failureResults.size,
                    failedIds = failureResults.map { it.id },
                    errors = failureResults.map { it.error }
                )
                
                logger.d(TAG, "批量删除完成: 成功${finalResult.successCount}, 失败${finalResult.failureCount}")
                ModernResult.Success(finalResult)
            }
        } catch (throwable: Throwable) {
            logger.e(TAG, "批量删除异常", throwable)
            ModernResult.Error(throwable.toUseCaseError("批量删除训练记录"))
        }
    }

    private sealed class BatchResult {
        data class Success(val id: String) : BatchResult()
        data class Failure(val id: String, val error: String) : BatchResult()
    }

    companion object {
        private const val TAG = "DeleteWorkoutRecordsBatchUseCase"
    }
}

// ========================================
// 7. 辅助工具和扩展
// ========================================

/**
 * ID生成器接口
 */
interface IdGenerator {
    fun generateId(): String
}

/**
 * 异常转换扩展函数
 */
private fun Throwable.toUseCaseError(operationName: String): ModernDataError {
    return ModernDataError(
        operationName = operationName,
        errorType = when (this) {
            is IllegalArgumentException -> GlobalErrorType.Data.ValidationError
            is IllegalStateException -> GlobalErrorType.System.General
            else -> GlobalErrorType.System.General
        },
        category = when (this) {
            is IllegalArgumentException -> ErrorCategory.DATA
            else -> ErrorCategory.SYSTEM
        },
        uiMessage = UiText.DynamicString(this.message ?: "操作失败"),
        cause = this
    )
}

// ========================================
// 8. 使用模式说明
// ========================================

/**
 * UseCase使用模式示例
 * 
 * 在ViewModel中的典型用法：
 * 
 * ```kotlin
 * @HiltViewModel
 * class WorkoutViewModel @Inject constructor(
 *     private val getWorkoutRecordsUseCase: GetWorkoutRecordsUseCase,
 *     private val createWorkoutRecordUseCase: CreateWorkoutRecordUseCase,
 *     private val searchWorkoutRecordsUseCase: SearchWorkoutRecordsUseCase,
 *     private val calculateWorkoutStatsUseCase: CalculateWorkoutStatsUseCase
 * ) : ViewModel() {
 *     
 *     // 响应式数据流
 *     val workoutRecords = getWorkoutRecordsUseCase()
 *         .stateIn(
 *             scope = viewModelScope,
 *             started = SharingStarted.WhileSubscribed(5000),
 *             initialValue = ModernResult.Loading()
 *         )
 *     
 *     // 搜索功能
 *     fun searchRecords(query: String) {
 *         val searchParams = SearchParams(
 *             query = query,
 *             sortBy = SortField.DATE,
 *             sortOrder = SortOrder.DESC
 *         )
 *         
 *         searchWorkoutRecordsUseCase(searchParams)
 *             .onEach { result ->
 *                 // 处理搜索结果
 *             }
 *             .launchIn(viewModelScope)
 *     }
 *     
 *     // 创建训练记录
 *     fun createRecord(title: String, duration: Long, calories: Int) {
 *         viewModelScope.launch {
 *             val params = CreateWorkoutRecordUseCase.Params(
 *                 title = title,
 *                 description = "",
 *                 duration = duration,
 *                 calories = calories,
 *                 difficulty = 3,
 *                 tags = emptyList()
 *             )
 *             
 *             when (val result = createWorkoutRecordUseCase(params)) {
 *                 is ModernResult.Success -> {
 *                     // 创建成功
 *                 }
 *                 is ModernResult.Error -> {
 *                     // 显示错误
 *                 }
 *                 is ModernResult.Loading -> {
 *                     // 显示加载状态
 *                 }
 *             }
 *         }
 *     }
 * }
 * ```
 * 
 * 测试模式示例：
 * 
 * ```kotlin
 * @Test
 * fun `when create record with valid params, should return success`() = runTest {
 *     // Given
 *     val params = CreateWorkoutRecordUseCase.Params(
 *         title = "测试训练",
 *         description = "测试描述",
 *         duration = 60,
 *         calories = 300,
 *         difficulty = 3,
 *         tags = listOf("力量", "胸部")
 *     )
 *     val expectedRecord = WorkoutRecord(...)
 *     coEvery { repository.createRecord(any()) } returns ModernResult.Success(expectedRecord)
 *     
 *     // When
 *     val result = createWorkoutRecordUseCase(params)
 *     
 *     // Then
 *     assertTrue(result is ModernResult.Success)
 *     assertEquals(expectedRecord, (result as ModernResult.Success).data)
 * }
 * ```
 */

/**
 * UseCase架构总结
 * 
 * 1. **单一职责原则**：
 *    - 每个UseCase只处理一个业务场景
 *    - 清晰的输入输出定义
 *    - 业务逻辑集中管理
 * 
 * 2. **组合模式**：
 *    - UseCase可以组合调用其他UseCase
 *    - 复杂业务逻辑通过组合实现
 *    - 保持各UseCase的独立性
 * 
 * 3. **错误处理**：
 *    - ModernResult统一错误包装
 *    - 业务验证和异常处理
 *    - 用户友好的错误信息
 * 
 * 4. **响应式支持**：
 *    - Flow响应式编程
 *    - 实时数据更新
 *    - 背压和错误传播
 * 
 * 5. **测试友好**：
 *    - 依赖注入支持Mock
 *    - 纯函数便于测试
 *    - 清晰的接口定义
 * 
 * 6. **性能优化**：
 *    - 适当的协程调度
 *    - 批量操作支持
 *    - 缓存和优化策略
 */
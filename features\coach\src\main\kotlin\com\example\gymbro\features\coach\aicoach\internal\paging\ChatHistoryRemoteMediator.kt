package com.example.gymbro.features.coach.aicoach.internal.paging

import androidx.paging.ExperimentalPagingApi
import androidx.paging.LoadType
import androidx.paging.PagingState
import androidx.paging.RemoteMediator
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.session.mapper.ChatSessionMapper.toDomainMessage
import com.example.gymbro.data.local.database.AppDatabase
import com.example.gymbro.domain.coach.repository.ChatRepository
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.aicoach.AiCoachMapper
import timber.log.Timber
import javax.inject.Inject

/**
 * ChatHistoryRemoteMediator - 聊天历史分页中介器
 *
 * 实现704coach优化计划Week 1目标：
 * - 基于现有History系统的Paging 3集成
 * - 利用现有AutoSave数据进行高效分页
 * - 集成ROOM的索引优化(session_id + timestamp)
 * - 支持ChatGPT式反向列表体验
 *
 * 设计原则：
 * - 利用现有ChatRawDao分页方法，避免重复实现
 * - 基于timestamp进行高效排序和分页
 * - 遵循RemoteMediator最佳实践
 * - 与ThinkingBox流式消息分离，专注历史数据
 *
 * @param chatRepository 聊天仓库，提供数据访问
 * @param chatRawDao 直接数据访问层，用于分页查询
 * @param database 数据库实例，用于事务操作
 */
@OptIn(ExperimentalPagingApi::class)
internal class ChatHistoryRemoteMediator @Inject constructor(
    private val chatRepository: ChatRepository,
    internal val chatRawDao: ChatRawDao, // 🔥 改为internal，供ViewModel访问
    private val database: AppDatabase,
) : RemoteMediator<Int, AiCoachContract.MessageUi>() {

    // 🔥 动态会话ID，通过setSessionId方法设置
    private var currentSessionId: String? = null

    companion object {
        private const val TAG = "ChatHistoryRemoteMediator"
        private const val PAGE_SIZE = 20 // 每页加载消息数量
        private const val STARTING_PAGE_INDEX = 0

        // 🔥 ChatGPT式体验：从最新消息开始加载
        private const val LOAD_DIRECTION_DESC = true
    }

    /**
     * 设置当前会话ID
     *
     * 必须在使用Pager之前调用此方法设置会话ID
     */
    fun setSessionId(sessionId: String) {
        this.currentSessionId = sessionId
        Timber.tag(TAG).d("🔥 设置会话ID: $sessionId")
    }

    /**
     * 实现RemoteMediator核心加载逻辑
     *
     * 策略：
     * 1. REFRESH: 加载最新的PAGE_SIZE条消息（ChatGPT式从底部开始）
     * 2. PREPEND: 加载更旧的消息（向上滚动时）
     * 3. APPEND: 加载更新的消息（向下滚动时，通常用于新消息）
     */
    override suspend fun load(
        loadType: LoadType,
        state: PagingState<Int, AiCoachContract.MessageUi>,
    ): MediatorResult {
        // 🔥 验证会话ID已设置
        val sessionId = currentSessionId
        if (sessionId == null) {
            Timber.tag(TAG).e("🔥 会话ID未设置，无法加载数据")
            return MediatorResult.Error(IllegalStateException("Session ID not set"))
        }

        return try {
            Timber.tag(TAG).d("🔥 开始加载: loadType=$loadType, sessionId=$sessionId")

            // 🔥 关键逻辑：计算加载参数
            val loadParams = calculateLoadParams(loadType, state)

            Timber.tag(TAG).d("🔥 加载参数: offset=${loadParams.offset}, limit=${loadParams.limit}")

            // 🔥 利用现有ChatRawDao分页方法（现已支持降序）
            val rawMessages = if (LOAD_DIRECTION_DESC) {
                // ChatGPT式：按时间戳降序加载（最新消息在前）
                chatRawDao.getMessagesBySessionPagedDesc(
                    sessionId = sessionId,
                    limit = loadParams.limit,
                    offset = loadParams.offset,
                )
            } else {
                // 传统式：按时间戳升序加载
                chatRawDao.getMessagesBySessionPaged(
                    sessionId = sessionId,
                    limit = loadParams.limit,
                    offset = loadParams.offset,
                )
            }

            Timber.tag(TAG).d("🔥 从数据库加载了 ${rawMessages.size} 条原始消息")

            // 🔥 数据转换：ChatRaw -> CoachMessage -> MessageUi
            val domainMessages = rawMessages.map { it.toDomainMessage() }
            val messageUiList = AiCoachMapper.convertCoachMessagesToMessageUi(domainMessages)

            Timber.tag(TAG).d("🔥 转换完成: ${messageUiList.size} 条UI消息")

            // 🔥 判断是否到达数据边界
            val endOfPaginationReached = rawMessages.size < loadParams.limit

            Timber.tag(TAG).d("🔥 分页状态: endOfPaginationReached=$endOfPaginationReached")

            // 🔥 返回成功结果
            MediatorResult.Success(endOfPaginationReached = endOfPaginationReached)
        } catch (exception: Exception) {
            Timber.tag(TAG).e(exception, "🔥 分页加载失败: sessionId=$sessionId")
            MediatorResult.Error(exception)
        }
    }

    /**
     * 计算加载参数
     *
     * 根据不同的LoadType计算合适的offset和limit
     * 支持ChatGPT式反向分页体验
     */
    private fun calculateLoadParams(
        loadType: LoadType,
        state: PagingState<Int, AiCoachContract.MessageUi>,
    ): LoadParams {
        return when (loadType) {
            LoadType.REFRESH -> {
                // 🔥 初始加载或刷新：从最新消息开始
                LoadParams(
                    offset = STARTING_PAGE_INDEX,
                    limit = PAGE_SIZE,
                )
            }

            LoadType.PREPEND -> {
                // 🔥 向前加载（更旧的消息）
                val anchorPosition = state.anchorPosition ?: 0
                val anchorPage = state.closestPageToPosition(anchorPosition)
                val prevKey = anchorPage?.prevKey

                if (prevKey == null) {
                    // 已经到达最开始，无更多旧消息
                    LoadParams(offset = 0, limit = 0)
                } else {
                    LoadParams(
                        offset = prevKey,
                        limit = PAGE_SIZE,
                    )
                }
            }

            LoadType.APPEND -> {
                // 🔥 向后加载（更新的消息）
                val anchorPosition = state.anchorPosition ?: 0
                val anchorPage = state.closestPageToPosition(anchorPosition)
                val nextKey = anchorPage?.nextKey

                if (nextKey == null) {
                    // 已经到达最新，无更多新消息
                    LoadParams(offset = 0, limit = 0)
                } else {
                    LoadParams(
                        offset = nextKey,
                        limit = PAGE_SIZE,
                    )
                }
            }
        }
    }

    /**
     * 加载参数数据类
     */
    private data class LoadParams(
        val offset: Int,
        val limit: Int,
    )
}

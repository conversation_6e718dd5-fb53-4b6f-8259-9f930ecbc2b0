package com.example.gymbro.domain.workout.usecase.stats

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.workout.model.stats.TimeRange
import com.example.gymbro.domain.workout.model.stats.UnifiedWorkoutStatistics
import com.example.gymbro.domain.workout.repository.StatsRepository
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.LocalDate
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * GetStatsUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试UseCase的核心业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证Flow返回类型
 * ✔️ 测试不同时间范围的计算逻辑
 */
class GetStatsUseCaseTest {

    @MockK
    private lateinit var statsRepository: StatsRepository

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: GetStatsUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = GetStatsUseCase(
            statsRepository = statsRepository,
            ioDispatcher = testDispatcher,
        )
    }

    @Test
    fun `invoke with WEEK should return weekly stats`() = runTest(testDispatcher) {
        // Given
        val expectedStats = createSampleStats()
        coEvery {
            statsRepository.getStatsInRange(any(), any(), TimeRange.WEEK)
        } returns flowOf(ModernResult.Success(expectedStats))

        // When
        val result = useCase.invoke(TimeRange.WEEK).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        assertEquals(expectedStats, (result.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { statsRepository.getStatsInRange(any(), any(), TimeRange.WEEK) }
    }

    @Test
    fun `invoke with MONTH should return monthly stats`() = runTest(testDispatcher) {
        // Given
        val expectedStats = createSampleStats()
        coEvery {
            statsRepository.getStatsInRange(any(), any(), TimeRange.MONTH)
        } returns flowOf(ModernResult.Success(expectedStats))

        // When
        val result = useCase.invoke(TimeRange.MONTH).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        assertEquals(expectedStats, (result.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { statsRepository.getStatsInRange(any(), any(), TimeRange.MONTH) }
    }

    @Test
    fun `invoke with YEAR should return yearly stats`() = runTest(testDispatcher) {
        // Given
        val expectedStats = createSampleStats()
        coEvery {
            statsRepository.getStatsInRange(any(), any(), TimeRange.YEAR)
        } returns flowOf(ModernResult.Success(expectedStats))

        // When
        val result = useCase.invoke(TimeRange.YEAR).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        assertEquals(expectedStats, (result.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { statsRepository.getStatsInRange(any(), any(), TimeRange.YEAR) }
    }

    @Test
    fun `invoke with CUSTOM should use provided dates`() = runTest(testDispatcher) {
        // Given
        val startDate = LocalDate(2024, 1, 1)
        val endDate = LocalDate(2024, 1, 31)
        val expectedStats = createSampleStats()

        coEvery {
            statsRepository.getStatsInRange(startDate, endDate, TimeRange.CUSTOM)
        } returns flowOf(ModernResult.Success(expectedStats))

        // When
        val result = useCase.invoke(TimeRange.CUSTOM, startDate, endDate).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        assertEquals(expectedStats, (result.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { statsRepository.getStatsInRange(startDate, endDate, TimeRange.CUSTOM) }
    }

    @Test
    fun `invoke with CUSTOM should use default dates when not provided`() = runTest(testDispatcher) {
        // Given
        val expectedStats = createSampleStats()
        coEvery {
            statsRepository.getStatsInRange(any(), any(), TimeRange.CUSTOM)
        } returns flowOf(ModernResult.Success(expectedStats))

        // When
        val result = useCase.invoke(TimeRange.CUSTOM).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        coVerify(exactly = 1) { statsRepository.getStatsInRange(any(), any(), TimeRange.CUSTOM) }
    }

    @Test
    fun `invoke should handle repository error`() = runTest(testDispatcher) {
        // Given
        val error = RuntimeException("Repository error")
        coEvery {
            statsRepository.getStatsInRange(any(), any(), any())
        } returns flowOf(ModernResult.Error(error))

        // When
        val result = useCase.invoke(TimeRange.WEEK).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Error)
        coVerify(exactly = 1) { statsRepository.getStatsInRange(any(), any(), TimeRange.WEEK) }
    }

    @Test
    fun `invoke should override dates when explicitly provided`() = runTest(testDispatcher) {
        // Given
        val customStartDate = LocalDate(2024, 6, 1)
        val customEndDate = LocalDate(2024, 6, 30)
        val expectedStats = createSampleStats()

        coEvery {
            statsRepository.getStatsInRange(customStartDate, customEndDate, TimeRange.MONTH)
        } returns flowOf(ModernResult.Success(expectedStats))

        // When
        val result = useCase.invoke(TimeRange.MONTH, customStartDate, customEndDate).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        coVerify(exactly = 1) {
            statsRepository.getStatsInRange(customStartDate, customEndDate, TimeRange.MONTH)
        }
    }

    @Test
    fun `invoke should handle empty flow from repository`() = runTest(testDispatcher) {
        // Given
        coEvery {
            statsRepository.getStatsInRange(any(), any(), any())
        } returns flowOf()

        // When
        val result = useCase.invoke(TimeRange.WEEK).toList()

        // Then
        assertEquals(0, result.size)
        coVerify(exactly = 1) { statsRepository.getStatsInRange(any(), any(), TimeRange.WEEK) }
    }

    @Test
    fun `invoke should handle multiple emissions from repository`() = runTest(testDispatcher) {
        // Given
        val stats1 = createSampleStats()
        val stats2 = createSampleStats()
        coEvery {
            statsRepository.getStatsInRange(any(), any(), any())
        } returns flowOf(
            ModernResult.Success(stats1),
            ModernResult.Success(stats2),
        )

        // When
        val result = useCase.invoke(TimeRange.WEEK).toList()

        // Then
        assertEquals(2, result.size)
        assertTrue(result[0] is ModernResult.Success)
        assertTrue(result[1] is ModernResult.Success)
        coVerify(exactly = 1) { statsRepository.getStatsInRange(any(), any(), TimeRange.WEEK) }
    }

    // === 辅助方法 ===

    private fun createSampleStats(): UnifiedWorkoutStatistics {
        return UnifiedWorkoutStatistics(
            totalWorkouts = 10,
            totalWorkoutTime = 600, // 10 hours in minutes
            averageWorkoutTime = 60,
            totalVolume = 1000.0,
            averageVolume = 100.0,
            totalSets = 50,
            averageSets = 5,
            workoutFrequency = 3.5,
            personalRecords = 2,
            exerciseCount = 15,
            favoriteExercises = listOf("深蹲", "卧推", "硬拉"),
            workoutStreak = 7,
            caloriesBurned = 2500,
            averageRestTime = 90,
            muscleGroupDistribution = mapOf(
                "腿部" to 30,
                "胸部" to 25,
                "背部" to 25,
                "肩部" to 20,
            ),
            progressTrend = "IMPROVING",
            lastWorkoutDate = LocalDate(2024, 1, 15),
            nextScheduledWorkout = LocalDate(2024, 1, 17),
            timeRange = TimeRange.WEEK,
        )
    }
}
package com.example.gymbro.core.ml.service

import com.example.gymbro.core.ml.embedding.BgeEmbeddingEngine
import com.example.gymbro.core.ml.embedding.EngineStatus
import kotlinx.coroutines.flow.StateFlow
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * BGE引擎管理器 - 全局单例
 *
 * 负责统一管理BGE模型的生命周期和状态：
 * - 提供幂等的初始化方法（可安全重复调用）
 * - 暴露引擎状态供UI观察
 * - 支持应用启动时预加载
 * - 确保全局只有一个BGE引擎实例
 */
@Singleton
class BgeEngineManager @Inject constructor(
    private val bgeEngine: BgeEmbeddingEngine,
) {

    /**
     * 🔥 暴露引擎状态 - UI可以观察这个Flow
     */
    val engineStatus: StateFlow<EngineStatus> = bgeEngine.status

    /**
     * 🔥 幂等的初始化方法 - 黄金标准（非阻塞版本）
     *
     * 这个方法可以在任何地方安全地调用：
     * - Application.onCreate() 预加载时
     * - ViewModel.init 确保可用时
     * - 真正使用前检查时
     *
     * 内部逻辑保证只有第一次调用会真正执行加载，
     * 后续调用会立即返回，不会阻塞调用者
     */
    suspend fun initialize() {
        if (bgeEngine.status.value == EngineStatus.UNINITIALIZED) {
            try {
                bgeEngine.initialize()
                // 初始化成功后，立即在后台进行预热推理
                if (bgeEngine.status.value == EngineStatus.READY) {
                    try {
                        bgeEngine.warmUp()
                        Timber.i("BGE引擎预热成功")
                    } catch (e: Exception) {
                        Timber.w(e, "BGE引擎预热失败")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "BGE引擎初始化异常")
            }
        }
        // 移除重复调用的调试日志
    }

    /**
     * 获取当前引擎状态（同步方法）
     */
    fun getCurrentStatus(): EngineStatus = engineStatus.value

    /**
     * 检查引擎是否已就绪
     */
    fun isReady(): Boolean = engineStatus.value == EngineStatus.READY

    /**
     * 预热引擎（可选调用）
     */
    suspend fun warmUp() {
        // 现在初始化流程会自动调用预热，此方法可保留为公共API供手动调用
        bgeEngine.warmUp()
    }

    /**
     * 获取嵌入服务（确保引擎已初始化）
     */
    fun getEmbeddingEngine(): BgeEmbeddingEngine = bgeEngine
}

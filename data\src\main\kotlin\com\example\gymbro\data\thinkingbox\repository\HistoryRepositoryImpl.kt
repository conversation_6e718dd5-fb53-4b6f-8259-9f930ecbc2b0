package com.example.gymbro.data.thinkingbox.repository

import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.local.entity.ChatRaw
import com.example.gymbro.domain.thinkingbox.model.ThinkingFinalEntity
import com.example.gymbro.domain.thinkingbox.model.ThinkingHistoryComplete
import com.example.gymbro.domain.thinkingbox.model.ThinkingMessageEntity
import com.example.gymbro.domain.thinkingbox.model.ThinkingPhaseEntity
import com.example.gymbro.domain.thinkingbox.repository.HistoryRepository
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * HistoryRepository 实现类
 *
 * 🔥 集成现有ROOM系统：使用ChatRaw表的thinking_nodes字段存储ThinkingBox历史
 * 符合HISTORY_README.md中的ROOM直写方案
 *
 * 🔥 重构：移到data层正确位置，符合Clean Architecture
 */
@Singleton
class HistoryRepositoryImpl
@Inject
constructor(
    private val chatRawDao: com.example.gymbro.data.coach.dao.ChatRawDao,
    // 🔥 【单一数据源修复】移除MessageEventDao依赖，统一使用ChatRaw架构
) : HistoryRepository {
    private val TAG = "TB-HISTORY-REPO"

    // 🔥 【单一数据源修复】移除双查策略，统一使用ChatRaw表

    override suspend fun insertThinkingMessage(
        messageId: String,
        status: String,
        startedAt: Long,
        finishedAt: Long?,
        durationMs: Long?,
        tokenCount: Int?,
    ) {
        // 🔥 【单一数据源修复】统一使用ChatRaw表查找AI消息
        val aiMessage = chatRawDao.getMessageById(messageId)

        if (aiMessage != null && aiMessage.role == ChatRaw.ROLE_ASSISTANT) {
            // 🔥 【单一数据源修复】更新ChatRaw表的metadata字段
            val metadata =
                mutableMapOf<String, Any>(
                    "thinking_status" to status,
                    "thinking_started_at" to startedAt,
                )

            finishedAt?.let { metadata["thinking_finished_at"] = it }
            durationMs?.let { metadata["thinking_duration_ms"] = it }
            tokenCount?.let { metadata["thinking_token_count"] = it }

            val updatedMessage =
                aiMessage.copy(
                    metadata = aiMessage.metadata + metadata,
                )
            chatRawDao.updateChatMessage(updatedMessage)
            Timber.tag(TAG).d("💾 [单一数据源] 更新ChatRaw表thinking元数据: $messageId ($status)")
        } else {
            Timber.tag(TAG).w("⚠️ 在ChatRaw表未找到AI消息: $messageId")
        }
    }

    override suspend fun updateThinkingMessage(
        messageId: String,
        status: String,
        finishedAt: Long,
        durationMs: Long,
        tokenCount: Int,
    ) {
        // 调用insert方法更新metadata
        insertThinkingMessage(
            messageId = messageId,
            status = status,
            startedAt = 0L, // 保持原值
            finishedAt = finishedAt,
            durationMs = durationMs,
            tokenCount = tokenCount,
        )
    }

    override suspend fun insertThinkingPhase(
        messageId: String,
        phaseId: String,
        title: String,
        content: String,
        complete: Boolean,
    ) {
        // 🔥 【单一数据源修复】直接通过messageId查找AI消息
        val aiMessage = chatRawDao.getMessageById(messageId)

        if (aiMessage != null && aiMessage.role == ChatRaw.ROLE_ASSISTANT) {
            // 解析现有的thinking_nodes JSON
            val thinkingNodesJson = aiMessage.thinkingNodes
            val existingNodes =
                try {
                    if (thinkingNodesJson.isNullOrBlank()) {
                        mutableListOf<Map<String, Any>>()
                    } else {
                        kotlinx.serialization.json.Json
                            .decodeFromString<MutableList<Map<String, Any>>>(thinkingNodesJson)
                    }
                } catch (e: Exception) {
                    mutableListOf<Map<String, Any>>()
                }

            // 添加新的阶段节点
            val phaseNode =
                mapOf(
                    "type" to "phase",
                    "phase_id" to phaseId,
                    "title" to title,
                    "content" to content,
                    "complete" to complete,
                    "timestamp" to System.currentTimeMillis(),
                )
            existingNodes.add(phaseNode)

            // 更新thinking_nodes字段
            val updatedNodesJson =
                kotlinx.serialization.json.Json
                    .encodeToString(existingNodes)
            val updatedMessage = aiMessage.copy(thinkingNodes = updatedNodesJson)

            chatRawDao.updateChatMessage(updatedMessage)
            Timber.tag(TAG).d("💾 更新thinking_nodes: $messageId - $phaseId: $title")
        } else {
            Timber.tag(TAG).w("⚠️ 未找到对应的AI消息进行阶段更新: $messageId")
        }
    }

    override suspend fun insertThinkingFinal(
        messageId: String,
        markdown: String,
    ) {
        // 🔥 【单一数据源修复】统一使用ChatRaw表查找AI消息
        val aiMessage = chatRawDao.getMessageById(messageId)

        if (aiMessage != null && aiMessage.role == ChatRaw.ROLE_ASSISTANT) {
            // 🔥 【单一数据源修复】只更新ChatRaw表的finalMarkdown字段
            val updatedMessage = aiMessage.copy(finalMarkdown = markdown)
            chatRawDao.updateChatMessage(updatedMessage)
            Timber
                .tag(TAG)
                .d("💾 [单一数据源] 更新ChatRaw表final_markdown: $messageId (${markdown.length} chars)")
        } else {
            Timber.tag(TAG).w("⚠️ 在ChatRaw表未找到AI消息: $messageId")
        }
    }

    override suspend fun getThinkingMessage(messageId: String): ThinkingMessageEntity? {
        // 🔥 【MessageId一致性修复】直接通过messageId查询，而不是通过会话过滤
        val aiMessage = chatRawDao.getMessageById(messageId)

        if (aiMessage == null || aiMessage.role != ChatRaw.ROLE_ASSISTANT) {
            return null
        }

        return aiMessage.let { message ->
            val metadata = message.metadata
            ThinkingMessageEntity(
                messageId = messageId,
                status = metadata["thinking_status"] as? String ?: "unknown",
                startedAt = metadata["thinking_started_at"] as? Long ?: message.timestamp,
                finishedAt = metadata["thinking_finished_at"] as? Long,
                durationMs = metadata["thinking_duration_ms"] as? Long,
                tokenCount = metadata["thinking_token_count"] as? Int,
            )
        }
    }

    override suspend fun getThinkingPhases(messageId: String): List<ThinkingPhaseEntity> {
        // 🔥 【单一数据源修复】直接通过messageId查找AI消息
        val aiMessage = chatRawDao.getMessageById(messageId)

        return aiMessage?.thinkingNodes?.let { nodesJson ->
            try {
                val nodes =
                    kotlinx.serialization.json.Json
                        .decodeFromString<List<Map<String, Any>>>(nodesJson)
                nodes.filter { it["type"] == "phase" }.map { node ->
                    ThinkingPhaseEntity(
                        messageId = messageId,
                        phaseId = node["phase_id"] as? String ?: "",
                        title = node["title"] as? String ?: "",
                        content = node["content"] as? String ?: "",
                        complete = node["complete"] as? Boolean ?: false,
                    )
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "解析thinking_nodes失败: $messageId")
                emptyList()
            }
        } ?: emptyList()
    }

    override suspend fun getThinkingFinal(messageId: String): ThinkingFinalEntity? {
        // 🔥 【MessageId一致性修复】直接通过messageId查询，而不是通过会话过滤
        val aiMessage = chatRawDao.getMessageById(messageId)

        if (aiMessage == null || aiMessage.role != ChatRaw.ROLE_ASSISTANT) {
            return null
        }

        return aiMessage?.thinkingNodes?.let { nodesJson ->
            try {
                val nodes =
                    kotlinx.serialization.json.Json
                        .decodeFromString<List<Map<String, Any>>>(nodesJson)
                val finalNode = nodes.find { it["type"] == "final" }
                finalNode?.let {
                    ThinkingFinalEntity(
                        messageId = messageId,
                        markdown = it["markdown"] as? String ?: "",
                    )
                }
            } catch (e: Exception) {
                Timber.tag(TAG).e(e, "解析thinking_final失败: $messageId")
                null
            }
        }
    }

    override suspend fun getCompleteThinkingHistory(messageId: String): ThinkingHistoryComplete? {
        val message = getThinkingMessage(messageId) ?: return null
        val phases = getThinkingPhases(messageId)
        val final = getThinkingFinal(messageId)

        return ThinkingHistoryComplete(
            message = message,
            phases = phases,
            final = final,
        )
    }
}

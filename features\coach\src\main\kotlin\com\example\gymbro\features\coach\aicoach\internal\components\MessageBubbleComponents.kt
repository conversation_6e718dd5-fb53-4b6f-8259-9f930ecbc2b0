package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.coachTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.coach.aicoach.AiCoachContract

/**
 * 用户消息气泡 - 内部实现，不依赖shared组件
 */
@Composable
internal fun UserMessageBubble(
    message: AiCoachContract.MessageUi,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.End,
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(0.85f),
            horizontalAlignment = Alignment.End,
        ) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors =
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primary,
                ),
                shape =
                RoundedCornerShape(
                    topStart = 16.dp,
                    topEnd = 4.dp,
                    bottomStart = 16.dp,
                    bottomEnd = 16.dp,
                ),
            ) {
                Text(
                    text = message.content,
                    modifier = Modifier.padding(12.dp),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onPrimary,
                )
            }

            // 🔥 保存状态指示器
            SaveStatusIndicator(
                saveStatus = message.saveStatus,
                modifier = Modifier.padding(top = 4.dp, end = 8.dp),
            )
        }
    }
}

/**
 * 保存状态指示器组件
 */
@Composable
internal fun SaveStatusIndicator(
    saveStatus: AiCoachContract.SaveStatus,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp),
    ) {
        when (saveStatus) {
            AiCoachContract.SaveStatus.PENDING -> {
                Icon(
                    imageVector = Icons.Default.Schedule,
                    contentDescription = "等待保存",
                    tint = MaterialTheme.coachTheme.iconTertiary,
                    modifier = Modifier.size(Tokens.Icon.Tiny),
                )
                Text(
                    text = "等待保存",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.coachTheme.textTertiary,
                )
            }

            AiCoachContract.SaveStatus.SAVING -> {
                CircularProgressIndicator(
                    modifier = Modifier.size(Tokens.Icon.Tiny),
                    strokeWidth = Tokens.Spacing.Tiny,
                    color = MaterialTheme.coachTheme.accentPrimary,
                )
                Text(
                    text = "保存中...",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.coachTheme.accentPrimary,
                )
            }

            AiCoachContract.SaveStatus.SAVED -> {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "已保存",
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.size(12.dp),
                )
                Text(
                    text = "已保存",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.primary,
                )
            }

            AiCoachContract.SaveStatus.FAILED -> {
                Icon(
                    imageVector = Icons.Default.Error,
                    contentDescription = "保存失败",
                    tint = MaterialTheme.colorScheme.error,
                    modifier = Modifier.size(12.dp),
                )
                Text(
                    text = "保存失败",
                    style = MaterialTheme.typography.labelSmall,
                    color = MaterialTheme.colorScheme.error,
                )
            }
        }
    }
}

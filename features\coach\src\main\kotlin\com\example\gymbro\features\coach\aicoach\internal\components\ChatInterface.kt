package com.example.gymbro.features.coach.aicoach.internal.components

// ThinkingBox unified integration - using standard export interface
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.aicoach.AiCoachViewModel
import com.example.gymbro.features.coach.shared.components.CoachAnimations
import com.example.gymbro.features.thinkingbox.ThinkingBox
// 🔥 【数据持久化修复】添加JSON处理导入
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import kotlinx.serialization.json.buildJsonArray

/**
 * 🔥 【数据持久化修复】构建思考节点JSON数据
 *
 * 将ThinkingBox的UiState转换为JSON格式，用于保存到数据库
 * 包含预思考内容和所有阶段内容
 */
private fun buildThinkingNodesJson(
    thinkingState: com.example.gymbro.features.thinkingbox.domain.interfaces.UiState,
): String {
    return try {
        buildJsonArray {
            // 🔥 添加预思考内容（如果存在）
            thinkingState.preThinking?.takeIf { it.isNotBlank() }?.let { preThinking ->
                add(
                    buildJsonObject {
                        put("type", "phase")
                        put("phase_id", "perthink")
                        put("title", "Bro is thinking")
                        put("content", preThinking)
                        put("complete", true)
                        put("timestamp", System.currentTimeMillis())
                    },
                )
            }

            // 🔥 添加所有阶段内容
            thinkingState.phases.forEach { phase ->
                add(
                    buildJsonObject {
                        put("type", "phase")
                        put("phase_id", phase.id)
                        put("title", phase.title ?: "")
                        put("content", phase.content)
                        put("complete", true)
                        put("timestamp", System.currentTimeMillis())
                    },
                )
            }

            // 🔥 添加最终内容（如果存在）
            thinkingState.finalMarkdown?.takeIf { it.isNotBlank() }?.let { finalMarkdown ->
                add(
                    buildJsonObject {
                        put("type", "final")
                        put("markdown", finalMarkdown)
                        put("timestamp", System.currentTimeMillis())
                    },
                )
            }
        }.toString()
    } catch (e: Exception) {
        timber.log.Timber.tag("ThinkingBox").e(e, "构建思考节点JSON失败")
        "[]" // 返回空数组作为降级方案
    }
}

/**
 * 聊天界面组件 - 简化的ChatGPT式实现
 *
 * 重构变更：
 * - 移除复杂的BottomSheetScaffold架构
 * - 简化为纯消息列表显示
 * - 基于状态驱动的条件渲染
 * - 移除冗余的FAB管理逻辑
 *
 * 职责：
 * - 消息列表显示和滚动管理
 * - 智能建议面板触发
 * - 输入提示系统
 *
 * @param state AI Coach 状态
 * @param onIntent Intent 分发函数
 * @param modifier 修饰符
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
internal fun ChatInterface(
    state: AiCoachContract.State,
    onIntent: (AiCoachContract.Intent) -> Unit,
    viewModel: AiCoachViewModel?,
    chatListState: LazyListState? = null,
    isCanvasLocked: Boolean = false,
    modifier: Modifier = Modifier,
) {
    val listState = chatListState ?: rememberLazyListState()
    val stableOnIntent = remember(onIntent) { onIntent }

    // 🔥 【704coach优化】Week 2: 获取历史消息分页数据
    val historyPagingItems = viewModel?.historyFlow?.collectAsLazyPagingItems()

    ChatInterfaceContent(
        state = state,
        listState = listState,
        onIntent = stableOnIntent,
        viewModel = viewModel,
        historyPagingItems = historyPagingItems,
        isCanvasLocked = isCanvasLocked,
        modifier =
        modifier
            .fillMaxSize(),
    )
}

/**
 * 聊天界面主要内容组件
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun ChatInterfaceContent(
    state: AiCoachContract.State,
    listState: LazyListState,
    onIntent: (AiCoachContract.Intent) -> Unit,
    viewModel: AiCoachViewModel?,
    historyPagingItems: LazyPagingItems<AiCoachContract.MessageUi>?,
    isCanvasLocked: Boolean = false,
    modifier: Modifier = Modifier,
) {
    // 🔥 【用户最佳阅读体验】在画布锁定期间禁用用户滑动交互
    val scrollModifier = if (isCanvasLocked) {
        Modifier // 禁用滑动，保持固定位置
    } else {
        Modifier // 允许正常滚动交互
    }

    Column(modifier = modifier) {
        // 🔥 门槛2：用户资料提示卡片
        // TODO: 重新实现ProfilePromptCard组件
        // ProfilePromptCard(
        //     isVisible = state.profilePromptState.shouldShow,
        //     onNavigateToProfile = {
        //         // onIntent(AiCoachContract.Intent.NavigateToProfile)
        //     },
        //     onDismiss = {
        //         // onIntent(AiCoachContract.Intent.DismissProfilePrompt)
        //     },
        //     modifier = Modifier.fillMaxWidth(),
        // )

        // 🔥 【704coach优化】Week 2: 智能显示逻辑修复
        val hasHistoryData = historyPagingItems != null && historyPagingItems.itemCount > 0
        val hasCurrentMessages = state.messages.isNotEmpty()
        // 🔥 修复：只有当有实际消息内容时才显示消息列表，而不是仅仅有会话

        if (hasHistoryData || hasCurrentMessages) {
            timber.log.Timber
                .tag("UI-DEBUG")
                .d(
                    "🔍 [UI-DEBUG] ChatInterface 显示消息列表: 历史=${historyPagingItems?.itemCount ?: 0}条, 当前=${state.messages.size}条",
                )
            // 🔥 Week 2: 改造为双数据源消息列表
            OptimizedChatMessageList(
                state = state,
                listState = listState,
                onIntent = onIntent,
                viewModel = viewModel,
                historyPagingItems = historyPagingItems,
                modifier = Modifier.fillMaxSize().then(scrollModifier),
            )
        } else {
            timber.log.Timber
                .tag("UI-DEBUG")
                .d("🔍 [UI-DEBUG] ChatInterface 显示欢迎界面 (无历史和当前消息)")
            // 🎯 空状态：WelcomeScreen在主内容区域显示（不在输入容器中）
            WelcomeScreen(
                suggestionConfig = state.suggestionConfig,
                suggestionChipsVisible = state.suggestionChipsVisible,
                onSuggestionClick = { suggestion ->
                    // 标签点击预填入input
                    onIntent(AiCoachContract.Intent.UpdateInput(suggestion))
                },
                onHideSuggestionChips = {
                    // 🔥 【SuggestionChip交互】隐藏建议标签
                    onIntent(AiCoachContract.Intent.HideSuggestionChips)
                },
                modifier = Modifier.fillMaxSize(),
            )
        }
    }
}

/**
 * 🔥 【704coach优化】Week 2: 优化后的双数据源消息列表（保守重构版本）
 *
 * 基于Box+LazyColumn架构，遵循单轴滚动原则
 * 集成历史消息分页和实时流式消息，明确messages与historyFlow的职责分工
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun OptimizedChatMessageList(
    state: AiCoachContract.State,
    listState: LazyListState,
    onIntent: (AiCoachContract.Intent) -> Unit,
    viewModel: AiCoachViewModel?,
    historyPagingItems: LazyPagingItems<AiCoachContract.MessageUi>?,
    modifier: Modifier = Modifier,
) {
    val currentMessages = state.messages

    // 🔍 调试日志：数据源状态
    LaunchedEffect(currentMessages.size, historyPagingItems?.itemCount) {
        timber.log.Timber
            .tag("UI-DEBUG")
            .d("🔍 [UI-DEBUG] OptimizedChatMessageList 数据源状态:")
        timber.log.Timber
            .tag("UI-DEBUG")
            .d("🔍 [UI-DEBUG] - 历史消息: ${historyPagingItems?.itemCount ?: 0}条")
        timber.log.Timber
            .tag("UI-DEBUG")
            .d("🔍 [UI-DEBUG] - 当前消息: ${currentMessages.size}条")
        timber.log.Timber
            .tag("UI-DEBUG")
            .d(
                "🔍 [UI-DEBUG] - 当前状态: activeSession=${state.activeSession?.id}, streamingState=${state.streamingState::class.simpleName}",
            )
    }

    // Calculate basic state for UI rendering
    val totalItemCount = (historyPagingItems?.itemCount ?: 0) + currentMessages.size
    val isCurrentlyStreaming = state.streamingState !is AiCoachContract.StreamingState.Idle

    val actualItemCount = totalItemCount + if (isCurrentlyStreaming) 1 else 0

    // 🔥 【调试日志】记录ThinkingBox显示决策
    timber.log.Timber.tag("Coach-ThinkingBox").d(
        "🔍 [显示决策] isStreaming=$isCurrentlyStreaming, " +
            "thinkingState: phases=${state.thinkingState.phases.size}, " +
            "activePhaseId=${state.thinkingState.activePhaseId}",
    )

    // Track user manual scrolling to prevent auto-scroll interference
    var hasUserScrolled by remember { mutableStateOf(false) }
    var lastAutoScrollTrigger by remember { mutableStateOf("") }

    // 🔥 【检测用户手动滚动】监听滚动状态变化
    LaunchedEffect(listState.firstVisibleItemIndex, listState.firstVisibleItemScrollOffset) {
        // 如果用户滚动到了非最新位置（index > 2），认为是手动滚动
        if (listState.firstVisibleItemIndex > 2 ||
            (listState.firstVisibleItemIndex > 0 && listState.firstVisibleItemScrollOffset > 100)
        ) {
            hasUserScrolled = true
            timber.log.Timber
                .tag("UI-SCROLL")
                .d(
                    "User manual scroll detected: index=${listState.firstVisibleItemIndex}, offset=${listState.firstVisibleItemScrollOffset}",
                )
        }
    }

    // Auto-scroll only when conversation turns are completed
    LaunchedEffect(currentMessages.size, isCurrentlyStreaming) {
        val currentTrigger = "${currentMessages.size}_$isCurrentlyStreaming"

        // Trigger conditions: new conversation turn completed && user hasn't scrolled manually && not duplicate trigger
        val shouldAutoScroll =
            actualItemCount > 0 &&
                !hasUserScrolled &&
                currentTrigger != lastAutoScrollTrigger &&
                (currentMessages.size > 0) // Ensure there's actual content

        if (shouldAutoScroll) {
            timber.log.Timber
                .tag("UI-SCROLL")
                .d(
                    "Auto-scroll triggered: messages=${currentMessages.size}, showThinkingBox=$isCurrentlyStreaming",
                )

            // Calculate optimal scroll position for comfortable viewing
            val optimalPosition =
                calculateOptimalScrollPosition(
                    totalItems = actualItemCount,
                    hasThinkingCard = isCurrentlyStreaming,
                )

            listState.animateScrollToItem(
                index = optimalPosition,
                scrollOffset = -80, // Extra offset to prevent content from touching edges
            )

            lastAutoScrollTrigger = currentTrigger

            timber.log.Timber
                .tag("UI-SCROLL")
                .d("🔍 [一次性定位] 滚动到位置: index=$optimalPosition, trigger=$currentTrigger")
        }
    }

    // 🔥 单轴滚动架构：遵循Box+LazyColumn原则，使用反向布局实现ChatGPT体验
    LazyColumn(
        modifier = modifier.fillMaxWidth(),
        state = listState,
        // 🔥 【关键】: ChatGPT式反向布局，最新消息在底部
        reverseLayout = true,
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        contentPadding =
        PaddingValues(
            start = Tokens.Spacing.XLarge, // 🔥 【宽度优化】32dp，约2行字符间距，提供更好的阅读体验
            end = Tokens.Spacing.XLarge, // 🔥 【宽度优化】32dp，约2行字符间距，提供更好的阅读体验
            top = Tokens.Spacing.Small, // 🔥 【顶部空白修复】减少顶部padding，避免过多空白
            bottom = Tokens.Spacing.Massive + Tokens.Spacing.XXLarge, // 🔥 【滚动空间优化】适度的底部空间，确保滚动体验但不过度
        ),
    ) {
        // Debug logging for ThinkingBox display state
        timber.log.Timber
            .tag("UI-DEBUG")
            .d("ThinkingBox display: streaming=$isCurrentlyStreaming")

        // Bottom spacing for optimal scroll experience
        item(key = "bottom_spacer") {
            Spacer(modifier = Modifier.height(Tokens.Card.HeightSmall))
        }

        // Current AI response with unified ThinkingBox component
        // 🔥 【点火信号修复】更精确的渲染条件和messageId获取
        val streamingMessageId = when (val streamingState = state.streamingState) {
            is AiCoachContract.StreamingState.Thinking -> streamingState.messageId
            is AiCoachContract.StreamingState.AwaitingFirstToken -> state.messages.lastOrNull { !it.isFromUser }?.id
            else -> null
        }
        
        if (streamingMessageId != null) {
            item(key = "current-thinking-box-$streamingMessageId") {
                // 🔥 【点火信号修复】验证ThinkingBox实例唯一性
                LaunchedEffect(streamingMessageId, state.streamingState) {
                    timber.log.Timber.tag("ThinkingBox-Instance")
                        .i(
                            "🔍 [点火信号] ThinkingBox激活: messageId=$streamingMessageId, streamingState=${state.streamingState::class.simpleName}",
                        )
                    timber.log.Timber.tag("ThinkingBox-Instance")
                        .i(
                            "🔍 [点火信号] thinking状态: isStreaming=${state.thinkingState.isStreaming}, phases=${state.thinkingState.phases.size}",
                        )
                }

                // 🔥 【重复渲染修复】记录ThinkingBox完成状态，防止重复回调
                var thinkingBoxCompleted by remember(streamingMessageId) { mutableStateOf(false) }

                // 🔥 【防重复实例】验证ThinkingBox唯一性
                LaunchedEffect(streamingMessageId) {
                    timber.log.Timber.tag("ThinkingBox-Instance")
                        .w(
                            "🚨 [实例创建] ThinkingBox实例创建/重建: messageId=$streamingMessageId, completed=$thinkingBoxCompleted",
                        )
                }

                // 🔥 移除噪音日志：ThinkingBox渲染调试信息
                
                ThinkingBox(
                    messageId = streamingMessageId,
                    tokenizerService = viewModel?.getTokenizerService(),
                    onDataReady = { data ->
                        // 🔥 【重复渲染修复】防止重复触发消息完成回调
                        if (!thinkingBoxCompleted) {
                            thinkingBoxCompleted = true

                            timber.log.Timber
                                .tag("ThinkingBox-Instance")
                                .w(
                                    "🎯 [回调唯一性] ThinkingBox消息完成回调触发: messageId=${data.messageId}, finalMarkdown长度=${data.finalMarkdown.length}",
                                )
                            timber.log.Timber
                                .tag("ThinkingBox-Instance")
                                .w(
                                    "🎯 [回调唯一性] 回调触发时机验证: isStreaming=$isCurrentlyStreaming, completed=$thinkingBoxCompleted",
                                )

                            timber.log.Timber
                                .tag("ThinkingBox-Instance")
                                .d("🔥 [数据持久化] 思考节点数据: duration=${data.duration}, tokens=${data.tokenCount}")

                            // 🔥 【保存修复】触发AI消息保存，包含完整数据
                            state.activeSession?.let { session ->
                                // 查找对应的AI消息
                                val aiMessage = state.messages.find { it.id == data.messageId && !it.isFromUser }
                                if (aiMessage != null) {
                                    onIntent(
                                        AiCoachContract.Intent.SaveAiMessage(
                                            sessionId = session.id,
                                            aiResponseId = data.messageId,
                                            content = aiMessage.content.ifBlank { data.finalMarkdown },
                                            finalMarkdown = data.finalMarkdown,
                                            thinkingNodes = data.thinkingProcess,
                                        ),
                                    )
                                    timber.log.Timber
                                        .tag("ThinkingBox-Instance")
                                        .w("🔥 [保存唯一性] AI消息保存触发成功: messageId=${data.messageId}, finalMarkdown长度=${data.finalMarkdown.length}")
                                } else {
                                    timber.log.Timber
                                        .tag("ThinkingBox-Instance")
                                        .e("🚨 [保存错误] 未找到对应的AI消息: messageId=${data.messageId}, 现有消息=${state.messages.map { "${it.id}(${if (it.isFromUser) "user" else "ai"})" }}")
                                }
                            } ?: run {
                                timber.log.Timber
                                    .tag("ThinkingBox-Instance")
                                    .e("🚨 [保存错误] activeSession为null: messageId=${data.messageId}")
                            }
                        } else {
                            timber.log.Timber
                                .tag("ThinkingBox-Instance")
                                .e(
                                    "🚨 [重复回调阻止] ThinkingBox回调重复触发被阻止: messageId=${data.messageId}, 这可能表示存在重复渲染!",
                                )
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                )
            }
        } else {
            // 🔥 【关键调试】ThinkingBox 没有被渲染的原因  
            // 调试信息已移至更高级别的日志管理
        }

        // 🔥 【多轮对话修复】显示所有消息：用户消息 + 已完成的AI响应
        // 🔥 修复：移除 isStreaming 字段后，所有在 messages 列表中的消息都是已完成的
        val allVisibleMessages = currentMessages // 所有消息都是已完成的

        // 🔥 修复：检查key重复问题
        val allKeys = mutableListOf<String>()
        allVisibleMessages.forEach { message ->
            allKeys.add(
                "current_${message.id}_${message.timestamp}_${if (message.isFromUser) "user" else "ai"}",
            )
        }
        historyPagingItems?.itemSnapshotList?.items?.forEach { msg ->
            if (msg.isFromUser) {
                allKeys.add("history_${msg.id}_${msg.timestamp}_user")
            }
        }
        val duplicateKeys = allKeys.groupingBy { it }.eachCount().filter { it.value > 1 }
        if (duplicateKeys.isNotEmpty()) {
            timber.log.Timber.tag("HISTORY-FIX").e("❌ [Key重复检测] ChatInterface发现重复keys: $duplicateKeys")
        } else {
            timber.log.Timber.tag("HISTORY-FIX").d("✅ [Key重复检测] ChatInterface所有keys唯一，共${allKeys.size}个")
        }

        // 🔥 【调试】记录过滤结果
        timber.log.Timber
            .tag("UI-DEBUG")
            .d("🔍 [多轮对话修复] 当前消息总数: ${currentMessages.size}, 可见消息: ${allVisibleMessages.size}")
        allVisibleMessages.forEach { msg ->
            timber.log.Timber
                .tag("UI-DEBUG")
                .d(
                    "🔍 [消息详情] id=${msg.id}, isFromUser=${msg.isFromUser}, saveStatus=${msg.saveStatus}, finalMarkdown长度=${msg.finalMarkdown?.length}",
                )
        }

        items(
            items = allVisibleMessages.reversed(), // 反向以适应reverseLayout
            key = { message ->
                // 🔥 修复：确保key的绝对唯一性，避免历史记录加载时的key冲突
                "current_${message.id}_${message.timestamp}_${if (message.isFromUser) "user" else "ai"}"
            },
            contentType = { message -> if (message.isFromUser) "user_message" else "ai_message" },
        ) { message ->
            AnimatedVisibility(
                visible = true,
                enter = CoachAnimations.messageEnterAnimation,
                exit = CoachAnimations.messageExitAnimation,
            ) {
                when {
                    message.isFromUser -> {
                        // 🔥 用户消息使用气泡布局，右对齐
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.End,
                        ) {
                            UserMessageBubble(
                                message = message,
                                modifier = Modifier.fillMaxWidth(0.85f), // 限制用户消息宽度
                            )
                        }
                    }

                    !message.isFromUser && message.saveStatus == AiCoachContract.SaveStatus.SAVED -> {
                        // 🔥 AI消息直接全宽渲染，不使用气泡布局
                        val aiContent: String = message.finalMarkdown?.takeIf { it.isNotBlank() } ?: message.content
                        if (aiContent.isNotBlank()) {
                            HistoricalAiResponseRenderer(
                                message = message,
                                onShowSummaryCard = { messageId ->
                                    onIntent(AiCoachContract.Intent.ShowSummaryCard(messageId))
                                },
                                modifier = Modifier.fillMaxWidth(), // 🔥 AI消息全宽显示
                            )
                        }
                    }
                }
            }
        }

        // Historical messages (paginated loading)
        if (historyPagingItems != null) {
            val itemCount = historyPagingItems.itemCount
            items(itemCount) { index ->
                val message = historyPagingItems[index]
                message?.let { msg ->
                    // 🔥 【调试】记录历史消息详情
                    timber.log.Timber
                        .tag("UI-DEBUG")
                        .d(
                            "🔍 [历史消息] id=${msg.id}, isFromUser=${msg.isFromUser}, content=${
                                msg.content.take(
                                    50,
                                )
                            }",
                        )

                    // 🔥 【修复】渲染所有历史消息，但区分用户和AI消息的布局
                    key("history_${msg.id}_${msg.timestamp}_${if (msg.isFromUser) "user" else "ai"}") {
                        AnimatedVisibility(
                            visible = true,
                            enter = CoachAnimations.historyMessageEnterAnimation,
                            exit = CoachAnimations.messageExitAnimation,
                        ) {
                            when {
                                msg.isFromUser -> {
                                    // 🔥 用户消息使用气泡布局，右对齐
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.End,
                                    ) {
                                        UserMessageBubble(
                                            message = msg,
                                            modifier = Modifier.fillMaxWidth(0.85f), // 限制用户消息宽度
                                        )
                                    }
                                }
                                !msg.isFromUser -> {
                                    // 🔥 AI消息直接全宽渲染，不使用气泡布局
                                    val aiContent: String = msg.finalMarkdown?.takeIf { it.isNotBlank() } ?: msg.content
                                    if (aiContent.isNotBlank()) {
                                        HistoricalAiResponseRenderer(
                                            message = msg,
                                            onShowSummaryCard = { messageId ->
                                                onIntent(AiCoachContract.Intent.ShowSummaryCard(messageId))
                                            },
                                            modifier = Modifier.fillMaxWidth(), // 🔥 AI消息全宽显示
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Top spacing to prevent first message from touching edges
        item(key = "top_spacer") {
            Spacer(modifier = Modifier.height(32.dp))
        }
    } // LazyColumn end
}

/**
 * Message item renderer - 已弃用，现在直接使用 UserMessageBubble
 * AI responses are handled by HistoricalAiResponseRenderer
 */
@Deprecated(
    "Use UserMessageBubble directly for user messages and HistoricalAiResponseRenderer for AI messages",
)
@Composable
private fun RenderMessageItem(
    message: AiCoachContract.MessageUi,
    onIntent: (AiCoachContract.Intent) -> Unit,
    viewModel: AiCoachViewModel?,
    modifier: Modifier = Modifier,
) {
    // 只处理用户消息，AI消息应该使用 HistoricalAiResponseRenderer
    if (message.isFromUser) {
        UserMessageBubble(
            message = message,
            modifier = modifier,
        )
    }
}

// All ThinkingBox-related functionality has been unified into the ThinkingBox module

/**
 * Calculate optimal scroll position for comfortable viewing
 *
 * @param totalItems Total number of items in the list
 * @param hasThinkingCard Whether ThinkingBox is currently displayed
 * @return Optimal scroll position index
 */
private fun calculateOptimalScrollPosition(
    totalItems: Int,
    hasThinkingCard: Boolean,
): Int {
    // 🔥 【最佳位置算法】
    // reverseLayout=true时，index=0是最新内容
    // 我们希望最新的用户消息+AI响应在屏幕中央偏上位置

    val spacerItems = 2 // bottom_spacer + top_spacer
    val contentItems = totalItems - spacerItems

    return when {
        // Few items: scroll near top with moderate spacing
        contentItems <= 2 -> maxOf(0, totalItems - 3)

        // With ThinkingBox: ensure both user message and ThinkingBox are visible
        hasThinkingCard -> maxOf(0, totalItems - 4)

        // General case: show latest 2-3 messages comfortably
        else -> maxOf(0, totalItems - 3)
    }
}

// All legacy ThinkingBox components have been replaced with the unified ThinkingBox module

// � 【代码清理】移除未使用的buildRawAIResponse函数

// 🔥 【代码拆分】SaveStatusIndicator已移至MessageBubbleComponents.kt

// ===== Preview Components =====

@GymBroPreview
@Composable
private fun OptimizedChatMessageListPreview() {
    MaterialTheme {
        val mockState =
            AiCoachContract.State(
                streamingState = AiCoachContract.StreamingState.Idle,
                thinkingState =
                com.example.gymbro.features.thinkingbox.domain.interfaces
                    .UiState(),
                inputState = AiCoachContract.InputState(placeholder = "输入消息..."),
                // 🔥 【Week 2】模拟活跃会话状态
                activeSession =
                ChatSession(
                    id = "preview-session-1",
                    title = "预览会话",
                    userId = "preview-user",
                    createdAt =
                    kotlinx.datetime.Clock.System
                        .now(),
                ),
            )

        OptimizedChatMessageList(
            state = mockState,
            listState = rememberLazyListState(),
            onIntent = {},
            viewModel = null,
            historyPagingItems = null,
            modifier = Modifier.fillMaxSize(),
        )
    }
}

@GymBroPreview
@Composable
private fun RenderMessageItemPreview() {
    MaterialTheme {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
        ) {
            // User message preview
            RenderMessageItem(
                message =
                AiCoachContract.MessageUi(
                    id = "user1",
                    content = "请帮我分析一下我的训练计划",
                    isFromUser = true,
                    timestamp =
                    kotlinx.datetime.Clock.System
                        .now(),
                    saveStatus = AiCoachContract.SaveStatus.SAVED,
                ),
                onIntent = {},
                viewModel = null,
                modifier = Modifier.fillMaxWidth(),
            )

            // AI message preview
            RenderMessageItem(
                message =
                AiCoachContract.MessageUi(
                    id = "ai1",
                    content = "根据你的训练计划分析，我发现以下几个要点...",
                    isFromUser = false,
                    timestamp =
                    kotlinx.datetime.Clock.System
                        .now(),
                    saveStatus = AiCoachContract.SaveStatus.SAVED,
                    finalMarkdown = "## 训练计划分析\n\n### 优势\n- 计划设置合理\n- 强度适中\n\n### 建议\n- 增加有氧运动",
                ),
                onIntent = {},
                viewModel = null,
                modifier = Modifier.fillMaxWidth(),
            )
        }
    }
}

// 🔥 【代码拆分】HistoricalAiResponseRenderer和StaticAiResponseRenderer已移至AiResponseComponents.kt

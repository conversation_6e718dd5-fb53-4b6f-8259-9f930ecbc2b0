package com.example.gymbro.core.userdata.internal.api

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.userdata.api.UserDataCenterApi
import com.example.gymbro.core.userdata.api.model.SyncStatus
import com.example.gymbro.core.userdata.api.model.UnifiedUserData
import com.example.gymbro.core.userdata.api.model.UserDataState
import com.example.gymbro.core.userdata.internal.repository.UserDataRepository
import com.example.gymbro.core.userdata.internal.synchronizer.UserDataSynchronizer
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.UserProfile
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import javax.inject.Inject
import javax.inject.Singleton

/**
 * UserDataCenter API 实现类
 *
 * 作为 UserDataCenter 模块的对外门面，协调内部各组件提供统一的用户数据服务。
 * 实现了完整的用户数据生命周期管理，包括同步、缓存、错误处理等。
 *
 * 核心职责：
 * - 对外提供统一的用户数据访问接口
 * - 协调 Repository 和 Synchronizer 的工作
 * - 处理错误和异常情况
 * - 管理数据状态和生命周期
 *
 * 设计特点：
 * - 响应式：基于 Flow 的数据流
 * - 容错性：完善的错误处理和恢复机制
 * - 性能优化：智能缓存和懒加载
 * - 线程安全：协程安全的并发控制
 */
@Singleton
class UserDataCenterApiImpl @Inject constructor(
    private val userDataRepository: UserDataRepository,
    private val userDataSynchronizer: UserDataSynchronizer,
    private val logger: Logger,
) : UserDataCenterApi {

    companion object {
        private const val TAG = "UserDataCenterApiImpl"
    }

    // 用于同步器的协程作用域
    private val synchronizerScope = CoroutineScope(SupervisorJob())

    init {
        // 启动数据同步监听
        userDataSynchronizer.startSynchronization(synchronizerScope)
        logger.d(TAG, "UserDataCenter API 初始化完成")
    }

    /**
     * 观察统一用户数据
     *
     * 将 Repository 的数据流转换为带状态的数据流，提供完整的状态信息。
     */
    override fun observeUserData(): Flow<UserDataState<UnifiedUserData>> {
        logger.d(TAG, "开始观察用户数据")

        return userDataRepository.observeUserData()
            .map { userData ->
                when {
                    userData == null -> {
                        logger.d(TAG, "用户数据为空")
                        UserDataState.Empty
                    }
                    userData.syncStatus == SyncStatus.SYNCING -> {
                        logger.d(TAG, "用户数据同步中")
                        UserDataState.Syncing(userData, 0.5f)
                    }
                    userData.syncStatus == SyncStatus.SYNC_FAILED -> {
                        logger.w(TAG, "用户数据同步失败")
                        UserDataState.Error(
                            error = com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                                operationName = "observeUserData",
                                message = com.example.gymbro.core.ui.text.UiText.DynamicString("数据同步失败"),
                                recoverable = true,
                            ),
                            partialData = userData,
                        )
                    }
                    else -> {
                        logger.d(TAG, "用户数据获取成功: userId=${userData.userId}")
                        UserDataState.Success(userData)
                    }
                }
            }
            .onStart {
                logger.d(TAG, "开始发送加载状态")
                emit(UserDataState.Loading)
            }
            .catch { exception ->
                logger.e(exception, TAG, "观察用户数据时发生异常")
                emit(
                    UserDataState.Error(
                        error = com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                            operationName = "observeUserData",
                            message = com.example.gymbro.core.ui.text.UiText.DynamicString(
                                "获取用户数据失败: ${exception.message}",
                            ),
                            recoverable = true,
                        ),
                    ),
                )
            }
    }

    /**
     * 同步认证数据
     *
     * 委托给 UserDataSynchronizer 处理，确保数据同步的一致性。
     */
    override suspend fun syncAuthData(authUser: AuthUser): ModernResult<Unit> {
        logger.d(TAG, "同步认证数据: userId=${authUser.uid}")

        return try {
            userDataSynchronizer.syncAuthData(authUser)
        } catch (e: Exception) {
            logger.e(e, TAG, "同步认证数据时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "syncAuthData",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("同步认证数据失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    /**
     * 更新用户资料
     *
     * 更新用户资料数据，并触发相关的同步操作。
     */
    override suspend fun updateProfile(profileData: UserProfile): ModernResult<Unit> {
        logger.d(TAG, "更新用户资料: userId=${profileData.userId}")

        return try {
            // 更新 Repository 中的数据
            val result = userDataRepository.updateProfileData(profileData.userId, profileData)

            when (result) {
                is ModernResult.Success -> {
                    logger.d(TAG, "用户资料更新成功")
                    ModernResult.Success(Unit)
                }
                is ModernResult.Error -> {
                    logger.e(TAG, "用户资料更新失败: ${result.error}")
                    ModernResult.Error(result.error)
                }
                else -> {
                    logger.w(TAG, "用户资料更新超时")
                    ModernResult.Success(Unit)
                }
            }
        } catch (e: Exception) {
            logger.e(e, TAG, "更新用户资料时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "updateProfile",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("更新用户资料失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    /**
     * 强制同步所有数据
     *
     * 触发完整的数据同步，从各个数据源重新获取最新数据。
     */
    override suspend fun forceSync(): ModernResult<Unit> {
        logger.d(TAG, "开始强制同步所有数据")

        return try {
            userDataSynchronizer.forceSync()
        } catch (e: Exception) {
            logger.e(e, TAG, "强制同步时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "forceSync",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("强制同步失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    /**
     * 获取当前用户数据快照
     *
     * 立即返回当前可用的用户数据，不等待异步更新。
     */
    override suspend fun getCurrentUserData(): ModernResult<UnifiedUserData?> {
        logger.d(TAG, "获取当前用户数据快照")

        return try {
            userDataRepository.getCurrentUserData()
        } catch (e: Exception) {
            logger.e(e, TAG, "获取当前用户数据时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "getCurrentUserData",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("获取用户数据失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    /**
     * 清除用户数据
     *
     * 用户登出时调用，清除所有本地用户数据和缓存。
     */
    override suspend fun clearUserData(): ModernResult<Unit> {
        logger.d(TAG, "清除用户数据")

        return try {
            userDataRepository.clearUserData()
        } catch (e: Exception) {
            logger.e(e, TAG, "清除用户数据时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "clearUserData",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("清除用户数据失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    /**
     * 检查数据同步状态
     *
     * 获取当前的数据同步状态，用于诊断和监控。
     */
    override suspend fun getSyncStatus(): ModernResult<SyncStatus> {
        logger.d(TAG, "检查数据同步状态")

        return try {
            // 获取当前用户数据
            val currentDataResult = userDataRepository.getCurrentUserData()
            when (currentDataResult) {
                is ModernResult.Success -> {
                    val userData = currentDataResult.data
                    if (userData != null) {
                        logger.d(TAG, "当前同步状态: ${userData.syncStatus}")
                        ModernResult.Success(userData.syncStatus)
                    } else {
                        logger.d(TAG, "用户未登录，返回默认同步状态")
                        ModernResult.Success(SyncStatus.SYNCED)
                    }
                }
                is ModernResult.Error -> {
                    logger.e(TAG, "获取同步状态失败: ${currentDataResult.error}")
                    ModernResult.Error(currentDataResult.error)
                }
                else -> {
                    logger.w(TAG, "获取同步状态超时")
                    ModernResult.Success(SyncStatus.SYNCED)
                }
            }
        } catch (e: Exception) {
            logger.e(e, TAG, "检查同步状态时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "getSyncStatus",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("检查同步状态失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    /**
     * 重试失败的同步操作
     *
     * 当数据同步失败时，可以调用此方法重试同步。
     */
    override suspend fun retrySyncIfNeeded(): ModernResult<Unit> {
        logger.d(TAG, "重试失败的同步操作")

        return try {
            userDataSynchronizer.retrySyncIfNeeded()
        } catch (e: Exception) {
            logger.e(e, TAG, "重试同步时发生异常")
            ModernResult.Error(
                com.example.gymbro.core.error.types.business.BusinessErrors.BusinessError.rule(
                    operationName = "retrySyncIfNeeded",
                    message = com.example.gymbro.core.ui.text.UiText.DynamicString("重试同步失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }
}

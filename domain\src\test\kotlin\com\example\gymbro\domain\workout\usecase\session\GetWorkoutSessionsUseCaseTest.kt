package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * GetWorkoutSessionsUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试UseCase的核心业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证Flow返回类型
 * ✔️ 测试用户认证和权限控制
 */
class GetWorkoutSessionsUseCaseTest {

    @MockK
    private lateinit var sessionRepository: SessionRepository

    @MockK
    private lateinit var getCurrentUserIdUseCase: GetCurrentUserIdUseCase

    @MockK
    private lateinit var logger: Logger

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: GetWorkoutSessionsUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = GetWorkoutSessionsUseCase(
            sessionRepository = sessionRepository,
            getCurrentUserIdUseCase = getCurrentUserIdUseCase,
            dispatcher = testDispatcher,
            logger = logger,
        )
    }

    @Test
    fun `createFlow should return user sessions when user is logged in`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val sessions = listOf(
            createSampleWorkoutSession(),
            createSampleWorkoutSession(id = "session-2"),
        )

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))
        coEvery { sessionRepository.getUserSessions(userId) } returns flowOf(ModernResult.Success(sessions))

        // When
        val result = useCase.createFlow(Unit).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        assertEquals(sessions, (result.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { sessionRepository.getUserSessions(userId) }
    }

    @Test
    fun `createFlow should return error when user is not logged in`() = runTest(testDispatcher) {
        // Given
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(null))

        // When
        val result = useCase.createFlow(Unit).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Error)
        assertTrue((result.first() as ModernResult.Error).error is BusinessErrors.BusinessError)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `createFlow should propagate error when getCurrentUserId fails`() = runTest(testDispatcher) {
        // Given
        val authError = mockk<BusinessErrors.BusinessError>()
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Error(authError))

        // When
        val result = useCase.createFlow(Unit).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Error)
        assertEquals(authError, (result.first() as ModernResult.Error).error)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `createFlow should propagate loading state from getCurrentUserId`() = runTest(testDispatcher) {
        // Given
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Loading)

        // When
        val result = useCase.createFlow(Unit).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Loading)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `createFlow should handle repository error gracefully`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val repositoryError = mockk<Exception>()

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))
        coEvery { sessionRepository.getUserSessions(userId) } returns flowOf(ModernResult.Error(repositoryError))

        // When
        val result = useCase.createFlow(Unit).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Error)
        assertEquals(repositoryError, (result.first() as ModernResult.Error).error)
        coVerify(exactly = 1) { sessionRepository.getUserSessions(userId) }
    }

    @Test
    fun `createFlow should handle empty session list`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val emptySessions = emptyList<WorkoutSession>()

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))
        coEvery { sessionRepository.getUserSessions(userId) } returns flowOf(ModernResult.Success(emptySessions))

        // When
        val result = useCase.createFlow(Unit).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        assertEquals(emptySessions, (result.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { sessionRepository.getUserSessions(userId) }
    }

    @Test
    fun `createFlow should handle multiple emissions from repository`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val sessions1 = listOf(createSampleWorkoutSession())
        val sessions2 = listOf(createSampleWorkoutSession(), createSampleWorkoutSession(id = "session-2"))

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))
        coEvery { sessionRepository.getUserSessions(userId) } returns flowOf(
            ModernResult.Success(sessions1),
            ModernResult.Success(sessions2),
        )

        // When
        val result = useCase.createFlow(Unit).toList()

        // Then
        assertEquals(2, result.size)
        assertTrue(result[0] is ModernResult.Success)
        assertTrue(result[1] is ModernResult.Success)
        assertEquals(sessions1, (result[0] as ModernResult.Success).data)
        assertEquals(sessions2, (result[1] as ModernResult.Success).data)
        coVerify(exactly = 1) { sessionRepository.getUserSessions(userId) }
    }

    @Test
    fun `invoke with no parameters should delegate to createFlow`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val sessions = listOf(createSampleWorkoutSession())

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))
        coEvery { sessionRepository.getUserSessions(userId) } returns flowOf(ModernResult.Success(sessions))

        // When
        val result = useCase.invoke().toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        assertEquals(sessions, (result.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { sessionRepository.getUserSessions(userId) }
    }

    @Test
    fun `createFlow should handle multiple users correctly`() = runTest(testDispatcher) {
        // Given
        val userId1 = "user-123"
        val userId2 = "user-456"
        val sessions1 = listOf(createSampleWorkoutSession(id = "session-1"))
        val sessions2 = listOf(createSampleWorkoutSession(id = "session-2"))

        // First call
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId1))
        coEvery { sessionRepository.getUserSessions(userId1) } returns flowOf(ModernResult.Success(sessions1))

        val result1 = useCase.createFlow(Unit).toList()

        // Second call with different user
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId2))
        coEvery { sessionRepository.getUserSessions(userId2) } returns flowOf(ModernResult.Success(sessions2))

        val result2 = useCase.createFlow(Unit).toList()

        // Then
        assertEquals(1, result1.size)
        assertEquals(1, result2.size)
        assertTrue(result1.first() is ModernResult.Success)
        assertTrue(result2.first() is ModernResult.Success)
        assertEquals(sessions1, (result1.first() as ModernResult.Success).data)
        assertEquals(sessions2, (result2.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { sessionRepository.getUserSessions(userId1) }
        coVerify(exactly = 1) { sessionRepository.getUserSessions(userId2) }
    }

    // === 辅助方法 ===

    private fun createSampleWorkoutSession(
        id: String = "session-123",
    ): WorkoutSession {
        return WorkoutSession(
            id = id,
            name = "测试训练会话",
            templateId = null,
            status = "DRAFT",
            startTime = null,
            endTime = null,
            exercises = emptyList(),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
        )
    }
}
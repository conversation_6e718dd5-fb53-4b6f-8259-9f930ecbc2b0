package com.example.gymbro.features.coach.aicoach.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.core.ml.embedding.EngineStatus
import com.example.gymbro.designSystem.components.extras.rememberMetallicBrush
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.coachTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.aicoach.getDisplayName
import com.example.gymbro.features.coach.aicoach.internal.components.DropdownMenuState
import com.example.gymbro.features.coach.aicoach.internal.components.UnifiedSettingsDropdown

/**
 * AI Coach顶部栏组件
 *
 * 从AiCoachScreen拆分出的独立组件，负责顶部导航栏的显示和交互
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
internal fun AiCoachTopBar(
    state: AiCoachContract.State,
    bgeEngineStatus: State<EngineStatus>,
    onBackClick: () -> Unit,
    onHistoryClick: () -> Unit,
    onNewChatClick: () -> Unit,
    onClearSessionClick: () -> Unit,
    onTogglePromptDebug: () -> Unit,
    showPromptDebugPanel: Boolean,
    promptRegistry: com.example.gymbro.core.ai.prompt.registry.PromptRegistry?,
    onSwitchPromptMode: (String) -> Unit,
    onModelSelected: (String) -> Unit = {},
    showDebugPanel: Boolean = false,
    onDebugToggle: (Boolean) -> Unit = {},
    onDebugPageClick: () -> Unit = {},
    // 🔥 激活Debug功能：支持模拟数据和真实执行两种调试模式
    debugScenarios: List<com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.DebugScenario> =
        emptyList(),
    onDebugScenarioSelected: (
        com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.DebugScenario,
    ) -> Unit = {},
    // 🔥 新增：真实执行调试场景
    realExecutionScenarios:
    List<com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.RealExecutionScenario> = emptyList(),
    onRealExecutionSelected: (
        com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.RealExecutionScenario,
    ) -> Unit = {},
    modifier: Modifier = Modifier,
) {
    var showSettingsMenu by remember { mutableStateOf(false) }
    var currentMenuState by remember { mutableStateOf(DropdownMenuState.MAIN) }

    // 🔥 【Box覆盖层架构】实现LOGO绝对居中和BGE状态指示器独立定位
    Box(modifier = modifier) {
        CenterAlignedTopAppBar(
            title = {
                // 🔥 【空占位符】为覆盖层让出空间
                Spacer(modifier = Modifier.height(Tokens.Spacing.Large))
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回",
                        tint = MaterialTheme.coachTheme.iconPrimary, // 🔥 【主题修复】使用Coach主题图标色
                        modifier = Modifier.size(24.dp),
                    )
                }
            },
            actions = {
                IconButton(onClick = onNewChatClick) {
                    Icon(
                        Icons.Filled.Add,
                        contentDescription = "新建对话",
                        tint = MaterialTheme.coachTheme.iconPrimary, // 🔥 【主题修复】使用Coach主题图标色
                        modifier = Modifier.size(24.dp),
                    )
                }

                Box {
                    IconButton(onClick = {
                        showSettingsMenu = !showSettingsMenu
                        currentMenuState = DropdownMenuState.MAIN
                    }) {
                        Icon(
                            Icons.Filled.MoreVert,
                            contentDescription = "更多选项",
                            tint = MaterialTheme.coachTheme.iconPrimary, // 🔥 【主题修复】使用Coach主题图标色
                            modifier = Modifier.size(24.dp),
                        )
                    }

                    if (showSettingsMenu) {
                        UnifiedSettingsDropdown(
                            currentMenuState = currentMenuState,
                            availableProviders = listOf(
                                AiCoachContract.ApiProvider.OPENAI,
                                AiCoachContract.ApiProvider.DEEPSEEK,
                                AiCoachContract.ApiProvider.GOOGLE_GEMINI,
                            ),
                            currentProvider = state.currentApiProvider,
                            getProviderDisplayName = { provider -> provider.getDisplayName() },
                            getProviderModel = { provider ->
                                when (provider) {
                                    AiCoachContract.ApiProvider.OPENAI -> "适用于大多数任务"
                                    AiCoachContract.ApiProvider.DEEPSEEK -> "研究预览"
                                    AiCoachContract.ApiProvider.GOOGLE_GEMINI -> "使用高级推理"
                                }
                            },
                            onProviderSelected = { provider ->
                                val modelId = when (provider) {
                                    AiCoachContract.ApiProvider.OPENAI -> "openai"
                                    AiCoachContract.ApiProvider.DEEPSEEK -> "deepseek"
                                    AiCoachContract.ApiProvider.GOOGLE_GEMINI -> "gemini"
                                }
                                onModelSelected(modelId)
                                showSettingsMenu = false
                                currentMenuState = DropdownMenuState.MAIN
                            },
                            availablePrompts = listOf("standard", "layered", "pipeline", "blank"),
                            currentPrompt = promptRegistry?.getCurrentMode() ?: "standard",
                            getPromptDisplayInfo = { promptId ->
                                when (promptId) {
                                    "layered" -> Pair("分层模式", "传统分层构建器")
                                    "pipeline" -> Pair("流水线模式", "结构化思考过程")
                                    "standard" -> Pair("标准思考模式", "支持阶段化思考指令")
                                    "blank" -> Pair("空白模式", "最小化提示")
                                    else -> Pair(promptId, "")
                                }
                            },
                            onPromptSelected = { promptId ->
                                promptRegistry?.switch(promptId)
                                showSettingsMenu = false
                                currentMenuState = DropdownMenuState.MAIN
                            },
                            sessionCount = state.sessionState.allSessions.size,
                            onHistoryClick = onHistoryClick,
                            showDebugPanel = showDebugPanel,
                            onDebugToggle = onDebugToggle,
                            onDebugPageClick = onDebugPageClick,
                            debugScenarios = debugScenarios,
                            onDebugScenarioSelected = onDebugScenarioSelected,
                            realExecutionScenarios = realExecutionScenarios,
                            onRealExecutionSelected = onRealExecutionSelected,
                            onMenuStateChanged = { newState -> currentMenuState = newState },
                            onDismiss = { showSettingsMenu = false },
                        )
                    }
                }
            },
            colors = TopAppBarDefaults.centerAlignedTopAppBarColors(
                containerColor = MaterialTheme.coachTheme.backgroundElevated, // 🔥 【颜色统一】使用Coach主题提升背景色作为标准
            ),
            modifier = Modifier.fillMaxWidth(),
        )

        // 🔥 【覆盖层 - 绝对居中的LOGO】在整个TopBar宽度中精确居中
        AbsoluteCenteredGymBroLogo(
            modifier = Modifier
                .align(Alignment.Center)
                .fillMaxWidth(),
        )

        // 🔥 【覆盖层 - 左侧BGE状态指示器】独立定位在左侧区域
        TopBarBgeStatusIndicator(
            bgeEngineStatus = bgeEngineStatus,
            modifier = Modifier
                .align(Alignment.CenterStart)
                .padding(start = Tokens.Spacing.XLarge), // 为返回按钮留出空间
        )
    }
}

/**
 * TopBar专用BGE状态指示器
 *
 * 🔥 【实时状态展示】设计要求：
 * - 实时显示BGE引擎工作状态
 * - 使用颜色变化表达状态：绿色工作中，灰色就绪，红色错误
 * - 简化文本显示：统一使用"BGE"三个字
 * - 遵循Token间距系统，100% Token化
 */
@Composable
private fun TopBarBgeStatusIndicator(
    bgeEngineStatus: State<EngineStatus>,
    modifier: Modifier = Modifier,
) {
    val engineStatus by bgeEngineStatus

    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Tiny),
        modifier = modifier,
    ) {
        // 🔥 【实时状态指示点】使用颜色变化表达工作状态
        Box(
            modifier = Modifier
                .size(6.dp)
                .background(
                    color = getBgeRealtimeStatusColor(engineStatus),
                    shape = CircleShape,
                ),
        )

        // 🔥 【简化文本】统一显示"BGE"，颜色表达状态
        Text(
            text = "BGE",
            style = MaterialTheme.typography.labelSmall.copy(
                fontSize = 10.sp,
                fontWeight = FontWeight.Medium,
            ),
            color = getBgeRealtimeStatusColor(engineStatus),
            maxLines = 1,
        )
    }
}

/**
 * 获取BGE实时状态对应的颜色
 *
 * 🔥 【实时状态颜色映射】：
 * - INITIALIZING: 蓝色（工作中/加载中）
 * - READY: 绿色（就绪/可用）
 * - ERROR: 红色（错误/不可用）
 * - UNINITIALIZED: 灰色（未初始化）
 */
@Composable
private fun getBgeRealtimeStatusColor(status: EngineStatus): androidx.compose.ui.graphics.Color {
    return when (status) {
        EngineStatus.INITIALIZING -> MaterialTheme.coachTheme.accentPrimary // 蓝色 - 工作中
        EngineStatus.READY -> Tokens.Color.Success // 绿色 - 就绪可用
        EngineStatus.ERROR -> Tokens.Color.Error // 红色 - 错误状态
        EngineStatus.UNINITIALIZED -> Tokens.Color.Gray500 // 灰色 - 未初始化
    }
}

/**
 * 绝对居中的GymBro LOGO组件
 *
 * 🔥 【Box覆盖层架构】实现LOGO在整个TopBar宽度中的绝对居中
 * - 使用Box覆盖层，完全独立于TopAppBar的title区域
 * - 100% Token化，遵循设计系统规范
 * - 保持金属质感动画效果
 * - 响应式布局适配不同屏幕尺寸
 */
@Composable
private fun AbsoluteCenteredGymBroLogo(
    modifier: Modifier = Modifier,
) {
    // 🔥 【LOGO样式】准备金属质感画刷
    val gymBroMetallicBrush = rememberMetallicBrush(
        useAnimate = true,
        rotationDurationMillis = 2500, // 稍慢的动画，适合标题
        useHdr = false,
    )

    // 🔥 【文本样式】定义LOGO文本样式，100% Token化
    val logoTextStyle = MaterialTheme.typography.titleLarge.copy(
        fontWeight = FontWeight.Bold,
        brush = gymBroMetallicBrush,
    )

    // 🔥 【绝对居中】使用Box的Alignment.Center实现真正的居中
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = "GymBro",
            style = logoTextStyle,
        )
    }
}

// ===== 预览函数 =====

@GymBroPreview
@Composable
private fun TopBarBgeStatusIndicatorPreview() {
    GymBroTheme {
        Surface {
            Column(
                modifier = Modifier.padding(Tokens.Spacing.Medium),
                verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
            ) {
                Text("BGE实时状态指示器预览", style = MaterialTheme.typography.titleMedium)

                // 🔥 【工作中状态】蓝色 - INITIALIZING
                Row(verticalAlignment = Alignment.CenterVertically) {
                    TopBarBgeStatusIndicator(
                        bgeEngineStatus = remember { mutableStateOf(EngineStatus.INITIALIZING) },
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
                    Text("工作中", style = MaterialTheme.typography.bodySmall)
                }

                // 🔥 【就绪状态】绿色 - READY
                Row(verticalAlignment = Alignment.CenterVertically) {
                    TopBarBgeStatusIndicator(
                        bgeEngineStatus = remember { mutableStateOf(EngineStatus.READY) },
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
                    Text("就绪可用", style = MaterialTheme.typography.bodySmall)
                }

                // 🔥 【错误状态】红色 - ERROR
                Row(verticalAlignment = Alignment.CenterVertically) {
                    TopBarBgeStatusIndicator(
                        bgeEngineStatus = remember { mutableStateOf(EngineStatus.ERROR) },
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
                    Text("错误状态", style = MaterialTheme.typography.bodySmall)
                }

                // 🔥 【未初始化状态】灰色 - UNINITIALIZED
                Row(verticalAlignment = Alignment.CenterVertically) {
                    TopBarBgeStatusIndicator(
                        bgeEngineStatus = remember { mutableStateOf(EngineStatus.UNINITIALIZED) },
                    )
                    Spacer(modifier = Modifier.width(Tokens.Spacing.Small))
                    Text("未初始化", style = MaterialTheme.typography.bodySmall)
                }
            }
        }
    }
}

@GymBroPreview
@Composable
private fun AbsoluteCenteredGymBroLogoElevatedPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp), // TopBar标准高度
            color = MaterialTheme.coachTheme.backgroundElevated,
        ) {
            AbsoluteCenteredGymBroLogo(
                modifier = Modifier.fillMaxSize(),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun AbsoluteCenteredGymBroLogoBasicPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp), // TopBar标准高度
            color = MaterialTheme.coachTheme.backgroundElevated,
        ) {
            AbsoluteCenteredGymBroLogo(
                modifier = Modifier.fillMaxSize(),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun AiCoachTopBarPreview() {
    GymBroTheme {
        Surface {
            // 模拟完整的TopBar状态
            val mockState = AiCoachContract.State(
                currentApiProvider = AiCoachContract.ApiProvider.OPENAI,
                sessionState = AiCoachContract.SessionManagementState(),
                inputState = AiCoachContract.InputState(placeholder = "与AI教练对话..."),
            )

            // 🔥 【Preview修复】由于PromptRegistry需要依赖注入，Preview中传入null
            // AiCoachTopBar函数会处理null情况
            val mockPromptRegistry: com.example.gymbro.core.ai.prompt.registry.PromptRegistry? = null

            AiCoachTopBar(
                state = mockState,
                bgeEngineStatus = remember { mutableStateOf(EngineStatus.READY) },
                onBackClick = {},
                onHistoryClick = {},
                onNewChatClick = {},
                onClearSessionClick = {},
                onTogglePromptDebug = {},
                showPromptDebugPanel = false,
                promptRegistry = mockPromptRegistry,
                onSwitchPromptMode = {},
                onModelSelected = {},
            )
        }
    }
}

package com.example.gymbro

import android.app.Application
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import androidx.work.WorkManager
import com.example.gymbro.app.notification.NotificationChannelManager
import com.example.gymbro.core.di.qualifiers.ApplicationScope
import com.example.gymbro.core.ml.service.BgeEngineManager
import com.example.gymbro.data.shared.migration.DatabaseMigrationService
import com.example.gymbro.startup.AppStartupManager
import com.google.android.gms.common.GoogleApiAvailability
import com.google.firebase.FirebaseApp
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 应用程序类
 * 使用 @HiltAndroidApp 注解启用 Hilt 依赖注入
 * 使用手动初始化 WorkManager 避免循环依赖问题
 */
@HiltAndroidApp
class GymBroApp :
    Application(),
    Configuration.Provider {
    // 只保留启动时必须的依赖
    @Inject
    lateinit var databaseMigrationService: DatabaseMigrationService

    @Inject
    lateinit var notificationChannelManager: NotificationChannelManager

    // 🔥 BGE引擎管理器 - 用于预加载
    @Inject
    lateinit var bgeEngineManager: BgeEngineManager

    // 🔥 【新增】应用启动管理器 - 统一管理所有重量级组件的后台加载
    @Inject
    lateinit var appStartupManager: AppStartupManager

    // 🔥 应用级协程作用域 - 用于后台预加载
    @Inject
    @ApplicationScope
    lateinit var applicationScope: CoroutineScope

    // 🔥 Hilt WorkerFactory - 修复 HardwareDetectionWorker ANR 问题
    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    // 🔥 【新增】统一日志管理器
    @Inject
    lateinit var timberManager: com.example.gymbro.core.logging.TimberManager

    // 🔥 【Token流调试】日志配置管理器
    @Inject
    lateinit var loggingConfig: com.example.gymbro.core.logging.LoggingConfig

    // 移除了不再需要的启动时依赖：
    // - authRepository: Firebase认证会自动初始化
    // - exerciseDataInitializer: 延迟到首次进入训练模块时
    // - syncManager: 延迟到用户登录后
    // - networkMonitor: 延迟到需要网络功能时
    // - aiProviderManager: 延迟到首次使用AI功能时

    // 注意：applicationScope现在通过DI注入，移除了手动创建的实例

    override fun onCreate() {
        super.onCreate()

        // 初始化Firebase
        initializeFirebase()

        // 配置日志记录器
        setupTimber()

        // 手动初始化WorkManager
        initializeWorkManager()

        // 初始化通知渠道
        initializeNotificationChannels()

        // 延迟初始化其他组件
        delayedInit()

        // 🔥 【架构重构】启动统一的后台加载管理器
        // 替代原有的单独BGE预加载，实现所有重量级组件的智能并行加载
        startBackgroundLoading()
    }

    /**
     * 初始化Firebase应用
     * 确保Firebase应用实例正确配置
     */
    private fun initializeFirebase() {
        try {
            // 检查Firebase是否已初始化
            if (FirebaseApp.getApps(this).isNotEmpty()) {
                Timber.d("Firebase已初始化")
            } else {
                Timber.d("Firebase尚未初始化，尝试重新初始化")
                FirebaseApp.initializeApp(this)
            }

            // 检查Google Play服务是否可用
            val googleApiAvailability = GoogleApiAvailability.getInstance()
            val resultCode = googleApiAvailability.isGooglePlayServicesAvailable(this)
            if (resultCode != com.google.android.gms.common.ConnectionResult.SUCCESS) {
                Timber.e("Google Play服务不可用，错误码: $resultCode")
                if (googleApiAvailability.isUserResolvableError(resultCode)) {
                    Timber.d("Google Play服务错误可被用户解决")
                }
            } else {
                Timber.d("Google Play服务可用")
            }
        } catch (e: Exception) {
            Timber.e(e, "初始化Firebase时出错")
        }
    }

    /**
     * 配置Timber日志系统
     *
     * 🔥 【架构合规】通过core层的TimberManager统一管理日志系统
     * 支持ThinkingBox专用模式，但不直接依赖features模块
     *
     * 启用关键调试标签：
     * - TB-MAPPER, TB-REDUCER, TB-TITLE - DomainMapper和ThinkingReducer事件处理
     * - ThinkingBox-Debug, ThinkingHeader-Debug - UI状态调试
     * - TOKEN-FLOW, TB-MAIN - token处理流程
     * 关闭噪音日志：
     * - COACH-*, WORKOUT-* 模块日志
     * - MVI、ViewModel、导航等噪音日志
     */
    private fun setupTimber() {
        // 🔥 【架构合规】使用TimberManager统一初始化，支持ThinkingBox模式
        timberManager.initializeWithThinkingBoxSupport(BuildConfig.DEBUG)
    }

    /**
     * 手动初始化WorkManager
     * 由于在AndroidManifest.xml中禁用了自动初始化，需要手动配置
     */
    private fun initializeWorkManager() {
        try {
            if (!WorkManager.isInitialized()) {
                val config = workManagerConfiguration
                WorkManager.initialize(this, config)
                Timber.d("WorkManager手动初始化完成")
            } else {
                Timber.d("WorkManager已经初始化")
            }
        } catch (e: Exception) {
            Timber.e(e, "WorkManager初始化失败")
        }
    }

    /**
     * 初始化通知渠道
     * 为休息计时器等功能创建必要的通知渠道
     */
    private fun initializeNotificationChannels() {
        try {
            notificationChannelManager.initializeChannels()
            Timber.d("通知渠道初始化完成")
        } catch (e: Exception) {
            Timber.e(e, "通知渠道初始化失败")
        }
    }

    /**
     * 提供WorkManager配置
     * 实现Configuration.Provider接口
     * 🔥 修复：注入 HiltWorkerFactory 解决 HardwareDetectionWorker ANR 问题
     */
    override val workManagerConfiguration: Configuration
        get() =
            Configuration
                .Builder()
                .setWorkerFactory(workerFactory)
                .setMinimumLoggingLevel(
                    if (BuildConfig.DEBUG) android.util.Log.DEBUG else android.util.Log.INFO,
                ).build()

    /**
     * 优化后的延迟初始化
     * 只执行启动时必须的任务，其他任务延迟到实际需要时执行
     */
    private fun delayedInit() {
        // 只执行必须的数据库迁移
        applicationScope.launch(Dispatchers.IO) {
            try {
                // 数据库迁移是必须的，因为应用依赖数据库
                migrateDatabase()
                Timber.d("数据库迁移完成")
            } catch (e: Exception) {
                Timber.e(e, "数据库迁移失败")
            }
        }

        // 🔥 调度硬件检测后台任务 - 减少启动时阻塞
        scheduleHardwareDetection()

        Timber.d("🔥 【架构重构】启动优化完成，所有重量级组件由AppStartupManager统一管理")
        Timber.d("- AI提供商初始化：由AppStartupManager在网络层统一初始化")
        Timber.d("- BGE引擎初始化：由AppStartupManager在AI核心层统一初始化")
        Timber.d("- 网络监控：由AppStartupManager在网络层优先初始化")
        Timber.d("- PromptRegistry：由AppStartupManager在AI核心层统一初始化")
        Timber.d("- 硬件检测：后台异步执行，不阻塞启动")
    }

    /**
     * 调度硬件检测后台任务
     *
     * 🎯 核心优化：将硬件检测从启动时同步执行改为后台异步执行
     * - 减少启动时的阻塞时间，特别是在低端机上
     * - 检测结果会被缓存，BGE初始化时可直接使用
     * - 检测失败时BGE会自动回退到CPU模式
     */
    private fun scheduleHardwareDetection() {
        try {
            // 使用静态方法调度硬件检测任务
            com.example.gymbro.features.coach.shared.managers.HardwareDetectionWorker
                .enqueueDetection(
                    context = this,
                    forceDetection = false, // 如果已有缓存则跳过
                )
            Timber.d("📱 硬件检测任务已调度，将在后台执行")
        } catch (e: Exception) {
            Timber.w(e, "调度硬件检测任务失败，BGE将在需要时进行检测")
        }
    }

    // 移除了不必要的启动时初始化方法：
    // - startNetworkMonitoring(): 延迟到需要网络功能时
    // - initializeAuth(): Firebase认证会自动初始化
    // - initializeAiProviders(): 延迟到首次使用AI功能时
    // - initializeExerciseData(): 延迟到首次进入训练模块时
    // - scheduleBackgroundSync(): 延迟到用户登录后
    // - scheduleWhitelistUpdate(): 延迟到需要时

    /**
     * 执行数据库迁移
     * 将训练动作数据从旧结构迁移到新的统一数据库结构
     */
    private fun migrateDatabase() {
        databaseMigrationService.executeMigrations()
    }

    /**
     * 🔥 【架构重构】统一后台加载管理 - 实现零等待AI体验的核心
     *
     * 使用AppStartupManager统一管理所有重量级组件的后台加载：
     * - 网络层优先初始化（NetworkWatchdog、AiProviderManager、ProtocolDetector）
     * - AI核心组件并行加载（BGE引擎、PromptRegistry、TokenizerService）
     * - 功能模块按需加载（ThinkingBox、ChatHistory等）
     * - 智能依赖关系管理和错误处理
     */
    private fun startBackgroundLoading() {
        try {
            Timber.i("🚀 启动应用后台静默加载管理器...")

            // 启动统一的后台加载流程
            appStartupManager.startBackgroundLoading()

            // 可选：监听启动状态（用于调试或显示启动进度）
            applicationScope.launch {
                appStartupManager.startupState.collect { state ->
                    when (state.phase) {
                        com.example.gymbro.startup.AppStartupManager.StartupPhase.NETWORK_LAYER_READY -> {
                            Timber.i("🌐 网络层就绪，AI功能网络基础已建立")
                        }
                        com.example.gymbro.startup.AppStartupManager.StartupPhase.AI_CORE_READY -> {
                            Timber.i("🧠 AI核心就绪，智能功能已可用")
                        }
                        com.example.gymbro.startup.AppStartupManager.StartupPhase.ALL_READY -> {
                            Timber.i("🎉 所有组件就绪！用户将享受零等待体验")
                            return@collect // 只收集到完成状态
                        }
                        com.example.gymbro.startup.AppStartupManager.StartupPhase.ERROR -> {
                            Timber.w("⚠️ 部分组件启动失败，但应用仍可正常使用")
                            return@collect
                        }
                        else -> {
                            // 其他状态继续监听
                        }
                    }
                }
            }
        } catch (e: Exception) {
            // 启动管理器失败不应影响应用正常启动
            Timber.w(e, "⚠️ 后台加载管理器启动异常，将降级到按需加载")
        }
    }

    // 移除了这些方法，它们现在会在需要时才执行：
    // - initializeExerciseData(): 移到WorkoutModule首次使用时
    // - scheduleBackgroundSync(): 移到用户登录后
    // - scheduleWhitelistUpdate(): 移到需要时
    // - initializeAiProviders(): 移到CoachModule首次使用时

    override fun onTerminate() {
        super.onTerminate()
        // 网络监控现在是按需启动的，所以这里不需要停止
        Timber.d("应用终止")
    }
}

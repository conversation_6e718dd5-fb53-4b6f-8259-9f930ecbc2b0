package com.example.gymbro.domain.coach.repository

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.coach.model.CoachMessage
import kotlinx.coroutines.flow.Flow

/**
 * 聊天仓库接口
 *
 * 定义聊天会话的数据访问操作，支持多轮对话的完整生命周期管理
 * 重构说明：统一命名，完善会话管理，所有异步操作返回ModernResult<T>
 */
interface ChatRepository {

    /**
     * 创建聊天会话
     * @param userId 用户ID
     * @param title 会话标题
     * @return 创建的会话
     */
    suspend fun createSession(userId: String, title: String?): ModernResult<ChatSession>

    /**
     * 获取聊天会话
     * @param sessionId 会话ID
     * @return 会话信息
     */
    suspend fun getSession(sessionId: String): ModernResult<ChatSession?>

    /**
     * 获取用户的聊天会话列表
     * @param userId 用户ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 会话列表
     */
    fun getUserSessions(
        userId: String,
        limit: Int = 20,
        offset: Int = 0,
    ): Flow<ModernResult<List<ChatSession>>>

    /**
     * 获取用户的聊天会话列表（用于分页）
     */
    suspend fun getUserSessionsPaged(
        userId: String,
        limit: Int,
        offset: Int,
    ): ModernResult<List<ChatSession>>

    /**
     * 观察最新的聊天会话（无感加载）
     *
     * 这个Flow会立即发出当前的数据快照，然后在数据变化时发出新值。
     * 它永远不会处于"加载中"状态，实现瞬时的、无感的数据呈现。
     *
     * @param userId 用户ID
     * @return 最新会话的Flow，如果没有会话则返回null
     */
    fun observeLatestSession(userId: String): Flow<ChatSession?>

    /**
     * 更新会话信息
     * @param sessionId 会话ID
     * @param title 新标题
     * @return 更新结果
     */
    suspend fun updateSession(sessionId: String, title: String): ModernResult<Unit>

    /**
     * 更新会话概要
     * @param sessionId 会话ID
     * @param summary 概要内容
     * @return 更新结果
     */
    suspend fun updateSessionSummary(sessionId: String, summary: String): ModernResult<Unit>

    /**
     * 删除会话
     * @param sessionId 会话ID
     * @return 删除结果
     */
    suspend fun deleteSession(sessionId: String): ModernResult<Unit>

    /**
     * 添加消息到会话
     * @param sessionId 会话ID
     * @param message 消息内容
     * @return 添加结果
     */
    suspend fun addMessage(sessionId: String, message: CoachMessage): ModernResult<Unit>

    /**
     * 获取会话消息列表
     * @param sessionId 会话ID
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 消息列表
     */
    fun getMessages(
        sessionId: String,
        limit: Int = 50,
        offset: Int = 0,
    ): Flow<ModernResult<List<CoachMessage>>>

    /**
     * 清空会话消息
     * @param sessionId 会话ID
     * @return 清空结果
     */
    suspend fun clearMessages(sessionId: String): ModernResult<Unit>

    // ===== Task2-CoachContext数据中心集成：RAG相关方法 =====

    /**
     * 向量搜索相似消息
     * Task2-CoachContext数据中心集成：基于向量相似度的消息检索
     *
     * @param queryVector 查询向量
     * @param k 返回的Top-K结果数量
     * @param sessionId 可选的会话ID过滤
     * @return 相似消息列表，按相似度排序
     */
    suspend fun findSimilarMessages(
        queryVector: FloatArray,
        k: Int = 5,
        sessionId: String? = null,
    ): ModernResult<List<CoachMessage>>

    /**
     * 获取最近的对话历史
     * Task2-CoachContext数据中心集成：获取最近N轮对话用于上下文
     *
     * @param sessionId 会话ID
     * @param n 最近N轮对话数量
     * @return 最近的对话消息列表，按时间排序
     */
    suspend fun getRecentHistory(
        sessionId: String,
        n: Int = 10,
    ): ModernResult<List<CoachMessage>>

    /**
     * 索引所有消息
     * Task2-CoachContext数据中心集成：为所有消息生成向量索引
     *
     * @return 索引操作结果，包含处理的消息数量
     */
    suspend fun indexAllMessages(): ModernResult<Int>

    /**
     * 保存AI消息并建立关联
     * Task2-CoachContext数据中心集成：保存AI消息时建立与用户消息的关联
     *
     * @param sessionId 会话ID
     * @param aiMessage AI消息
     * @param inReplyToMessageId 回复的用户消息ID
     * @return 保存结果
     */
    suspend fun saveAiMessage(
        sessionId: String,
        aiMessage: CoachMessage.AiMessage,
        inReplyToMessageId: String,
    ): ModernResult<Unit>

    /**
     * 根据消息ID获取单个消息
     *
     * 基于 history-todo-plan.md 的单个消息获取逻辑要求，实现：
     * - 通过消息ID准确获取CoachMessage
     * - 支持错误处理和重试机制
     * - 性能优化，避免N+1查询问题
     *
     * @param messageId 消息ID（字符串格式）
     * @return 消息对象，如果不存在则返回null
     */
    suspend fun getMessageById(messageId: String): ModernResult<CoachMessage?>

    // ==================== 历史记录专用方法 ====================

    /**
     * 获取用户的所有会话列表（分页）
     *
     * @param userId 用户ID
     * @param limit 每页数量
     * @param offset 偏移量
     * @return 会话列表
     */
    suspend fun getSessionsForUser(
        userId: String,
        limit: Int = 20,
        offset: Int = 0,
    ): ModernResult<List<ChatSession>>

    /**
     * 搜索会话（支持内容搜索）
     *
     * @param userId 用户ID
     * @param query 搜索关键词
     * @param limit 每页数量
     * @param offset 偏移量
     * @return 匹配的会话列表
     */
    suspend fun searchSessions(
        userId: String,
        query: String,
        limit: Int = 20,
        offset: Int = 0,
    ): ModernResult<List<ChatSession>>

    /**
     * 获取会话的完整消息历史（包含 ThinkingBox 数据）
     *
     * @param sessionId 会话ID
     * @param includeThinkingNodes 是否包含思考过程数据
     * @return 完整的消息列表
     */
    suspend fun getSessionHistory(
        sessionId: String,
        includeThinkingNodes: Boolean = true,
    ): ModernResult<List<CoachMessage>>

    /**
     * 获取包含最终内容的 AI 消息列表
     *
     * @param userId 用户ID
     * @param limit 数量限制
     * @return 包含 finalMarkdown 的 AI 消息列表
     */
    suspend fun getAiMessagesWithFinalContent(
        userId: String,
        limit: Int = 50,
    ): ModernResult<List<CoachMessage.AiMessage>>

    /**
     * 按时间范围获取会话
     *
     * @param userId 用户ID
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @return 时间范围内的会话列表
     */
    suspend fun getSessionsByTimeRange(
        userId: String,
        startTime: Long,
        endTime: Long,
    ): ModernResult<List<ChatSession>>

    /**
     * 更新会话标题
     *
     * @param sessionId 会话ID
     * @param newTitle 新标题
     * @return 更新结果
     */
    suspend fun updateSessionTitle(
        sessionId: String,
        newTitle: String,
    ): ModernResult<Unit>
}

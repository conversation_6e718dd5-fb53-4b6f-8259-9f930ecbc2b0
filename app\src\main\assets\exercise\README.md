# GymBro 动作库种子数据

## 📁 文件说明

- `exercise_official_seed.json` - 官方动作种子数据
- `exercise_schema.json` - JSON Schema 验证文件
- `README.md` - 本说明文档

## 🎯 数据概览

当前种子数据包含 **11 个基础健身动作**，覆盖主要肌群：

### 胸部动作 (2个)
- 杠铃卧推 (`off_5023c57c`)
- 哑铃卧推 (`off_a9a8603d`)

### 背部动作 (2个)
- 引体向上 (`off_63bab238`)
- 杠铃划船 (`off_dee0cb74`)

### 腿部动作 (2个)
- 杠铃深蹲 (`off_eb02d317`)
- 硬拉 (`off_615f2a8a`)

### 肩部动作 (2个)
- 杠铃推举 (`off_b7ccaf9d`)
- 哑铃侧平举 (`off_93566fbc`)

### 手臂动作 (2个)
- 杠铃弯举 (`off_eafd145e`)
- 双杠臂屈伸 (`off_a87e1409`)

### 核心动作 (1个)
- 平板支撑 (`off_4beb4c0f`)

## 🔧 ID 生成规则

### 官方动作 ID
- **格式**: `off_{hash}`
- **生成**: MD5(动作中文名).substring(0,8)
- **示例**: `off_5023c57c` (杠铃卧推)

### 自定义动作 ID
- **格式**: `u_{userId}_{uuid}`
- **生成**: 用户ID + UUID前8位
- **示例**: `u_user123_a1b2c3d4`

## 📝 数据格式

每个动作包含以下字段：

### 必填字段
- `id` - 唯一标识符
- `name` - 中英文名称
- `muscleGroup` - 主要肌群
- `category` - 动作分类
- `equipment` - 所需器械
- `source` - 动作来源
- `isCustom/isOfficial` - 动作类型标识
- `contentHash` - 内容哈希
- `createdAt/updatedAt` - 时间戳

### 可选字段
- `description` - 动作描述
- `targetMuscles` - 目标肌群
- `secondaryMuscles` - 次要肌群
- `difficulty` - 难度等级
- `defaultSets/Reps/RestTime` - 默认训练参数
- `steps` - 动作步骤
- `tips` - 训练技巧
- `media` - 媒体资源

## ✅ 数据验证

使用 JSON Schema 验证数据格式：

```bash
# 安装验证工具
npm install -g ajv-cli

# 验证数据格式
ajv validate -s exercise_schema.json -d exercise_official_seed.json
```

## 🚀 使用方式

### 在代码中加载
```kotlin
// OfficialExerciseInitializer.kt
private fun loadSeedData(): ExerciseSeedData {
    val json = context.assets.open("exercise/exercise_official_seed.json")
        .bufferedReader().use { it.readText() }
    return Json.decodeFromString<ExerciseSeedData>(json)
}
```

### 数据初始化流程
1. 应用首次启动时检查官方动作数量
2. 如果为空，从 assets 加载种子数据
3. 转换为 Entity 对象并批量插入数据库
4. 标记初始化完成，避免重复加载

## 📊 数据统计

- **总动作数**: 11
- **难度分布**: 
  - 初级: 3个 (27%)
  - 中级: 7个 (64%)
  - 高级: 1个 (9%)
- **器械分布**:
  - 杠铃: 5个 (45%)
  - 哑铃: 2个 (18%)
  - 徒手: 4个 (36%)

## 🔄 更新流程

1. **修改数据**: 编辑 `exercise_official_seed.json`
2. **验证格式**: 运行 JSON Schema 验证
3. **更新版本**: 修改 `entityVersion` 和 `generatedAt`
4. **测试加载**: 确保应用能正确加载新数据
5. **提交代码**: 通过 PR 流程合并更改

## 🛡️ 注意事项

1. **ID 唯一性**: 确保所有 ID 在全局范围内唯一
2. **哈希一致性**: 官方动作 ID 必须与名称哈希匹配
3. **时间戳**: `createdAt <= updatedAt`
4. **必填字段**: 所有必填字段都不能为空
5. **枚举值**: 确保所有枚举字段使用正确的值

## 📞 联系方式

如有问题或建议，请联系开发团队或提交 Issue。

package com.example.gymbro.examples

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.example.gymbro.designSystem.animation.GymBroAnimations
import com.example.gymbro.designSystem.foundation.GymBroColors
import com.example.gymbro.designSystem.foundation.GymBroSpacing
import com.example.gymbro.designSystem.foundation.GymBroTypography
import com.example.gymbro.designSystem.foundation.GymBroTokens
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.preview.GymBroThemePreview
import kotlinx.coroutines.flow.Flow

/**
 * UI组件标准示例
 *
 * 本示例展示了GymBro项目中UI组件的完整实现标准：
 * 1. Design System Token使用规范
 * 2. Material Design 3集成模式
 * 3. Compose最佳实践和性能优化
 * 4. @GymBroPreview预览系统
 * 5. 状态提升和组件组合模式
 *
 * 设计原则：
 * - Token化设计：所有值通过Token系统管理
 * - 组件无状态：状态提升到父组件
 * - 可复用性：组件支持多场景使用
 * - 预览完整：所有组件都有预览支持
 */

// ========================================
// 1. 基础组件标准
// ========================================

/**
 * 基础卡片组件
 *
 * 特点：
 * - 完全Token化设计
 * - 支持多种状态
 * - 无状态实现
 * - 完整预览覆盖
 */
@Composable
fun GymBroCard(
    modifier: Modifier = Modifier,
    elevation: CardElevation = CardDefaults.cardElevation(
        defaultElevation = GymBroTokens.Elevation.Level2
    ),
    colors: CardColors = CardDefaults.cardColors(
        containerColor = GymBroColors.surface,
        contentColor = GymBroColors.onSurface
    ),
    onClick: (() -> Unit)? = null,
    content: @Composable ColumnScope.() -> Unit
) {
    if (onClick != null) {
        Card(
            onClick = onClick,
            modifier = modifier,
            elevation = elevation,
            colors = colors,
            shape = RoundedCornerShape(GymBroTokens.BorderRadius.Medium),
            content = content
        )
    } else {
        Card(
            modifier = modifier,
            elevation = elevation,
            colors = colors,
            shape = RoundedCornerShape(GymBroTokens.BorderRadius.Medium),
            content = content
        )
    }
}

/**
 * 状态按钮组件
 *
 * 支持加载、禁用、成功等多种状态
 */
@Composable
fun GymBroButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false,
    buttonType: GymBroButtonType = GymBroButtonType.Primary,
    leadingIcon: ImageVector? = null,
    trailingIcon: ImageVector? = null
) {
    val buttonColors = when (buttonType) {
        GymBroButtonType.Primary -> ButtonDefaults.buttonColors(
            containerColor = GymBroColors.primary,
            contentColor = GymBroColors.onPrimary,
            disabledContainerColor = GymBroColors.surfaceVariant,
            disabledContentColor = GymBroColors.onSurfaceVariant
        )
        GymBroButtonType.Secondary -> ButtonDefaults.outlinedButtonColors(
            contentColor = GymBroColors.primary,
            disabledContentColor = GymBroColors.onSurfaceVariant
        )
        GymBroButtonType.Tertiary -> ButtonDefaults.textButtonColors(
            contentColor = GymBroColors.primary,
            disabledContentColor = GymBroColors.onSurfaceVariant
        )
    }

    when (buttonType) {
        GymBroButtonType.Primary -> {
            Button(
                onClick = onClick,
                modifier = modifier.height(GymBroTokens.Size.ButtonHeight),
                enabled = enabled && !isLoading,
                colors = buttonColors,
                shape = RoundedCornerShape(GymBroTokens.BorderRadius.Medium),
                contentPadding = PaddingValues(
                    horizontal = GymBroSpacing.medium,
                    vertical = GymBroSpacing.small
                )
            ) {
                ButtonContent(
                    text = text,
                    isLoading = isLoading,
                    leadingIcon = leadingIcon,
                    trailingIcon = trailingIcon
                )
            }
        }
        GymBroButtonType.Secondary -> {
            OutlinedButton(
                onClick = onClick,
                modifier = modifier.height(GymBroTokens.Size.ButtonHeight),
                enabled = enabled && !isLoading,
                colors = buttonColors,
                shape = RoundedCornerShape(GymBroTokens.BorderRadius.Medium),
                contentPadding = PaddingValues(
                    horizontal = GymBroSpacing.medium,
                    vertical = GymBroSpacing.small
                )
            ) {
                ButtonContent(
                    text = text,
                    isLoading = isLoading,
                    leadingIcon = leadingIcon,
                    trailingIcon = trailingIcon
                )
            }
        }
        GymBroButtonType.Tertiary -> {
            TextButton(
                onClick = onClick,
                modifier = modifier.height(GymBroTokens.Size.ButtonHeight),
                enabled = enabled && !isLoading,
                colors = buttonColors,
                shape = RoundedCornerShape(GymBroTokens.BorderRadius.Medium),
                contentPadding = PaddingValues(
                    horizontal = GymBroSpacing.medium,
                    vertical = GymBroSpacing.small
                )
            ) {
                ButtonContent(
                    text = text,
                    isLoading = isLoading,
                    leadingIcon = leadingIcon,
                    trailingIcon = trailingIcon
                )
            }
        }
    }
}

@Composable
private fun ButtonContent(
    text: String,
    isLoading: Boolean,
    leadingIcon: ImageVector?,
    trailingIcon: ImageVector?
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(GymBroSpacing.small),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (isLoading) {
            CircularProgressIndicator(
                modifier = Modifier.size(GymBroTokens.Size.IconSmall),
                strokeWidth = 2.dp,
                color = LocalContentColor.current
            )
        } else {
            leadingIcon?.let { icon ->
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(GymBroTokens.Size.IconSmall)
                )
            }
        }
        
        Text(
            text = text,
            style = GymBroTypography.labelLarge,
            fontWeight = FontWeight.Medium
        )
        
        if (!isLoading) {
            trailingIcon?.let { icon ->
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    modifier = Modifier.size(GymBroTokens.Size.IconSmall)
                )
            }
        }
    }
}

/**
 * 按钮类型枚举
 */
enum class GymBroButtonType {
    Primary,    // 主要按钮
    Secondary,  // 次要按钮（边框）
    Tertiary    // 文本按钮
}

// ========================================
// 2. 复杂组件标准
// ========================================

/**
 * 训练记录卡片组件
 *
 * 展示完整的组件设计模式：
 * - 数据类绑定
 * - 状态管理
 * - 动画集成
 * - 操作回调
 */
@Composable
fun WorkoutRecordCard(
    record: WorkoutRecord,
    onCardClick: (WorkoutRecord) -> Unit,
    onEditClick: (WorkoutRecord) -> Unit,
    onDeleteClick: (WorkoutRecord) -> Unit,
    modifier: Modifier = Modifier,
    isSelected: Boolean = false,
    showActions: Boolean = true
) {
    val cardElevation by animateDpAsState(
        targetValue = if (isSelected) GymBroTokens.Elevation.Level3 else GymBroTokens.Elevation.Level1,
        animationSpec = GymBroAnimations.springDampedMedium(),
        label = "card_elevation"
    )
    
    val cardColors = CardDefaults.cardColors(
        containerColor = if (isSelected) {
            GymBroColors.primaryContainer
        } else {
            GymBroColors.surface
        },
        contentColor = if (isSelected) {
            GymBroColors.onPrimaryContainer
        } else {
            GymBroColors.onSurface
        }
    )

    GymBroCard(
        onClick = { onCardClick(record) },
        modifier = modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = cardElevation),
        colors = cardColors
    ) {
        Column(
            modifier = Modifier.padding(GymBroSpacing.medium),
            verticalArrangement = Arrangement.spacedBy(GymBroSpacing.small)
        ) {
            // 标题行
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = record.title,
                    style = GymBroTypography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.weight(1f)
                )
                
                if (showActions) {
                    WorkoutRecordActions(
                        onEditClick = { onEditClick(record) },
                        onDeleteClick = { onDeleteClick(record) }
                    )
                }
            }
            
            // 描述
            if (record.description.isNotBlank()) {
                Text(
                    text = record.description,
                    style = GymBroTypography.bodyMedium,
                    color = GymBroColors.onSurfaceVariant,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis
                )
            }
            
            // 统计信息
            WorkoutRecordStats(
                duration = record.duration,
                calories = record.calories,
                difficulty = record.difficulty
            )
            
            // 标签
            if (record.tags.isNotEmpty()) {
                WorkoutRecordTags(
                    tags = record.tags,
                    maxDisplayTags = 3
                )
            }
            
            // 同步状态
            WorkoutRecordSyncStatus(isSynced = record.isSynced)
        }
    }
}

@Composable
private fun WorkoutRecordActions(
    onEditClick: () -> Unit,
    onDeleteClick: () -> Unit
) {
    Row(
        horizontalArrangement = Arrangement.spacedBy(GymBroSpacing.extraSmall)
    ) {
        IconButton(
            onClick = onEditClick,
            modifier = Modifier.size(GymBroTokens.Size.TouchTarget)
        ) {
            Icon(
                imageVector = GymBroTokens.Icons.Edit,
                contentDescription = "编辑",
                tint = GymBroColors.primary
            )
        }
        
        IconButton(
            onClick = onDeleteClick,
            modifier = Modifier.size(GymBroTokens.Size.TouchTarget)
        ) {
            Icon(
                imageVector = GymBroTokens.Icons.Delete,
                contentDescription = "删除",
                tint = GymBroColors.error
            )
        }
    }
}

@Composable
private fun WorkoutRecordStats(
    duration: Long,
    calories: Int,
    difficulty: Int
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        StatItem(
            icon = GymBroTokens.Icons.Timer,
            label = "时长",
            value = "${duration}分钟"
        )
        
        StatItem(
            icon = GymBroTokens.Icons.LocalFireDepartment,
            label = "卡路里",
            value = "${calories}cal"
        )
        
        StatItem(
            icon = GymBroTokens.Icons.TrendingUp,
            label = "难度",
            value = "$difficulty/5"
        )
    }
}

@Composable
private fun StatItem(
    icon: ImageVector,
    label: String,
    value: String
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(GymBroSpacing.extraSmall)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = label,
            modifier = Modifier.size(GymBroTokens.Size.IconSmall),
            tint = GymBroColors.primary
        )
        
        Text(
            text = value,
            style = GymBroTypography.labelMedium,
            fontWeight = FontWeight.Medium,
            textAlign = TextAlign.Center
        )
        
        Text(
            text = label,
            style = GymBroTypography.labelSmall,
            color = GymBroColors.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun WorkoutRecordTags(
    tags: List<String>,
    maxDisplayTags: Int = 3
) {
    LazyRow(
        horizontalArrangement = Arrangement.spacedBy(GymBroSpacing.extraSmall),
        contentPadding = PaddingValues(vertical = GymBroSpacing.extraSmall)
    ) {
        items(tags.take(maxDisplayTags)) { tag ->
            AssistChip(
                onClick = { /* Handle tag click */ },
                label = {
                    Text(
                        text = tag,
                        style = GymBroTypography.labelSmall
                    )
                },
                colors = AssistChipDefaults.assistChipColors(
                    containerColor = GymBroColors.secondaryContainer,
                    labelColor = GymBroColors.onSecondaryContainer
                )
            )
        }
        
        if (tags.size > maxDisplayTags) {
            item {
                AssistChip(
                    onClick = { /* Show all tags */ },
                    label = {
                        Text(
                            text = "+${tags.size - maxDisplayTags}",
                            style = GymBroTypography.labelSmall
                        )
                    },
                    colors = AssistChipDefaults.assistChipColors(
                        containerColor = GymBroColors.surfaceVariant,
                        labelColor = GymBroColors.onSurfaceVariant
                    )
                )
            }
        }
    }
}

@Composable
private fun WorkoutRecordSyncStatus(isSynced: Boolean) {
    val statusColor = if (isSynced) GymBroColors.primary else GymBroColors.warning
    val statusText = if (isSynced) "已同步" else "待同步"
    val statusIcon = if (isSynced) GymBroTokens.Icons.CloudDone else GymBroTokens.Icons.CloudQueue
    
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(GymBroSpacing.extraSmall)
    ) {
        Icon(
            imageVector = statusIcon,
            contentDescription = statusText,
            modifier = Modifier.size(GymBroTokens.Size.IconExtraSmall),
            tint = statusColor
        )
        
        Text(
            text = statusText,
            style = GymBroTypography.labelSmall,
            color = statusColor
        )
    }
}

// ========================================
// 3. 列表组件标准
// ========================================

/**
 * 训练记录列表组件
 *
 * 展示列表组件最佳实践：
 * - 状态管理
 * - 性能优化
 * - 动画处理
 * - 错误状态
 */
@Composable
fun WorkoutRecordList(
    recordsFlow: Flow<ModernResult<List<WorkoutRecord>>>,
    onRecordClick: (WorkoutRecord) -> Unit,
    onRecordEdit: (WorkoutRecord) -> Unit,
    onRecordDelete: (WorkoutRecord) -> Unit,
    modifier: Modifier = Modifier,
    selectedRecordId: String? = null,
    showActions: Boolean = true
) {
    val recordsState by recordsFlow.collectAsStateWithLifecycle(
        initialValue = ModernResult.Loading()
    )
    
    Box(modifier = modifier.fillMaxSize()) {
        when (val result = recordsState) {
            is ModernResult.Loading -> {
                WorkoutRecordListLoading()
            }
            
            is ModernResult.Success -> {
                if (result.data.isEmpty()) {
                    WorkoutRecordListEmpty()
                } else {
                    WorkoutRecordListContent(
                        records = result.data,
                        onRecordClick = onRecordClick,
                        onRecordEdit = onRecordEdit,
                        onRecordDelete = onRecordDelete,
                        selectedRecordId = selectedRecordId,
                        showActions = showActions
                    )
                }
            }
            
            is ModernResult.Error -> {
                WorkoutRecordListError(
                    error = result.error,
                    onRetry = { /* Handle retry */ }
                )
            }
        }
    }
}

@Composable
private fun WorkoutRecordListContent(
    records: List<WorkoutRecord>,
    onRecordClick: (WorkoutRecord) -> Unit,
    onRecordEdit: (WorkoutRecord) -> Unit,
    onRecordDelete: (WorkoutRecord) -> Unit,
    selectedRecordId: String?,
    showActions: Boolean
) {
    LazyColumn(
        contentPadding = PaddingValues(GymBroSpacing.medium),
        verticalArrangement = Arrangement.spacedBy(GymBroSpacing.small)
    ) {
        items(
            items = records,
            key = { it.id }
        ) { record ->
            WorkoutRecordCard(
                record = record,
                onCardClick = onRecordClick,
                onEditClick = onRecordEdit,
                onDeleteClick = onRecordDelete,
                isSelected = record.id == selectedRecordId,
                showActions = showActions,
                modifier = Modifier.animateItemPlacement(
                    animationSpec = GymBroAnimations.springDampedMedium()
                )
            )
        }
    }
}

@Composable
private fun WorkoutRecordListLoading() {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        CircularProgressIndicator(
            color = GymBroColors.primary,
            strokeWidth = 3.dp
        )
        
        Spacer(modifier = Modifier.height(GymBroSpacing.medium))
        
        Text(
            text = "加载训练记录...",
            style = GymBroTypography.bodyMedium,
            color = GymBroColors.onSurfaceVariant
        )
    }
}

@Composable
private fun WorkoutRecordListEmpty() {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = GymBroTokens.Icons.FitnessCenter,
            contentDescription = "暂无训练记录",
            modifier = Modifier.size(GymBroTokens.Size.IconLarge),
            tint = GymBroColors.onSurfaceVariant
        )
        
        Spacer(modifier = Modifier.height(GymBroSpacing.medium))
        
        Text(
            text = "暂无训练记录",
            style = GymBroTypography.titleMedium,
            color = GymBroColors.onSurface
        )
        
        Text(
            text = "开始你的第一次训练吧！",
            style = GymBroTypography.bodyMedium,
            color = GymBroColors.onSurfaceVariant
        )
    }
}

@Composable
private fun WorkoutRecordListError(
    error: ModernDataError,
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = GymBroTokens.Icons.Error,
            contentDescription = "加载失败",
            modifier = Modifier.size(GymBroTokens.Size.IconLarge),
            tint = GymBroColors.error
        )
        
        Spacer(modifier = Modifier.height(GymBroSpacing.medium))
        
        Text(
            text = "加载失败",
            style = GymBroTypography.titleMedium,
            color = GymBroColors.onSurface
        )
        
        Text(
            text = error.uiMessage.toString(),
            style = GymBroTypography.bodyMedium,
            color = GymBroColors.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(GymBroSpacing.medium))
        
        GymBroButton(
            text = "重试",
            onClick = onRetry,
            buttonType = GymBroButtonType.Secondary
        )
    }
}

// ========================================
// 4. 预览系统标准
// ========================================

/**
 * 预览参数提供器
 */
class WorkoutRecordPreviewParameterProvider : PreviewParameterProvider<WorkoutRecord> {
    override val values = sequenceOf(
        WorkoutRecord(
            id = "1",
            title = "胸部训练",
            description = "胸大肌和三角肌前束的综合训练",
            duration = 60,
            calories = 300,
            difficulty = 4,
            tags = listOf("胸部", "力量", "哑铃"),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            isSynced = true
        ),
        WorkoutRecord(
            id = "2",
            title = "HIIT间歇训练",
            description = "",
            duration = 30,
            calories = 250,
            difficulty = 5,
            tags = listOf("有氧", "HIIT", "减脂"),
            createdAt = System.currentTimeMillis() - 86400000,
            updatedAt = System.currentTimeMillis() - 86400000,
            isSynced = false
        )
    )
}

// ========================================
// 5. 预览定义
// ========================================

@GymBroPreview
@Composable
private fun GymBroCardPreview() {
    GymBroThemePreview {
        GymBroCard {
            Column(
                modifier = Modifier.padding(GymBroSpacing.medium)
            ) {
                Text(
                    text = "卡片标题",
                    style = GymBroTypography.titleMedium
                )
                Text(
                    text = "卡片内容描述",
                    style = GymBroTypography.bodyMedium
                )
            }
        }
    }
}

@GymBroPreview
@Composable
private fun GymBroButtonPreview() {
    GymBroThemePreview {
        Column(
            verticalArrangement = Arrangement.spacedBy(GymBroSpacing.medium)
        ) {
            GymBroButton(
                text = "主要按钮",
                onClick = { },
                buttonType = GymBroButtonType.Primary
            )
            
            GymBroButton(
                text = "次要按钮",
                onClick = { },
                buttonType = GymBroButtonType.Secondary
            )
            
            GymBroButton(
                text = "加载中",
                onClick = { },
                isLoading = true
            )
            
            GymBroButton(
                text = "禁用状态",
                onClick = { },
                enabled = false
            )
        }
    }
}

@GymBroPreview
@Composable
private fun WorkoutRecordCardPreview(
    @PreviewParameter(WorkoutRecordPreviewParameterProvider::class) record: WorkoutRecord
) {
    GymBroThemePreview {
        WorkoutRecordCard(
            record = record,
            onCardClick = { },
            onEditClick = { },
            onDeleteClick = { }
        )
    }
}

@GymBroPreview
@Composable
private fun WorkoutRecordCardSelectedPreview() {
    GymBroThemePreview {
        WorkoutRecordCard(
            record = WorkoutRecordPreviewParameterProvider().values.first(),
            onCardClick = { },
            onEditClick = { },
            onDeleteClick = { },
            isSelected = true
        )
    }
}

// ========================================
// 6. 使用模式说明
// ========================================

/**
 * UI组件使用模式示例
 * 
 * 在Screen中的典型用法：
 * 
 * ```kotlin
 * @Composable
 * fun WorkoutScreen(
 *     viewModel: WorkoutViewModel = hiltViewModel()
 * ) {
 *     val recordsFlow = viewModel.workoutRecords
 *     
 *     Column {
 *         // 使用标准列表组件
 *         WorkoutRecordList(
 *             recordsFlow = recordsFlow,
 *             onRecordClick = { record -> 
 *                 viewModel.selectRecord(record.id)
 *             },
 *             onRecordEdit = { record ->
 *                 // 导航到编辑页面
 *             },
 *             onRecordDelete = { record ->
 *                 viewModel.deleteRecord(record.id)
 *             }
 *         )
 *         
 *         // 使用标准按钮
 *         GymBroButton(
 *             text = "添加训练",
 *             onClick = { /* 导航到添加页面 */ },
 *             modifier = Modifier.fillMaxWidth()
 *         )
 *     }
 * }
 * ```
 */

/**
 * UI组件标准总结
 * 
 * 1. **Token化设计**：
 *    - 所有尺寸、颜色、字体通过Token系统管理
 *    - 统一的设计语言和视觉规范
 *    - 支持主题切换和动态颜色
 * 
 * 2. **组件无状态**：
 *    - 组件不持有内部状态
 *    - 状态提升到父组件或ViewModel
 *    - 便于测试和复用
 * 
 * 3. **性能优化**：
 *    - 使用remember和derivedStateOf避免重组
 *    - LazyList的key和animateItemPlacement
 *    - 适当的Modifier链式调用
 * 
 * 4. **预览系统**：
 *    - @GymBroPreview统一预览标准
 *    - PreviewParameterProvider支持多状态预览
 *    - 覆盖各种边界情况
 * 
 * 5. **可访问性**：
 *    - 合理的contentDescription
 *    - 适当的语义角色
 *    - 支持TalkBack和键盘导航
 * 
 * 6. **动画集成**：
 *    - 使用GymBroAnimations统一动画规范
 *    - 适当的动画时机和类型
 *    - 性能友好的动画实现
 */
package com.example.gymbro.data.database.migrations

import androidx.room.Room
import androidx.room.testing.MigrationTestHelper
import androidx.sqlite.db.framework.FrameworkSQLiteOpenHelperFactory
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.platform.app.InstrumentationRegistry
import com.example.gymbro.data.workout.plan.database.PlanDatabase
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import org.junit.Assert.*
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith
import java.io.IOException

/**
 * Room Migration Test for PlanDatabase
 *
 * Tests migration from version 1 to 2:
 * - Verifies progress column is added to plan_days table
 * - Verifies default value 'NOT_STARTED' is set for existing rows
 * - Verifies index on progress column is created
 * - Tests migration from all previous versions
 */
@RunWith(AndroidJUnit4::class)
class RoomMigrationTest {

    private val TEST_DB = "migration-test"

    @get:Rule
    val helper: MigrationTestHelper = MigrationTestHelper(
        InstrumentationRegistry.getInstrumentation(),
        PlanDatabase::class.java,
        emptyList(),
        FrameworkSQLiteOpenHelperFactory(),
    )

    @Test
    @Throws(IOException::class)
    fun migrate1To2() {
        // Create database with version 1
        helper.createDatabase(TEST_DB, 1).apply {
            // Insert test data in version 1
            execSQL(
                """
                INSERT INTO plan_days (id, planId, dayNumber, isRestDay, notes, orderIndex, estimatedDuration, isCompleted, createdAt)
                VALUES ('day1', 'plan1', 1, 0, 'Test day', 0, 60, 0, ${System.currentTimeMillis()})
                """.trimIndent(),
            )

            execSQL(
                """
                INSERT INTO plan_days (id, planId, dayNumber, isRestDay, notes, orderIndex, estimatedDuration, isCompleted, createdAt)
                VALUES ('day2', 'plan1', 2, 1, 'Rest day', 1, null, 0, ${System.currentTimeMillis()})
                """.trimIndent(),
            )

            close()
        }

        // Run migration and validate
        val db = helper.runMigrationsAndValidate(TEST_DB, 2, true, Migration_1_2)

        // Verify data integrity after migration
        val cursor = db.query("SELECT * FROM plan_days ORDER BY dayNumber")

        assertTrue("Should have migrated data", cursor.moveToFirst())

        // Check first row
        assertEquals("day1", cursor.getString(cursor.getColumnIndex("id")))
        assertEquals("plan1", cursor.getString(cursor.getColumnIndex("planId")))
        assertEquals(1, cursor.getInt(cursor.getColumnIndex("dayNumber")))
        assertEquals(0, cursor.getInt(cursor.getColumnIndex("isRestDay")))
        assertEquals("Test day", cursor.getString(cursor.getColumnIndex("notes")))
        assertEquals(0, cursor.getInt(cursor.getColumnIndex("orderIndex")))
        assertEquals(60, cursor.getInt(cursor.getColumnIndex("estimatedDuration")))
        assertEquals(0, cursor.getInt(cursor.getColumnIndex("isCompleted")))

        // Verify progress column exists and has default value
        val progressIndex = cursor.getColumnIndex("progress")
        assertNotEquals("Progress column should exist", -1, progressIndex)
        assertEquals("NOT_STARTED", cursor.getString(progressIndex))

        // Check second row
        assertTrue("Should have second row", cursor.moveToNext())
        assertEquals("day2", cursor.getString(cursor.getColumnIndex("id")))
        assertEquals("NOT_STARTED", cursor.getString(cursor.getColumnIndex("progress")))

        cursor.close()

        // Verify index exists
        val indexCursor = db.query(
            "SELECT * FROM sqlite_master WHERE type='index' AND name='index_plan_days_progress'",
        )
        assertTrue("Progress index should exist", indexCursor.count > 0)
        indexCursor.close()
    }

    @Test
    @Throws(IOException::class)
    fun migrateAll() {
        // Create oldest version of the database
        helper.createDatabase(TEST_DB, 1).apply {
            close()
        }

        // Open latest version of the database
        Room.databaseBuilder(
            InstrumentationRegistry.getInstrumentation().targetContext,
            PlanDatabase::class.java,
            TEST_DB,
        ).addMigrations(Migration_1_2).build().apply {
            openHelper.writableDatabase
            close()
        }
    }

    @Test
    @Throws(IOException::class)
    fun testProgressEnumValues() {
        // Test that all enum values can be stored and retrieved
        helper.createDatabase(TEST_DB, 2).apply {
            // Test inserting with different progress values
            PlanProgressStatus.values().forEachIndexed { index, status ->
                execSQL(
                    """
                    INSERT INTO plan_days (id, planId, dayNumber, isRestDay, progress, createdAt)
                    VALUES ('day$index', 'plan1', $index, 0, '${status.name}', ${System.currentTimeMillis()})
                    """.trimIndent(),
                )
            }

            // Verify all values
            val cursor = db.query("SELECT id, progress FROM plan_days ORDER BY dayNumber")
            var count = 0
            while (cursor.moveToNext()) {
                val progress = cursor.getString(cursor.getColumnIndex("progress"))
                assertTrue(
                    "Progress value should be valid enum",
                    PlanProgressStatus.values().any { it.name == progress },
                )
                count++
            }
            assertEquals("Should have all enum values", PlanProgressStatus.values().size, count)
            cursor.close()

            close()
        }
    }

    @Test
    @Throws(IOException::class)
    fun testInvalidProgressValue() {
        // Test that invalid progress values are handled
        helper.createDatabase(TEST_DB, 2).apply {
            execSQL(
                """
                INSERT INTO plan_days (id, planId, dayNumber, isRestDay, progress, createdAt)
                VALUES ('day1', 'plan1', 1, 0, 'INVALID_STATUS', ${System.currentTimeMillis()})
                """.trimIndent(),
            )

            // Room TypeConverter should handle invalid values when reading
            // This test verifies the data can be stored (migration doesn't validate enum values)
            val cursor = db.query("SELECT progress FROM plan_days WHERE id = 'day1'")
            assertTrue(cursor.moveToFirst())
            assertEquals("INVALID_STATUS", cursor.getString(0))
            cursor.close()

            close()
        }
    }
}

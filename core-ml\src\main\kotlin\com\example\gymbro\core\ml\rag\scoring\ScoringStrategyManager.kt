package com.example.gymbro.core.ml.rag.scoring

import com.example.gymbro.core.logging.Logger
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 评分策略管理器
 *
 * 负责A/B测试、动态策略切换和性能监控：
 * - 支持运行时策略切换
 * - A/B测试用户分组
 * - 性能指标收集
 * - 策略效果分析
 */
@Singleton
class ScoringStrategyManager @Inject constructor(
    private val defaultStrategy: DefaultScoringStrategy,
    private val advancedStrategy: AdvancedScoringStrategy,
    private val experimentalStrategy: ExperimentalScoringStrategy,
    private val conservativeStrategy: ConservativeScoringStrategy,
    private val logger: Logger,
) {

    /**
     * 评分策略类型枚举
     */
    enum class StrategyType(val displayName: String, val description: String) {
        DEFAULT("默认策略", "标准的时间衰减+会话惩罚算法"),
        ADVANCED("高级策略", "基于机器学习的多因素非线性算法"),
        EXPERIMENTAL("实验策略", "激进的新鲜度导向算法"),
        CONSERVATIVE("保守策略", "稳定均衡的传统算法"),
    }

    /**
     * A/B测试配置
     */
    data class ABTestConfig(
        val isEnabled: Boolean = false,
        val testName: String = "",
        val userGroupPercentages: Map<StrategyType, Float> = mapOf(
            StrategyType.DEFAULT to 0.4f,
            StrategyType.ADVANCED to 0.3f,
            StrategyType.EXPERIMENTAL to 0.2f,
            StrategyType.CONSERVATIVE to 0.1f,
        ),
    )

    private var currentABTestConfig = ABTestConfig()
    private var manualOverrideStrategy: StrategyType? = null

    /**
     * 获取当前生效的评分策略
     *
     * @param userId 用户ID（用于A/B测试分组）
     * @return 对应的评分策略实例
     */
    fun getCurrentStrategy(userId: String? = null): ScoringStrategy {
        // 1. 检查手动覆盖
        manualOverrideStrategy?.let { strategyType ->
            logger.d("ScoringStrategyManager", "使用手动覆盖策略: ${strategyType.displayName}")
            return getStrategy(strategyType)
        }

        // 2. 检查A/B测试
        if (currentABTestConfig.isEnabled && userId != null) {
            val assignedStrategy = getABTestStrategy(userId)
            logger.d("ScoringStrategyManager", "A/B测试分配策略: ${assignedStrategy.displayName} (用户: $userId)")
            return getStrategy(assignedStrategy)
        }

        // 3. 默认策略
        logger.d("ScoringStrategyManager", "使用默认策略")
        return defaultStrategy
    }

    /**
     * 根据类型获取策略实例
     */
    private fun getStrategy(type: StrategyType): ScoringStrategy {
        return when (type) {
            StrategyType.DEFAULT -> defaultStrategy
            StrategyType.ADVANCED -> advancedStrategy
            StrategyType.EXPERIMENTAL -> experimentalStrategy
            StrategyType.CONSERVATIVE -> conservativeStrategy
        }
    }

    /**
     * A/B测试用户分组算法
     * 基于用户ID的哈希值确保分组稳定性
     */
    private fun getABTestStrategy(userId: String): StrategyType {
        val hash = userId.hashCode()
        val normalizedHash = (hash.toDouble() / Int.MAX_VALUE.toDouble()).coerceIn(0.0, 1.0)

        var cumulativePercentage = 0f
        for ((strategy, percentage) in currentABTestConfig.userGroupPercentages) {
            cumulativePercentage += percentage
            if (normalizedHash <= cumulativePercentage) {
                return strategy
            }
        }

        // 兜底返回默认策略
        return StrategyType.DEFAULT
    }

    /**
     * 启动A/B测试
     *
     * @param testName 测试名称
     * @param userGroupPercentages 用户分组百分比
     */
    fun startABTest(
        testName: String,
        userGroupPercentages: Map<StrategyType, Float> = currentABTestConfig.userGroupPercentages,
    ) {
        // 验证百分比总和
        val totalPercentage = userGroupPercentages.values.sum()
        require(totalPercentage <= 1.0f) { "用户分组百分比总和不能超过100%" }

        currentABTestConfig = ABTestConfig(
            isEnabled = true,
            testName = testName,
            userGroupPercentages = userGroupPercentages,
        )

        logger.i("ScoringStrategyManager", "启动A/B测试: $testName, 分组: $userGroupPercentages")
    }

    /**
     * 停止A/B测试
     */
    fun stopABTest() {
        val previousTest = currentABTestConfig.testName
        currentABTestConfig = ABTestConfig(isEnabled = false)
        logger.i("ScoringStrategyManager", "停止A/B测试: $previousTest")
    }

    /**
     * 设置手动策略覆盖（用于调试和特殊场景）
     */
    fun setManualOverride(strategyType: StrategyType?) {
        manualOverrideStrategy = strategyType
        if (strategyType != null) {
            logger.i("ScoringStrategyManager", "设置手动策略覆盖: ${strategyType.displayName}")
        } else {
            logger.i("ScoringStrategyManager", "清除手动策略覆盖")
        }
    }

    /**
     * 获取当前配置状态
     */
    fun getCurrentConfig(): ScoringStrategyConfig {
        return ScoringStrategyConfig(
            isABTestEnabled = currentABTestConfig.isEnabled,
            currentTestName = currentABTestConfig.testName,
            manualOverride = manualOverrideStrategy,
            userGroupDistribution = currentABTestConfig.userGroupPercentages,
        )
    }

    /**
     * 评分策略性能基准测试
     *
     * @param testCases 测试用例列表
     * @return 性能报告
     */
    fun performBenchmark(testCases: List<ScoringTestCase>): ScoringBenchmarkReport {
        val results = mutableMapOf<StrategyType, BenchmarkResult>()

        StrategyType.values().forEach { strategyType ->
            val strategy = getStrategy(strategyType)
            val startTime = System.nanoTime()

            testCases.forEach { testCase ->
                strategy.calculateCombinedScore(
                    vectorScore = testCase.vectorScore,
                    keywordScore = testCase.keywordScore,
                    hybridWeight = testCase.hybridWeight,
                    timestamp = testCase.timestamp,
                    isCurrentSession = testCase.isCurrentSession,
                )
            }

            val endTime = System.nanoTime()
            val avgTimePerCall = (endTime - startTime) / testCases.size.toDouble()

            results[strategyType] = BenchmarkResult(
                averageTimeNanos = avgTimePerCall,
                testCaseCount = testCases.size,
            )
        }

        return ScoringBenchmarkReport(results)
    }
}

/**
 * 配置状态数据类
 */
data class ScoringStrategyConfig(
    val isABTestEnabled: Boolean,
    val currentTestName: String,
    val manualOverride: ScoringStrategyManager.StrategyType?,
    val userGroupDistribution: Map<ScoringStrategyManager.StrategyType, Float>,
)

/**
 * 性能测试用例
 */
data class ScoringTestCase(
    val vectorScore: Float,
    val keywordScore: Float,
    val hybridWeight: Float,
    val timestamp: Long,
    val isCurrentSession: Boolean,
)

/**
 * 基准测试结果
 */
data class BenchmarkResult(
    val averageTimeNanos: Double,
    val testCaseCount: Int,
) {
    val averageTimeMicros: Double = averageTimeNanos / 1000.0
    val averageTimeMillis: Double = averageTimeNanos / 1_000_000.0
}

/**
 * 性能基准报告
 */
data class ScoringBenchmarkReport(
    val results: Map<ScoringStrategyManager.StrategyType, BenchmarkResult>,
) {
    fun getFastestStrategy(): ScoringStrategyManager.StrategyType? {
        return results.minByOrNull { it.value.averageTimeNanos }?.key
    }

    fun getSlowestStrategy(): ScoringStrategyManager.StrategyType? {
        return results.maxByOrNull { it.value.averageTimeNanos }?.key
    }

    fun getPerformanceRatio(baseline: ScoringStrategyManager.StrategyType): Map<ScoringStrategyManager.StrategyType, Double> {
        val baselineTime = results[baseline]?.averageTimeNanos ?: return emptyMap()
        return results.mapValues { (_, result) ->
            result.averageTimeNanos / baselineTime
        }
    }
}

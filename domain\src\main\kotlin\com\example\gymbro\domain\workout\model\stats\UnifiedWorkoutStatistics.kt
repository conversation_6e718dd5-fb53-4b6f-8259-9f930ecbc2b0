package com.example.gymbro.domain.workout.model.stats

import androidx.compose.runtime.Immutable
import kotlinx.datetime.Clock
import kotlinx.datetime.LocalDate
import kotlinx.datetime.TimeZone
import kotlinx.datetime.daysUntil
import kotlinx.datetime.toLocalDateTime
import kotlinx.serialization.Serializable
import kotlin.math.abs

/**
 * 统一训练统计数据模型
 *
 * 作为跨层DTO使用，支持不同时间范围的统计数据聚合。
 * 用于周级、月级、年级统计的通用数据容器。
 *
 * 设计理念：
 * - 单一数据模型支持多时间维度
 * - 提供丰富的统计计算方法
 * - 支持趋势分析和比较
 * - 兼容现有的WeeklyStatsBar组件
 *
 * @property timeRange 时间范围
 * @property totalSessions 总训练次数
 * @property totalExercises 总练习数量
 * @property totalSets 总组数
 * @property totalReps 总次数
 * @property totalWeight 总重量(kg)
 * @property totalDuration 总训练时长(秒)
 * @property avgRpe 平均RPE
 * @property workoutDays 训练天数
 * @property restDays 休息天数
 * @property personalRecords PR记录数
 * @property dailyStats 日级统计详情
 * @property caloriesBurned 总消耗卡路里
 * @property avgHeartRate 平均心率
 * @property consistency 训练一致性百分比(0-100)
 */
@Immutable
@Serializable
data class UnifiedWorkoutStatistics(
    val timeRange: TimeRange = TimeRange.WEEK,
    val startDate: LocalDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date,
    val endDate: LocalDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date,
    val totalSessions: Int = 0,
    val totalExercises: Int = 0,
    val totalSets: Int = 0,
    val totalReps: Int = 0,
    val totalWeight: Double = 0.0,
    val totalDuration: Int = 0,
    val avgRpe: Float? = null,
    val workoutDays: Int = 0,
    val restDays: Int = 0,
    val personalRecords: Int = 0,
    val dailyStats: List<DailyStats> = emptyList(),
    val caloriesBurned: Int? = null,
    val avgHeartRate: Int? = null,
    val consistency: Float = 0f,
) {
    /**
     * 计算训练频率 (次/周)
     */
    fun getWorkoutFrequency(): Float {
        val weeks = when (timeRange) {
            TimeRange.WEEK -> 1f
            TimeRange.MONTH -> 4.33f
            TimeRange.YEAR -> 52f
            TimeRange.CUSTOM -> {
                val days = abs(startDate.daysUntil(endDate))
                (days / 7f).coerceAtLeast(1f)
            }
        }
        return totalSessions / weeks
    }

    /**
     * 计算平均每次训练时长(分钟)
     */
    fun getAvgSessionDuration(): Int {
        return if (totalSessions > 0) totalDuration / totalSessions / 60 else 0
    }

    /**
     * 计算平均每次训练组数
     */
    fun getAvgSetsPerSession(): Float {
        return if (totalSessions > 0) totalSets.toFloat() / totalSessions else 0f
    }

    /**
     * 计算平均每组重量
     */
    fun getAvgWeightPerSet(): Double {
        return if (totalSets > 0) totalWeight / totalSets else 0.0
    }

    /**
     * 获取训练强度分布
     */
    fun getIntensityDistribution(): Map<IntensityLevel, Int> {
        return dailyStats.groupingBy { it.getIntensityLevel() }
            .eachCount()
    }

    /**
     * 计算进步趋势 (相比上个时期的百分比变化)
     */
    fun calculateProgressTrend(previousStats: UnifiedWorkoutStatistics?): ProgressTrend {
        if (previousStats == null) {
            return ProgressTrend.flat()
        }

        val volumeChange = calculatePercentageChange(totalWeight, previousStats.totalWeight)
        val sessionsChange =
            calculatePercentageChange(totalSessions.toDouble(), previousStats.totalSessions.toDouble())
        val durationChange =
            calculatePercentageChange(totalDuration.toDouble(), previousStats.totalDuration.toDouble())

        return ProgressTrend(
            volumeChange = volumeChange,
            sessionsChange = sessionsChange,
            durationChange = durationChange,
        )
    }

    /**
     * 获取最活跃的训练日
     */
    fun getMostActiveDay(): Int? {
        return dailyStats.groupingBy { it.dayOfWeek }
            .eachCount()
            .maxByOrNull { it.value }
            ?.key
    }

    /**
     * 检查是否为活跃时期
     * 基于训练频率和一致性评估
     */
    fun isActivePeriod(): Boolean {
        return getWorkoutFrequency() >= 3f && consistency >= 0.7f
    }

    /**
     * 转换为WeeklyStatsBar兼容格式
     */
    fun toWeeklyStatsBarData(): WeeklyStatsBarData {
        return WeeklyStatsBarData(
            totalWorkouts = totalSessions,
            totalSets = totalSets,
            totalWeight = totalWeight,
            totalDuration = getFormattedDuration(),
            avgRpe = avgRpe,
            consistency = consistency,
        )
    }

    /**
     * 获取格式化的总时长
     */
    fun getFormattedDuration(): String {
        val hours = totalDuration / 3600
        val minutes = (totalDuration % 3600) / 60

        return when {
            hours > 0 -> "${hours}h ${minutes}m"
            minutes > 0 -> "${minutes}m"
            else -> "0m"
        }
    }

    /**
     * 获取格式化的总重量
     */
    fun getFormattedWeight(): String {
        return when {
            totalWeight >= 1000 -> "${(totalWeight / 1000).toInt()}k"
            totalWeight > 0 -> "${totalWeight.toInt()}kg"
            else -> "0kg"
        }
    }

    private fun calculatePercentageChange(current: Double, previous: Double): Float {
        if (previous == 0.0) return if (current > 0) 100f else 0f
        return ((current - previous) / previous * 100).toFloat()
    }

    companion object {
        /**
         * 创建空的统计数据
         */
        fun empty(timeRange: TimeRange = TimeRange.WEEK): UnifiedWorkoutStatistics {
            return UnifiedWorkoutStatistics(timeRange = timeRange)
        }

        /**
         * 从日级统计聚合创建
         */
        fun fromDailyStats(
            dailyStats: List<DailyStats>,
            timeRange: TimeRange,
        ): UnifiedWorkoutStatistics {
            if (dailyStats.isEmpty()) {
                return empty(timeRange)
            }

            val startDate = dailyStats.minOf { it.date }
            val endDate = dailyStats.maxOf { it.date }

            return UnifiedWorkoutStatistics(
                timeRange = timeRange,
                startDate = startDate,
                endDate = endDate,
                totalSessions = dailyStats.sumOf { it.completedSessions },
                totalExercises = dailyStats.sumOf { it.completedExercises },
                totalSets = dailyStats.sumOf { it.completedSets },
                totalReps = dailyStats.sumOf { it.totalReps },
                totalWeight = dailyStats.sumOf { it.totalWeight },
                totalDuration = dailyStats.sumOf { it.sessionDurationSec },
                avgRpe = dailyStats.mapNotNull { it.avgRpe }.let { rpes ->
                    if (rpes.isNotEmpty()) rpes.average().toFloat() else null
                },
                workoutDays = dailyStats.count { it.completedSessions > 0 },
                restDays = dailyStats.count { it.completedSessions == 0 },
                dailyStats = dailyStats,
                caloriesBurned = dailyStats.mapNotNull { it.caloriesBurned }.sum().takeIf { it > 0 },
                avgHeartRate = dailyStats.mapNotNull { it.averageHeartRate }.let { hrs ->
                    if (hrs.isNotEmpty()) hrs.average().toInt() else null
                },
                consistency = calculateConsistency(dailyStats, timeRange),
            )
        }

        private fun calculateConsistency(dailyStats: List<DailyStats>, timeRange: TimeRange): Float {
            if (dailyStats.isEmpty()) return 0f

            val workoutDays = dailyStats.count { it.completedSessions > 0 }
            val expectedDays = when (timeRange) {
                TimeRange.WEEK -> 7
                TimeRange.MONTH -> 30
                TimeRange.YEAR -> 365
                TimeRange.CUSTOM -> dailyStats.size
            }

            val targetWorkoutDays = (expectedDays * 0.5f).toInt() // 假设目标是50%天数训练
            return (workoutDays.toFloat() / targetWorkoutDays).coerceIn(0f, 1f)
        }
    }
}

/**
 * 时间范围枚举
 */
@Serializable
enum class TimeRange(val displayName: String, val days: Int) {
    WEEK("本周", 7),
    MONTH("本月", 30),
    YEAR("本年", 365),
    CUSTOM("自定义", 0),
}

/**
 * 进步趋势数据
 */
@Immutable
@Serializable
data class ProgressTrend(
    val volumeChange: Float = 0f, // 重量变化百分比
    val sessionsChange: Float = 0f, // 训练次数变化百分比
    val durationChange: Float = 0f, // 时长变化百分比
) {
    /**
     * 获取总体趋势方向
     */
    fun getOverallTrend(): TrendDirection {
        val avgChange = (volumeChange + sessionsChange + durationChange) / 3
        return when {
            avgChange > 5f -> TrendDirection.UP
            avgChange < -5f -> TrendDirection.DOWN
            else -> TrendDirection.FLAT
        }
    }

    companion object {
        fun flat() = ProgressTrend(0f, 0f, 0f)
    }
}

/**
 * 趋势方向
 */
@Serializable
enum class TrendDirection {
    UP, DOWN, FLAT
}

/**
 * WeeklyStatsBar兼容数据模型
 */
@Immutable
data class WeeklyStatsBarData(
    val totalWorkouts: Int,
    val totalSets: Int,
    val totalWeight: Double,
    val totalDuration: String,
    val avgRpe: Float?,
    val consistency: Float,
)
# ThinkingBox 架构深度分析

## 核心架构 (v3.0 MVI)
- **双时序架构**: 数据时序(Backend) + UI时序(Frontend) 完全分离
- **MVI组件**: Contract/ViewModel/Screen 三层架构
- **直接监听**: ThinkingBoxViewModel直接监听ConversationScope，消除重复Token流处理
- **双握手机制**: PhaseEnd事件(数据完成) + PhaseAnimFinished事件(UI完成) = 真正切换

## Phase 生命周期管理
1. **perthink阶段**: `<think>` → 立即显示 → `<thinking>` 结束
2. **正式phases**: `<phase id="X">` → 队列管理 → 双握手切换  
3. **final阶段**: `<final>` → 后台渲染 + 前台显示分离

## 关键状态控制
- `activePhaseId`: 当前激活phase (唯一真源)
- `pending`: 等待队列管理phase切换顺序
- `finalRichTextReady`: 控制final内容前台渲染时机
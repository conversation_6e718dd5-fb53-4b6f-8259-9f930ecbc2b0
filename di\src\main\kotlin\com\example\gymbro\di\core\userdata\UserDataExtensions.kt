package com.example.gymbro.di.core.userdata

import com.example.gymbro.core.userdata.internal.dao.UserAuthData
import com.example.gymbro.core.userdata.internal.dao.UserProfileData
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.UserProfile
import com.example.gymbro.domain.profile.model.user.enums.FitnessLevel
import com.example.gymbro.domain.profile.model.user.enums.Gender

/**
 * 用户数据转换扩展方法
 *
 * 提供 Domain 模型与 UserDataCenter 内部数据模型之间的转换。
 */

// === AuthUser 转换 ===

/**
 * 将 AuthUser 转换为 UserAuthData
 */
fun AuthUser.toUserAuthData(): UserAuthData {
    return UserAuthData(
        userId = this.uid,
        email = this.email,
        displayName = this.displayName,
        phoneNumber = this.phoneNumber,
        isAnonymous = this.isAnonymous,
        isEmailVerified = this.isEmailVerified,
        isPhoneVerified = this.isPhoneVerified,
        photoUrl = this.photoUrl,
        isActive = true,
        lastUpdated = System.currentTimeMillis(),
        createdAt = System.currentTimeMillis(),
    )
}

/**
 * 将 UserAuthData 转换为 AuthUser
 */
fun UserAuthData.toAuthUser(): AuthUser {
    return AuthUser(
        uid = this.userId,
        displayName = this.displayName,
        email = this.email,
        phoneNumber = this.phoneNumber,
        isAnonymous = this.isAnonymous,
        photoUrl = this.photoUrl,
        isEmailVerified = this.isEmailVerified,
        isPhoneVerified = this.isPhoneVerified,
    )
}

// === UserProfile 转换 ===

/**
 * 将 UserProfile 转换为 UserProfileData
 */
fun UserProfile.toUserProfileData(): UserProfileData {
    return UserProfileData(
        userId = this.userId,
        username = this.username,
        displayName = this.displayName,
        email = this.email,
        phoneNumber = this.phoneNumber,
        bio = this.bio,
        gender = this.gender.name,
        height = this.height,
        weight = this.weight,
        fitnessLevel = this.fitnessLevel.ordinal,
        fitnessGoals = this.fitnessGoals.map { it.name },
        workoutDays = this.workoutDays.map { it.name },
        allowPartnerMatching = this.allowPartnerMatching,
        totalActivityCount = this.totalActivityCount,
        weeklyActiveMinutes = this.weeklyActiveMinutes,
        lastUpdated = System.currentTimeMillis(),
        createdAt = System.currentTimeMillis(),
    )
}

/**
 * 将 UserProfileData 转换为 UserProfile
 */
fun UserProfileData.toUserProfile(): UserProfile {
    return UserProfile(
        userId = this.userId,
        username = this.username,
        displayName = this.displayName,
        email = this.email,
        phoneNumber = this.phoneNumber,
        bio = this.bio,
        gender = parseGender(this.gender),
        height = this.height,
        weight = this.weight,
        fitnessLevel = parseFitnessLevel(this.fitnessLevel),
        fitnessGoals = this.fitnessGoals.mapNotNull { goalName ->
            try {
                com.example.gymbro.domain.profile.model.user.FitnessGoal.valueOf(goalName)
            } catch (e: Exception) {
                null
            }
        },
        workoutDays = this.workoutDays.mapNotNull { dayName ->
            try {
                com.example.gymbro.domain.profile.model.user.WorkoutDay.valueOf(dayName)
            } catch (e: Exception) {
                null
            }
        },
        allowPartnerMatching = this.allowPartnerMatching,
        totalActivityCount = this.totalActivityCount,
        weeklyActiveMinutes = this.weeklyActiveMinutes,
    )
}

// === 私有辅助方法 ===

/**
 * 解析性别枚举
 */
private fun parseGender(genderString: String?): Gender {
    return try {
        Gender.valueOf(genderString ?: "OTHER")
    } catch (e: Exception) {
        Gender.OTHER
    }
}

/**
 * 解析健身水平枚举
 */
private fun parseFitnessLevel(fitnessLevelOrdinal: Int?): FitnessLevel {
    return try {
        val ordinal = fitnessLevelOrdinal ?: 0
        if (ordinal >= 0 && ordinal < FitnessLevel.values().size) {
            FitnessLevel.values()[ordinal]
        } else {
            FitnessLevel.BEGINNER
        }
    } catch (e: Exception) {
        FitnessLevel.BEGINNER
    }
}

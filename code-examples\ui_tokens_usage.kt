package com.example.gymbro.examples

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors
import com.example.gymbro.designSystem.components.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 设计系统Token使用规范示例
 * 
 * 本示例展示了GymBro项目中Token系统的正确使用方式：
 * 1. Tokens.Color.*颜色系统使用
 * 2. MaterialTheme.workoutColors.*主题色彩
 * 3. Tokens.Spacing.*间距系统
 * 4. Tokens.Radius.*圆角系统
 * 5. Tokens.Elevation.*阴影系统
 * 6. 避免硬编码值的最佳实践
 * 
 * 参考标准：
 * - 100% Token化，零硬编码值
 * - 优先使用语义化Token
 * - 支持13级灰阶视觉层次
 * - 三种主题风格差异化
 */

// ================================
// 1. 颜色Token使用示例
// ================================

/**
 * 颜色Token使用规范展示
 */
@Composable
fun ColorTokensExample(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(Tokens.Spacing.Medium), // 🔥 使用Token间距
        verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium) // 🔥 使用Token间距
    ) {
        Text(
            text = "颜色Token使用示例",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.workoutColors.aiCoachText // 🔥 使用主题文本色
        )
        
        // ✅ 正确：使用Token颜色系统
        Card(
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.workoutColors.cardBackground, // 🔥 使用主题卡片背景
                contentColor = MaterialTheme.workoutColors.aiCoachText // 🔥 使用主题文本色
            ),
            border = BorderStroke(
                width = Tokens.Border.Thin, // 🔥 使用Token边框宽度
                color = MaterialTheme.workoutColors.cardBorder // 🔥 使用主题边框色
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = Tokens.Elevation.Card // 🔥 使用Token阴影
            ),
            shape = RoundedCornerShape(Tokens.Radius.Card) // 🔥 使用Token圆角
        ) {
            Column(
                modifier = Modifier.padding(Tokens.Spacing.Medium) // 🔥 使用Token内边距
            ) {
                Text(
                    text = "✅ 正确的Token使用",
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.workoutColors.successPrimary // 🔥 使用主题成功色
                )
                
                Spacer(modifier = Modifier.height(Tokens.Spacing.Small)) // 🔥 使用Token间距
                
                Text(
                    text = "使用MaterialTheme.workoutColors.*获取主题色彩",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.workoutColors.accentSecondary // 🔥 使用主题次要色
                )
            }
        }
        
        // ❌ 错误示例：硬编码颜色（仅用于对比展示）
        Card(
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF2A2A2A), // ❌ 硬编码颜色
                contentColor = Color(0xFFFFFFFF) // ❌ 硬编码颜色
            ),
            border = BorderStroke(
                width = 1.dp, // ❌ 硬编码尺寸
                color = Color(0xFF444444) // ❌ 硬编码颜色
            ),
            elevation = CardDefaults.cardElevation(
                defaultElevation = 4.dp // ❌ 硬编码阴影
            ),
            shape = RoundedCornerShape(8.dp) // ❌ 硬编码圆角
        ) {
            Column(
                modifier = Modifier.padding(16.dp) // ❌ 硬编码内边距
            ) {
                Text(
                    text = "❌ 错误的硬编码使用",
                    style = MaterialTheme.typography.titleMedium,
                    color = Color(0xFFFF5722) // ❌ 硬编码颜色
                )
                
                Spacer(modifier = Modifier.height(8.dp)) // ❌ 硬编码间距
                
                Text(
                    text = "避免使用硬编码的颜色和尺寸值",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFF9E9E9E) // ❌ 硬编码颜色
                )
            }
        }
    }
}

// ================================
// 2. 间距Token使用示例
// ================================

/**
 * 间距Token使用规范展示
 */
@Composable
fun SpacingTokensExample(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(Tokens.Spacing.Medium) // 🔥 使用Token间距
    ) {
        Text(
            text = "间距Token使用示例",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.workoutColors.aiCoachText // 🔥 使用主题文本色
        )
        
        Spacer(modifier = Modifier.height(Tokens.Spacing.Medium)) // 🔥 使用Token间距
        
        // 不同间距级别展示
        val spacingLevels = listOf(
            "XSmall" to Tokens.Spacing.XSmall,
            "Small" to Tokens.Spacing.Small,
            "Medium" to Tokens.Spacing.Medium,
            "Large" to Tokens.Spacing.Large,
            "XLarge" to Tokens.Spacing.XLarge
        )
        
        spacingLevels.forEach { (name, spacing) ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Tokens.Spacing.$name:",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.workoutColors.aiCoachText, // 🔥 使用主题文本色
                    modifier = Modifier.width(200.dp)
                )
                
                Box(
                    modifier = Modifier
                        .width(spacing) // 🔥 使用Token间距
                        .height(Tokens.Spacing.Small) // 🔥 使用Token间距
                        .background(
                            MaterialTheme.workoutColors.accentPrimary, // 🔥 使用主题强调色
                            RoundedCornerShape(Tokens.Radius.Small) // 🔥 使用Token圆角
                        )
                )
            }
            
            Spacer(modifier = Modifier.height(Tokens.Spacing.Small)) // 🔥 使用Token间距
        }
    }
}

// ================================
// 3. 圆角Token使用示例
// ================================

/**
 * 圆角Token使用规范展示
 */
@Composable
fun RadiusTokensExample(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(Tokens.Spacing.Medium) // 🔥 使用Token间距
    ) {
        Text(
            text = "圆角Token使用示例",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.workoutColors.aiCoachText // 🔥 使用主题文本色
        )
        
        Spacer(modifier = Modifier.height(Tokens.Spacing.Medium)) // 🔥 使用Token间距
        
        // 不同圆角级别展示
        val radiusLevels = listOf(
            "Small" to Tokens.Radius.Small,
            "Medium" to Tokens.Radius.Medium,
            "Large" to Tokens.Radius.Large,
            "Card" to Tokens.Radius.Card,
            "Button" to Tokens.Radius.Button
        )
        
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium), // 🔥 使用Token间距
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium) // 🔥 使用Token间距
        ) {
            items(radiusLevels.size) { index ->
                val (name, radius) = radiusLevels[index]
                
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp),
                    shape = RoundedCornerShape(radius), // 🔥 使用Token圆角
                    color = MaterialTheme.workoutColors.cardBackground, // 🔥 使用主题卡片背景
                    border = BorderStroke(
                        width = Tokens.Border.Thin, // 🔥 使用Token边框宽度
                        color = MaterialTheme.workoutColors.cardBorder // 🔥 使用主题边框色
                    )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "Tokens.Radius.$name",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.workoutColors.aiCoachText // 🔥 使用主题文本色
                        )
                    }
                }
            }
        }
    }
}

// ================================
// 4. 阴影Token使用示例
// ================================

/**
 * 阴影Token使用规范展示
 */
@Composable
fun ElevationTokensExample(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(Tokens.Spacing.Medium) // 🔥 使用Token间距
    ) {
        Text(
            text = "阴影Token使用示例",
            style = MaterialTheme.typography.headlineSmall,
            color = MaterialTheme.workoutColors.aiCoachText // 🔥 使用主题文本色
        )
        
        Spacer(modifier = Modifier.height(Tokens.Spacing.Medium)) // 🔥 使用Token间距
        
        // 不同阴影级别展示
        val elevationLevels = listOf(
            "Small" to Tokens.Elevation.Small,
            "Medium" to Tokens.Elevation.Medium,
            "Large" to Tokens.Elevation.Large,
            "Card" to Tokens.Elevation.Card,
            "Button" to Tokens.Elevation.Button
        )
        
        LazyVerticalGrid(
            columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Large), // 🔥 使用Token间距
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Large) // 🔥 使用Token间距
        ) {
            items(elevationLevels.size) { index ->
                val (name, elevation) = elevationLevels[index]
                
                Surface(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp),
                    shape = RoundedCornerShape(Tokens.Radius.Card), // 🔥 使用Token圆角
                    color = MaterialTheme.workoutColors.cardBackground, // 🔥 使用主题卡片背景
                    shadowElevation = elevation // 🔥 使用Token阴影
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "Tokens.Elevation.$name",
                            style = MaterialTheme.typography.labelMedium,
                            color = MaterialTheme.workoutColors.aiCoachText // 🔥 使用主题文本色
                        )
                    }
                }
            }
        }
    }
}

// ================================
// 5. 预览组件
// ================================

@GymBroPreview
@Composable
private fun ColorTokensExamplePreview() {
    GymBroTheme {
        ColorTokensExample()
    }
}

@GymBroPreview
@Composable
private fun SpacingTokensExamplePreview() {
    GymBroTheme {
        SpacingTokensExample()
    }
}

@GymBroPreview
@Composable
private fun RadiusTokensExamplePreview() {
    GymBroTheme {
        RadiusTokensExample()
    }
}

@GymBroPreview
@Composable
private fun ElevationTokensExamplePreview() {
    GymBroTheme {
        ElevationTokensExample()
    }
}

/**
 * 完整Token使用示例屏幕
 */
@GymBroPreview
@Composable
private fun CompleteTokenUsageExamplePreview() {
    GymBroTheme {
        LazyColumn(
            modifier = Modifier.fillMaxSize(),
            contentPadding = PaddingValues(Tokens.Spacing.Medium), // 🔥 使用Token间距
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Large) // 🔥 使用Token间距
        ) {
            item { ColorTokensExample() }
            item { SpacingTokensExample() }
            item { RadiusTokensExample() }
            item { ElevationTokensExample() }
        }
    }
}

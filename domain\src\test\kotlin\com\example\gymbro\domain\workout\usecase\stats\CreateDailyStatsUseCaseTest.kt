package com.example.gymbro.domain.workout.usecase.stats

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.workout.model.SessionExercise
import com.example.gymbro.domain.workout.model.SessionSet
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.model.stats.DailyStats
import com.example.gymbro.domain.workout.repository.StatsRepository
import com.example.gymbro.domain.workout.usecase.session.GetWorkoutSessionsUseCase
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.LocalDate
import org.junit.Before
import org.junit.Test
import kotlin.test.assertTrue

/**
 * CreateDailyStatsUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试UseCase的核心业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证ModernResult返回类型
 * ✔️ 测试基于真实数据的统计计算
 */
class CreateDailyStatsUseCaseTest {

    @MockK
    private lateinit var statsRepository: StatsRepository

    @MockK
    private lateinit var getWorkoutSessionsUseCase: GetWorkoutSessionsUseCase

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: CreateDailyStatsUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = CreateDailyStatsUseCase(
            statsRepository = statsRepository,
            getWorkoutSessionsUseCase = getWorkoutSessionsUseCase,
            ioDispatcher = testDispatcher,
        )
    }

    @Test
    fun `invoke should create daily stats from real session data successfully`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val targetDate = LocalDate(2024, 1, 15)
        val sessions = listOf(
            createCompletedSession(userId),
            createCompletedSession(userId, sessionId = "session-2"),
        )

        coEvery { getWorkoutSessionsUseCase.invoke() } returns flowOf(ModernResult.Success(sessions))
        coEvery { statsRepository.saveDailyStats(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(userId, targetDate)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 1) { statsRepository.saveDailyStats(any()) }
    }

    @Test
    fun `invoke should use current date when date parameter is null`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val sessions = listOf(createCompletedSession(userId))

        coEvery { getWorkoutSessionsUseCase.invoke() } returns flowOf(ModernResult.Success(sessions))
        coEvery { statsRepository.saveDailyStats(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(userId, null)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 1) { statsRepository.saveDailyStats(any()) }
    }

    @Test
    fun `invoke should propagate error when getWorkoutSessions fails`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val targetDate = LocalDate(2024, 1, 15)
        val error = mockk<Exception>()

        coEvery { getWorkoutSessionsUseCase.invoke() } returns flowOf(ModernResult.Error(error))

        // When
        val result = useCase.invoke(userId, targetDate)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 0) { statsRepository.saveDailyStats(any()) }
    }

    @Test
    fun `invoke should handle loading state from getWorkoutSessions`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val targetDate = LocalDate(2024, 1, 15)

        coEvery { getWorkoutSessionsUseCase.invoke() } returns flowOf(ModernResult.Loading)

        // When
        val result = useCase.invoke(userId, targetDate)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 0) { statsRepository.saveDailyStats(any()) }
    }

    @Test
    fun `invoke should filter sessions by userId correctly`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val otherUserId = "user-456"
        val targetDate = LocalDate(2024, 1, 15)
        val sessions = listOf(
            createCompletedSession(userId, sessionId = "session-1"),
            createCompletedSession(otherUserId, sessionId = "session-2"), // 不同用户
            createCompletedSession(userId, sessionId = "session-3"),
        )

        coEvery { getWorkoutSessionsUseCase.invoke() } returns flowOf(ModernResult.Success(sessions))
        coEvery { statsRepository.saveDailyStats(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(userId, targetDate)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 1) { statsRepository.saveDailyStats(any()) }
    }

    @Test
    fun `invoke should calculate stats correctly for empty session list`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val targetDate = LocalDate(2024, 1, 15)
        val emptySessions = emptyList<WorkoutSession>()

        coEvery { getWorkoutSessionsUseCase.invoke() } returns flowOf(ModernResult.Success(emptySessions))
        coEvery { statsRepository.saveDailyStats(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(userId, targetDate)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 1) { statsRepository.saveDailyStats(any()) }
    }

    @Test
    fun `invoke should calculate stats correctly for sessions with different statuses`() = runTest(
        testDispatcher,
    ) {
        // Given
        val userId = "user-123"
        val targetDate = LocalDate(2024, 1, 15)
        val sessions = listOf(
            createSession(userId, "session-1", "COMPLETED"),
            createSession(userId, "session-2", "IN_PROGRESS"),
            createSession(userId, "session-3", "ABORTED"),
            createSession(userId, "session-4", "COMPLETED"),
        )

        coEvery { getWorkoutSessionsUseCase.invoke() } returns flowOf(ModernResult.Success(sessions))
        coEvery { statsRepository.saveDailyStats(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(userId, targetDate)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 1) { statsRepository.saveDailyStats(any()) }
    }

    @Test
    fun `invoke should handle statsRepository save error`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val targetDate = LocalDate(2024, 1, 15)
        val sessions = listOf(createCompletedSession(userId))
        val saveError = mockk<Exception>()

        coEvery { getWorkoutSessionsUseCase.invoke() } returns flowOf(ModernResult.Success(sessions))
        coEvery { statsRepository.saveDailyStats(any()) } returns ModernResult.Error(saveError)

        // When
        val result = useCase.invoke(userId, targetDate)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 1) { statsRepository.saveDailyStats(any()) }
    }

    @Test
    fun `invoke should handle complex session data with multiple exercises and sets`() = runTest(
        testDispatcher,
    ) {
        // Given
        val userId = "user-123"
        val targetDate = LocalDate(2024, 1, 15)
        val complexSession = createComplexSession(userId)
        val sessions = listOf(complexSession)

        coEvery { getWorkoutSessionsUseCase.invoke() } returns flowOf(ModernResult.Success(sessions))
        coEvery { statsRepository.saveDailyStats(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(userId, targetDate)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 1) { statsRepository.saveDailyStats(any()) }
    }

    @Test
    fun `invoke should handle exception gracefully`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val targetDate = LocalDate(2024, 1, 15)

        coEvery { getWorkoutSessionsUseCase.invoke() } throws RuntimeException("Test exception")

        // When
        val result = useCase.invoke(userId, targetDate)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { getWorkoutSessionsUseCase.invoke() }
        coVerify(exactly = 0) { statsRepository.saveDailyStats(any()) }
    }

    // === 辅助方法 ===

    private fun createSession(
        userId: String,
        sessionId: String,
        status: String,
    ): WorkoutSession {
        val currentTime = System.currentTimeMillis()
        return WorkoutSession(
            id = sessionId,
            name = "测试训练会话",
            templateId = null,
            status = status,
            userId = userId,
            startTime = if (status != "DRAFT") currentTime - 3600000 else null, // 1小时前开始
            endTime = if (status == "COMPLETED") currentTime else null,
            exercises = emptyList(),
            createdAt = currentTime,
            updatedAt = currentTime,
        )
    }

    private fun createCompletedSession(
        userId: String,
        sessionId: String = "session-123",
    ): WorkoutSession {
        return createSession(userId, sessionId, "COMPLETED").copy(
            exercises = listOf(createSampleExercise(sessionId)),
        )
    }

    private fun createComplexSession(userId: String): WorkoutSession {
        val sessionId = "complex-session"
        val exercises = listOf(
            createSampleExercise(sessionId, exerciseId = "exercise-1", completedSets = 3),
            createSampleExercise(sessionId, exerciseId = "exercise-2", completedSets = 4),
            createSampleExercise(sessionId, exerciseId = "exercise-3", completedSets = 2),
        )

        return createSession(userId, sessionId, "COMPLETED").copy(
            exercises = exercises,
        )
    }

    private fun createSampleExercise(
        sessionId: String,
        exerciseId: String = "exercise-123",
        completedSets: Int = 3,
    ): SessionExercise {
        val sets = (1..completedSets).map { setNumber ->
            SessionSet(
                id = "set-$setNumber",
                sessionId = sessionId,
                exerciseId = exerciseId,
                setNumber = setNumber,
                weight = 100.0,
                reps = 10,
                restDuration = 60,
                isCompleted = true,
                completedAt = System.currentTimeMillis(),
                rpe = 8,
                notes = null,
            )
        }

        return SessionExercise(
            id = "session-exercise-$exerciseId",
            sessionId = sessionId,
            exerciseId = exerciseId,
            order = 0,
            name = "测试动作",
            targetSets = completedSets,
            status = "COMPLETED",
            isCompleted = true,
            sets = sets,
            startTime = System.currentTimeMillis() - 3600000,
            endTime = System.currentTimeMillis(),
            notes = null,
        )
    }

    private fun createSampleDailyStats(
        userId: String,
        date: LocalDate,
    ): DailyStats {
        return DailyStats(
            userId = userId,
            date = date,
            completedSessions = 2,
            completedExercises = 6,
            completedSets = 18,
            totalReps = 180,
            totalWeight = 18000.0,
            avgRpe = 8.0f,
            sessionDurationSec = 3600,
        )
    }
}
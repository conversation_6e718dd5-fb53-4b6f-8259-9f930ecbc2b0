package com.example.gymbro.data.workout.template.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 * 模板排序实体类
 *
 * 用于持久化保存模板的排序信息，支持用户自定义排序和拖拽排序功能。
 *
 * 🏗️ 架构设计：
 * - 独立的排序表，与模板表解耦
 * - 支持多用户排序隔离
 * - 自动时间戳管理
 * - 高效的查询索引
 *
 * 🔧 使用场景：
 * - 模板列表的拖拽排序
 * - 用户自定义模板顺序
 * - 置顶功能实现
 * - 排序状态持久化
 *
 * @property id 主键ID，自动生成
 * @property templateId 关联的模板ID
 * @property userId 用户ID，支持多用户隔离
 * @property sortOrder 排序顺序，数值越小越靠前
 * @property createdAt 创建时间戳
 * @property updatedAt 更新时间戳
 */
@Entity(
    tableName = "template_order",
    indices = [
        Index(value = ["template_id"], unique = false),
        Index(value = ["user_id"], unique = false),
        Index(value = ["user_id", "sort_order"], unique = false),
        Index(value = ["user_id", "template_id"], unique = true), // 每个用户的每个模板只能有一个排序记录
    ],
)
data class TemplateOrderEntity(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    val id: Long = 0,

    @ColumnInfo(name = "template_id")
    val templateId: String,

    @ColumnInfo(name = "user_id")
    val userId: String,

    @ColumnInfo(name = "sort_order")
    val sortOrder: Int,

    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),

    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis(),
)

/**
 * 草稿排序实体类
 *
 * 用于持久化保存草稿的排序信息，与模板排序分离管理。
 *
 * 🏗️ 架构设计：
 * - 与 TemplateOrderEntity 结构一致
 * - 独立的草稿排序管理
 * - 支持草稿到模板的排序迁移
 *
 * @property id 主键ID，自动生成
 * @property draftId 关联的草稿ID
 * @property userId 用户ID，支持多用户隔离
 * @property sortOrder 排序顺序，数值越小越靠前
 * @property createdAt 创建时间戳
 * @property updatedAt 更新时间戳
 */
@Entity(
    tableName = "draft_order",
    indices = [
        Index(value = ["draft_id"], unique = false),
        Index(value = ["user_id"], unique = false),
        Index(value = ["user_id", "sort_order"], unique = false),
        Index(value = ["user_id", "draft_id"], unique = true), // 每个用户的每个草稿只能有一个排序记录
    ],
)
data class DraftOrderEntity(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    val id: Long = 0,

    @ColumnInfo(name = "draft_id")
    val draftId: String,

    @ColumnInfo(name = "user_id")
    val userId: String,

    @ColumnInfo(name = "sort_order")
    val sortOrder: Int,

    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),

    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis(),
)

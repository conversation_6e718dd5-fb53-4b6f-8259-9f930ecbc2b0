import os
import re
import sys
from urllib.parse import urlparse

MISSING = []

# Walk through repository for markdown files
for root, dirs, files in os.walk('.'):
    # Skip hidden directories (like .git)
    dirs[:] = [d for d in dirs if not d.startswith('.')]
    for name in files:
        if name.endswith('.md'):
            path = os.path.join(root, name)
            with open(path, 'r', encoding='utf-8') as f:
                content = f.read()
            # Find markdown links: [text](url)
            for match in re.findall(r"\[[^\]]+\]\(([^)]+)\)", content):
                url = match.split('#')[0]  # Remove anchor
                if url.startswith('http') or url.startswith('mailto:') or url.startswith('#'):
                    continue
                if urlparse(url).scheme != '':
                    # Skip other schemes
                    continue
                # Normalise path relative to current file
                link_path = os.path.normpath(os.path.join(root, url))
                if not os.path.exists(link_path):
                    MISSING.append(f'{path}: {url}')

if MISSING:
    print('Missing links found:')
    for item in MISSING:
        print('  ' + item)
    sys.exit(1)
else:
    print('All local links valid.')

package com.example.gymbro.examples

import androidx.room.Dao
import androidx.room.Entity
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.PrimaryKey
import androidx.room.Query
import androidx.room.Update
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.network.api.ApiService
import com.example.gymbro.core.network.dto.ApiResponse
import dagger.hilt.android.scopes.ViewModelScoped
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import timber.log.Timber
import javax.inject.Inject

/**
 * Repository模式实现示例
 *
 * 本示例展示了GymBro项目中Repository层的完整实现模式：
 * 1. Local-First架构设计
 * 2. 双重数据源管理（本地+远程）
 * 3. ModernResult错误处理
 * 4. Flow响应式编程
 * 5. 缓存策略和数据同步
 *
 * 架构特点：
 * - 单一真实来源：本地数据库作为唯一数据源
 * - 网络层透明：Repository封装网络复杂性
 * - 错误统一处理：ModernResult包装所有操作结果
 * - 响应式更新：Flow支持实时数据订阅
 */

// ========================================
// 1. 数据模型定义
// ========================================

/**
 * 示例实体类 - 训练记录
 */
@Entity(tableName = "workout_records")
data class WorkoutRecordEntity(
    @PrimaryKey val id: String,
    val title: String,
    val description: String,
    val duration: Long, // 训练时长(分钟)
    val calories: Int,
    val difficulty: Int, // 1-5难度等级
    val tags: String, // JSON格式标签
    val createdAt: Long,
    val updatedAt: Long,
    val syncStatus: SyncStatus = SyncStatus.LOCAL_ONLY
)

/**
 * 同步状态枚举
 */
enum class SyncStatus {
    LOCAL_ONLY,     // 仅本地
    SYNCED,         // 已同步
    PENDING_SYNC,   // 待同步
    SYNC_FAILED     // 同步失败
}

/**
 * 领域模型 - 训练记录
 */
data class WorkoutRecord(
    val id: String,
    val title: String,
    val description: String,
    val duration: Long,
    val calories: Int,
    val difficulty: Int,
    val tags: List<String>,
    val createdAt: Long,
    val updatedAt: Long,
    val isSynced: Boolean
)

/**
 * 网络DTO - 训练记录
 */
data class WorkoutRecordDto(
    val id: String,
    val title: String,
    val description: String,
    val duration: Long,
    val calories: Int,
    val difficulty: Int,
    val tags: List<String>,
    val created_at: Long,
    val updated_at: Long
)

// ========================================
// 2. 数据访问层 (DAO)
// ========================================

/**
 * Room DAO接口
 *
 * 设计原则：
 * - 提供Flow响应式查询
 * - 支持批量操作
 * - 包含同步状态管理
 */
@Dao
interface WorkoutRecordDao {
    
    /**
     * 响应式查询所有记录
     */
    @Query("SELECT * FROM workout_records ORDER BY created_at DESC")
    fun getAllRecordsFlow(): Flow<List<WorkoutRecordEntity>>
    
    /**
     * 根据ID查询单条记录
     */
    @Query("SELECT * FROM workout_records WHERE id = :id")
    suspend fun getRecordById(id: String): WorkoutRecordEntity?
    
    /**
     * 插入或更新记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecord(record: WorkoutRecordEntity)
    
    /**
     * 批量插入记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecords(records: List<WorkoutRecordEntity>)
    
    /**
     * 更新记录
     */
    @Update
    suspend fun updateRecord(record: WorkoutRecordEntity)
    
    /**
     * 删除记录
     */
    @Query("DELETE FROM workout_records WHERE id = :id")
    suspend fun deleteRecord(id: String)
    
    /**
     * 查询待同步记录
     */
    @Query("SELECT * FROM workout_records WHERE sync_status IN (:statuses)")
    suspend fun getRecordsByStatus(statuses: List<SyncStatus>): List<WorkoutRecordEntity>
    
    /**
     * 更新同步状态
     */
    @Query("UPDATE workout_records SET sync_status = :status WHERE id = :id")
    suspend fun updateSyncStatus(id: String, status: SyncStatus)
    
    /**
     * 搜索记录
     */
    @Query("""
        SELECT * FROM workout_records 
        WHERE title LIKE '%' || :query || '%' 
        OR description LIKE '%' || :query || '%'
        ORDER BY created_at DESC
    """)
    fun searchRecords(query: String): Flow<List<WorkoutRecordEntity>>
}

// ========================================
// 3. 网络服务接口
// ========================================

/**
 * 网络API服务接口
 */
interface WorkoutRecordApiService {
    suspend fun getWorkoutRecords(): ApiResponse<List<WorkoutRecordDto>>
    suspend fun getWorkoutRecord(id: String): ApiResponse<WorkoutRecordDto>
    suspend fun createWorkoutRecord(record: WorkoutRecordDto): ApiResponse<WorkoutRecordDto>
    suspend fun updateWorkoutRecord(id: String, record: WorkoutRecordDto): ApiResponse<WorkoutRecordDto>
    suspend fun deleteWorkoutRecord(id: String): ApiResponse<Unit>
    suspend fun syncWorkoutRecords(records: List<WorkoutRecordDto>): ApiResponse<List<WorkoutRecordDto>>
}

// ========================================
// 4. 数据映射器
// ========================================

/**
 * 数据映射器 - Entity <-> Domain <-> DTO转换
 *
 * 职责：
 * - 数据模型转换
 * - 数据格式标准化
 * - 类型安全保证
 */
object WorkoutRecordMapper {
    
    /**
     * Entity -> Domain转换
     */
    fun entityToDomain(entity: WorkoutRecordEntity): WorkoutRecord {
        return WorkoutRecord(
            id = entity.id,
            title = entity.title,
            description = entity.description,
            duration = entity.duration,
            calories = entity.calories,
            difficulty = entity.difficulty,
            tags = parseTagsFromJson(entity.tags),
            createdAt = entity.createdAt,
            updatedAt = entity.updatedAt,
            isSynced = entity.syncStatus == SyncStatus.SYNCED
        )
    }
    
    /**
     * Domain -> Entity转换
     */
    fun domainToEntity(domain: WorkoutRecord): WorkoutRecordEntity {
        return WorkoutRecordEntity(
            id = domain.id,
            title = domain.title,
            description = domain.description,
            duration = domain.duration,
            calories = domain.calories,
            difficulty = domain.difficulty,
            tags = tagsToJson(domain.tags),
            createdAt = domain.createdAt,
            updatedAt = domain.updatedAt,
            syncStatus = if (domain.isSynced) SyncStatus.SYNCED else SyncStatus.LOCAL_ONLY
        )
    }
    
    /**
     * DTO -> Entity转换
     */
    fun dtoToEntity(dto: WorkoutRecordDto): WorkoutRecordEntity {
        return WorkoutRecordEntity(
            id = dto.id,
            title = dto.title,
            description = dto.description,
            duration = dto.duration,
            calories = dto.calories,
            difficulty = dto.difficulty,
            tags = tagsToJson(dto.tags),
            createdAt = dto.created_at,
            updatedAt = dto.updated_at,
            syncStatus = SyncStatus.SYNCED
        )
    }
    
    /**
     * Entity -> DTO转换
     */
    fun entityToDto(entity: WorkoutRecordEntity): WorkoutRecordDto {
        return WorkoutRecordDto(
            id = entity.id,
            title = entity.title,
            description = entity.description,
            duration = entity.duration,
            calories = entity.calories,
            difficulty = entity.difficulty,
            tags = parseTagsFromJson(entity.tags),
            created_at = entity.createdAt,
            updated_at = entity.updatedAt
        )
    }
    
    private fun parseTagsFromJson(json: String): List<String> {
        return try {
            // 简化的JSON解析，实际使用Kotlinx Serialization
            json.removeSurrounding("[", "]")
                .split(",")
                .map { it.trim().removeSurrounding("\"") }
                .filter { it.isNotEmpty() }
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    private fun tagsToJson(tags: List<String>): String {
        return "[${tags.joinToString(",") { "\"$it\"" }}]"
    }
}

// ========================================
// 5. Repository接口定义
// ========================================

/**
 * Repository接口
 *
 * 定义数据访问契约：
 * - 响应式数据流
 * - ModernResult错误处理
 * - 同步状态管理
 */
interface WorkoutRecordRepository {
    fun getAllRecords(): Flow<ModernResult<List<WorkoutRecord>>>
    suspend fun getRecordById(id: String): ModernResult<WorkoutRecord>
    suspend fun createRecord(record: WorkoutRecord): ModernResult<WorkoutRecord>
    suspend fun updateRecord(record: WorkoutRecord): ModernResult<WorkoutRecord>
    suspend fun deleteRecord(id: String): ModernResult<Unit>
    fun searchRecords(query: String): Flow<ModernResult<List<WorkoutRecord>>>
    suspend fun syncWithRemote(): ModernResult<Unit>
    suspend fun refreshFromRemote(): ModernResult<List<WorkoutRecord>>
}

// ========================================
// 6. Repository实现
// ========================================

/**
 * Repository实现类
 *
 * Local-First架构实现：
 * 1. 本地数据库作为单一真实来源
 * 2. 网络层负责数据同步
 * 3. 错误统一处理和恢复
 * 4. 缓存策略和离线支持
 */
@ViewModelScoped
class WorkoutRecordRepositoryImpl @Inject constructor(
    private val dao: WorkoutRecordDao,
    private val apiService: WorkoutRecordApiService,
    private val networkChecker: NetworkChecker
) : WorkoutRecordRepository {

    override fun getAllRecords(): Flow<ModernResult<List<WorkoutRecord>>> {
        return dao.getAllRecordsFlow()
            .map { entities ->
                val records = entities.map { WorkoutRecordMapper.entityToDomain(it) }
                ModernResult.Success(records)
            }
            .onStart {
                // 首次加载时尝试从网络刷新
                if (networkChecker.isNetworkAvailable()) {
                    try {
                        refreshFromRemoteInternal()
                    } catch (e: Exception) {
                        Timber.w(e, "后台刷新失败，使用本地数据")
                    }
                }
            }
            .catch { error ->
                emit(ModernResult.Error(error.toModernDataError("获取训练记录")))
            }
    }

    override suspend fun getRecordById(id: String): ModernResult<WorkoutRecord> {
        return try {
            val entity = dao.getRecordById(id)
            if (entity != null) {
                val record = WorkoutRecordMapper.entityToDomain(entity)
                ModernResult.Success(record)
            } else {
                ModernResult.Error(
                    ModernDataError(
                        operationName = "获取训练记录",
                        errorType = GlobalErrorType.Data.NotFound,
                        category = ErrorCategory.DATA,
                        uiMessage = UiText.DynamicString("未找到指定的训练记录")
                    )
                )
            }
        } catch (e: Exception) {
            Timber.e(e, "获取训练记录失败: $id")
            ModernResult.Error(e.toModernDataError("获取训练记录"))
        }
    }

    override suspend fun createRecord(record: WorkoutRecord): ModernResult<WorkoutRecord> {
        return try {
            val entity = WorkoutRecordMapper.domainToEntity(record).copy(
                syncStatus = if (networkChecker.isNetworkAvailable()) SyncStatus.PENDING_SYNC else SyncStatus.LOCAL_ONLY
            )
            
            dao.insertRecord(entity)
            
            // 尝试同步到远程
            if (networkChecker.isNetworkAvailable()) {
                try {
                    val dto = WorkoutRecordMapper.entityToDto(entity)
                    val response = apiService.createWorkoutRecord(dto)
                    
                    if (response.isSuccess) {
                        dao.updateSyncStatus(record.id, SyncStatus.SYNCED)
                    } else {
                        dao.updateSyncStatus(record.id, SyncStatus.SYNC_FAILED)
                    }
                } catch (e: Exception) {
                    Timber.w(e, "远程创建失败，标记为同步失败")
                    dao.updateSyncStatus(record.id, SyncStatus.SYNC_FAILED)
                }
            }
            
            ModernResult.Success(record)
            
        } catch (e: Exception) {
            Timber.e(e, "创建训练记录失败")
            ModernResult.Error(e.toModernDataError("创建训练记录"))
        }
    }

    override suspend fun updateRecord(record: WorkoutRecord): ModernResult<WorkoutRecord> {
        return try {
            val entity = WorkoutRecordMapper.domainToEntity(record).copy(
                updatedAt = System.currentTimeMillis(),
                syncStatus = if (networkChecker.isNetworkAvailable()) SyncStatus.PENDING_SYNC else SyncStatus.LOCAL_ONLY
            )
            
            dao.updateRecord(entity)
            
            // 尝试同步到远程
            if (networkChecker.isNetworkAvailable()) {
                try {
                    val dto = WorkoutRecordMapper.entityToDto(entity)
                    val response = apiService.updateWorkoutRecord(record.id, dto)
                    
                    if (response.isSuccess) {
                        dao.updateSyncStatus(record.id, SyncStatus.SYNCED)
                    } else {
                        dao.updateSyncStatus(record.id, SyncStatus.SYNC_FAILED)
                    }
                } catch (e: Exception) {
                    Timber.w(e, "远程更新失败，标记为同步失败")
                    dao.updateSyncStatus(record.id, SyncStatus.SYNC_FAILED)
                }
            }
            
            val updatedRecord = record.copy(updatedAt = entity.updatedAt)
            ModernResult.Success(updatedRecord)
            
        } catch (e: Exception) {
            Timber.e(e, "更新训练记录失败")
            ModernResult.Error(e.toModernDataError("更新训练记录"))
        }
    }

    override suspend fun deleteRecord(id: String): ModernResult<Unit> {
        return try {
            dao.deleteRecord(id)
            
            // 尝试从远程删除
            if (networkChecker.isNetworkAvailable()) {
                try {
                    apiService.deleteWorkoutRecord(id)
                } catch (e: Exception) {
                    Timber.w(e, "远程删除失败，但本地删除成功")
                }
            }
            
            ModernResult.Success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "删除训练记录失败")
            ModernResult.Error(e.toModernDataError("删除训练记录"))
        }
    }

    override fun searchRecords(query: String): Flow<ModernResult<List<WorkoutRecord>>> {
        return dao.searchRecords(query)
            .map { entities ->
                val records = entities.map { WorkoutRecordMapper.entityToDomain(it) }
                ModernResult.Success(records)
            }
            .catch { error ->
                emit(ModernResult.Error(error.toModernDataError("搜索训练记录")))
            }
    }

    override suspend fun syncWithRemote(): ModernResult<Unit> {
        return try {
            if (!networkChecker.isNetworkAvailable()) {
                return ModernResult.Error(
                    ModernDataError(
                        operationName = "同步数据",
                        errorType = GlobalErrorType.Network.NoConnection,
                        category = ErrorCategory.NETWORK,
                        uiMessage = UiText.DynamicString("网络连接不可用")
                    )
                )
            }
            
            // 获取待同步的记录
            val pendingRecords = dao.getRecordsByStatus(
                listOf(SyncStatus.PENDING_SYNC, SyncStatus.SYNC_FAILED)
            )
            
            if (pendingRecords.isNotEmpty()) {
                val dtos = pendingRecords.map { WorkoutRecordMapper.entityToDto(it) }
                val response = apiService.syncWorkoutRecords(dtos)
                
                if (response.isSuccess) {
                    // 更新同步状态
                    pendingRecords.forEach { record ->
                        dao.updateSyncStatus(record.id, SyncStatus.SYNCED)
                    }
                } else {
                    // 标记同步失败
                    pendingRecords.forEach { record ->
                        dao.updateSyncStatus(record.id, SyncStatus.SYNC_FAILED)
                    }
                    return ModernResult.Error(
                        ModernDataError(
                            operationName = "同步数据",
                            errorType = GlobalErrorType.Network.ServerError,
                            category = ErrorCategory.NETWORK,
                            uiMessage = UiText.DynamicString("服务器同步失败")
                        )
                    )
                }
            }
            
            ModernResult.Success(Unit)
            
        } catch (e: Exception) {
            Timber.e(e, "同步失败")
            ModernResult.Error(e.toModernDataError("同步数据"))
        }
    }

    override suspend fun refreshFromRemote(): ModernResult<List<WorkoutRecord>> {
        return try {
            refreshFromRemoteInternal()
        } catch (e: Exception) {
            Timber.e(e, "刷新数据失败")
            ModernResult.Error(e.toModernDataError("刷新数据"))
        }
    }

    private suspend fun refreshFromRemoteInternal(): ModernResult<List<WorkoutRecord>> {
        if (!networkChecker.isNetworkAvailable()) {
            return ModernResult.Error(
                ModernDataError(
                    operationName = "刷新数据",
                    errorType = GlobalErrorType.Network.NoConnection,
                    category = ErrorCategory.NETWORK,
                    uiMessage = UiText.DynamicString("网络连接不可用")
                )
            )
        }
        
        val response = apiService.getWorkoutRecords()
        
        if (response.isSuccess && response.data != null) {
            val entities = response.data.map { WorkoutRecordMapper.dtoToEntity(it) }
            dao.insertRecords(entities)
            
            val records = entities.map { WorkoutRecordMapper.entityToDomain(it) }
            return ModernResult.Success(records)
        } else {
            return ModernResult.Error(
                ModernDataError(
                    operationName = "刷新数据",
                    errorType = GlobalErrorType.Network.ServerError,
                    category = ErrorCategory.NETWORK,
                    uiMessage = UiText.DynamicString(response.message ?: "服务器错误")
                )
            )
        }
    }
}

// ========================================
// 7. 辅助工具
// ========================================

/**
 * 网络连接检查器
 */
interface NetworkChecker {
    fun isNetworkAvailable(): Boolean
}

/**
 * 异常转换扩展函数
 */
private fun Throwable.toModernDataError(operationName: String): ModernDataError {
    return ModernDataError(
        operationName = operationName,
        errorType = when (this) {
            is java.net.UnknownHostException -> GlobalErrorType.Network.NoConnection
            is java.net.SocketTimeoutException -> GlobalErrorType.Network.Timeout
            is IllegalArgumentException -> GlobalErrorType.Data.ValidationError
            else -> GlobalErrorType.System.General
        },
        category = when (this) {
            is java.net.UnknownHostException,
            is java.net.SocketTimeoutException -> ErrorCategory.NETWORK
            is IllegalArgumentException -> ErrorCategory.DATA
            else -> ErrorCategory.SYSTEM
        },
        uiMessage = UiText.DynamicString(this.message ?: "操作失败"),
        cause = this
    )
}

// ========================================
// 8. 使用模式说明
// ========================================

/**
 * Repository使用模式示例
 * 
 * 在UseCase中的典型用法：
 * 
 * ```kotlin
 * class GetWorkoutRecordsUseCase @Inject constructor(
 *     private val repository: WorkoutRecordRepository
 * ) {
 *     operator fun invoke(): Flow<ModernResult<List<WorkoutRecord>>> {
 *         return repository.getAllRecords()
 *     }
 * }
 * 
 * // 在ViewModel中使用
 * class WorkoutViewModel @Inject constructor(
 *     private val getWorkoutRecordsUseCase: GetWorkoutRecordsUseCase
 * ) : ViewModel() {
 *     
 *     val workoutRecords = getWorkoutRecordsUseCase()
 *         .stateIn(
 *             scope = viewModelScope,
 *             started = SharingStarted.WhileSubscribed(5000),
 *             initialValue = ModernResult.Loading()
 *         )
 * }
 * ```
 * 
 * 错误处理模式：
 * 
 * ```kotlin
 * viewModel.workoutRecords.collect { result ->
 *     when (result) {
 *         is ModernResult.Loading -> {
 *             // 显示加载状态
 *         }
 *         is ModernResult.Success -> {
 *             // 显示数据
 *             val records = result.data
 *         }
 *         is ModernResult.Error -> {
 *             // 显示错误信息
 *             val errorMessage = result.error.uiMessage
 *         }
 *     }
 * }
 * ```
 */

/**
 * Repository架构总结
 * 
 * 1. **Local-First设计**：
 *    - 本地数据库作为单一真实来源
 *    - 网络层透明化，用户感知不到网络状态
 *    - 离线优先，在线增强
 * 
 * 2. **数据流管理**：
 *    - Flow响应式编程
 *    - 自动数据同步和更新
 *    - 缓存策略和性能优化
 * 
 * 3. **错误处理**：
 *    - ModernResult统一错误包装
 *    - 分类错误处理策略
 *    - 用户友好的错误信息
 * 
 * 4. **同步机制**：
 *    - 智能同步状态管理
 *    - 冲突解决策略
 *    - 批量操作优化
 * 
 * 5. **测试友好**：
 *    - 接口抽象便于Mock
 *    - 依赖注入支持
 *    - 纯函数映射器
 */
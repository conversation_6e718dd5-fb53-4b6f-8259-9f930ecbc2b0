package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.model.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.SessionRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertTrue

/**
 * StartWorkoutSessionUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试UseCase的核心业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证ModernResult返回类型
 * ✔️ 测试错误处理和边界情况
 */
class StartWorkoutSessionUseCaseTest {

    @MockK
    private lateinit var sessionRepository: SessionRepository

    @MockK
    private lateinit var templateRepository: TemplateRepository

    @MockK
    private lateinit var logger: Logger

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: StartWorkoutSessionUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = StartWorkoutSessionUseCase(
            sessionRepository = sessionRepository,
            templateRepository = templateRepository,
            dispatcher = testDispatcher,
            logger = logger,
        )
    }

    @Test
    fun `execute with FromSession should save session and return success`() = runTest(testDispatcher) {
        // Given
        val session = createSampleWorkoutSession()
        val params = StartWorkoutSessionUseCase.Params.FromSession(session)

        coEvery { sessionRepository.saveSession(session) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { sessionRepository.saveSession(session) }
    }

    @Test
    fun `execute with FromSession should update template usage stats when templateId exists`() = runTest(
        testDispatcher,
    ) {
        // Given
        val templateId = "template-123"
        val session = createSampleWorkoutSession(templateId = templateId)
        val template = createSampleTemplate(id = templateId, usageCount = 5)
        val params = StartWorkoutSessionUseCase.Params.FromSession(session)

        coEvery { sessionRepository.saveSession(session) } returns ModernResult.Success(Unit)
        coEvery { templateRepository.getTemplateById(templateId) } returns ModernResult.Success(template)
        coEvery { templateRepository.saveTemplate(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { templateRepository.getTemplateById(templateId) }
        coVerify(exactly = 1) { templateRepository.saveTemplate(any()) }
    }

    @Test
    fun `execute with FromId should start existing session successfully`() = runTest(testDispatcher) {
        // Given
        val sessionId = "session-123"
        val existingSession = createSampleWorkoutSession(id = sessionId, status = "DRAFT")
        val params = StartWorkoutSessionUseCase.Params.FromId(sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(existingSession)
        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with FromId should return error when session not found`() = runTest(testDispatcher) {
        // Given
        val sessionId = "nonexistent-session"
        val params = StartWorkoutSessionUseCase.Params.FromId(sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(null)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with FromId should return error when session is already completed`() = runTest(
        testDispatcher,
    ) {
        // Given
        val sessionId = "completed-session"
        val completedSession = createSampleWorkoutSession(id = sessionId, status = "COMPLETED")
        val params = StartWorkoutSessionUseCase.Params.FromId(sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(completedSession)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with FromId should return error when session is aborted`() = runTest(testDispatcher) {
        // Given
        val sessionId = "aborted-session"
        val abortedSession = createSampleWorkoutSession(id = sessionId, status = "ABORTED")
        val params = StartWorkoutSessionUseCase.Params.FromId(sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(abortedSession)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `invoke with WorkoutSession should delegate to execute with FromSession`() = runTest(testDispatcher) {
        // Given
        val session = createSampleWorkoutSession()

        coEvery { sessionRepository.saveSession(session) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(session)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { sessionRepository.saveSession(session) }
    }

    @Test
    fun `invoke with sessionId should delegate to execute with FromId`() = runTest(testDispatcher) {
        // Given
        val sessionId = "session-123"
        val existingSession = createSampleWorkoutSession(id = sessionId, status = "DRAFT")

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(existingSession)
        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(sessionId)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `should handle template repository error gracefully during usage stats update`() = runTest(
        testDispatcher,
    ) {
        // Given
        val templateId = "template-123"
        val session = createSampleWorkoutSession(templateId = templateId)
        val params = StartWorkoutSessionUseCase.Params.FromSession(session)

        coEvery { sessionRepository.saveSession(session) } returns ModernResult.Success(Unit)
        coEvery { templateRepository.getTemplateById(templateId) } returns ModernResult.Error(mockk())

        // When
        val result = useCase.execute(params)

        // Then
        // 应该成功，因为模板统计更新失败不应影响主流程
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { sessionRepository.saveSession(session) }
        coVerify(exactly = 1) { templateRepository.getTemplateById(templateId) }
    }

    // === 辅助方法 ===

    private fun createSampleWorkoutSession(
        id: String = "session-123",
        templateId: String? = null,
        status: String = "DRAFT",
    ): WorkoutSession {
        return WorkoutSession(
            id = id,
            name = "测试训练会话",
            templateId = templateId,
            status = status,
            startTime = null,
            endTime = null,
            exercises = emptyList(),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
        )
    }

    private fun createSampleTemplate(
        id: String = "template-123",
        usageCount: Int = 0,
    ): WorkoutTemplate {
        return WorkoutTemplate(
            id = id,
            name = "测试模板",
            description = "测试描述",
            exercises = emptyList(),
            difficulty = 1,
            usageCount = usageCount,
            lastUsedDate = null,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            currentVersion = 1,
        )
    }
}
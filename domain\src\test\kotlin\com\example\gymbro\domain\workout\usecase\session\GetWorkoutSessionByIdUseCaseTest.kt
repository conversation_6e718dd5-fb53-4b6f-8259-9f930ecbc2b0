package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * GetWorkoutSessionByIdUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试UseCase的核心业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证Flow返回类型
 * ✔️ 测试异常处理和边界情况
 */
class GetWorkoutSessionByIdUseCaseTest {

    @MockK
    private lateinit var sessionRepository: SessionRepository

    @MockK
    private lateinit var logger: Logger

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: GetWorkoutSessionByIdUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = GetWorkoutSessionByIdUseCase(
            sessionRepository = sessionRepository,
            dispatcher = testDispatcher,
            logger = logger,
        )
    }

    @Test
    fun `createFlow should return session when found`() = runTest(testDispatcher) {
        // Given
        val sessionId = "session-123"
        val expectedSession = createSampleWorkoutSession(id = sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(expectedSession)

        // When
        val result = useCase.createFlow(sessionId).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        assertEquals(expectedSession, (result.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
    }

    @Test
    fun `createFlow should return null when session not found`() = runTest(testDispatcher) {
        // Given
        val sessionId = "nonexistent-session"

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(null)

        // When
        val result = useCase.createFlow(sessionId).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        assertEquals(null, (result.first() as ModernResult.Success).data)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
    }

    @Test
    fun `createFlow should propagate repository error`() = runTest(testDispatcher) {
        // Given
        val sessionId = "error-session"
        val repositoryError = mockk<DataErrors.DataError>()

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Error(repositoryError)

        // When
        val result = useCase.createFlow(sessionId).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Error)
        assertEquals(repositoryError, (result.first() as ModernResult.Error).error)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
    }

    @Test
    fun `createFlow should handle repository exception and log error`() = runTest(testDispatcher) {
        // Given
        val sessionId = "exception-session"
        val exception = RuntimeException("Database connection failed")

        coEvery { sessionRepository.getSessionById(sessionId) } throws exception

        // When
        val result = useCase.createFlow(sessionId).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Error)

        // 验证日志被记录
        verify { logger.e("获取训练会话失败，会话ID: $sessionId", exception) }
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
    }

    @Test
    fun `createFlow should handle different session types correctly`() = runTest(testDispatcher) {
        // Given
        val sessionTypes = listOf(
            createSampleWorkoutSession(id = "draft-session", status = "DRAFT"),
            createSampleWorkoutSession(id = "active-session", status = "IN_PROGRESS"),
            createSampleWorkoutSession(id = "completed-session", status = "COMPLETED"),
            createSampleWorkoutSession(id = "aborted-session", status = "ABORTED"),
        )

        // When & Then
        sessionTypes.forEach { session ->
            coEvery { sessionRepository.getSessionById(session.id) } returns ModernResult.Success(session)

            val result = useCase.createFlow(session.id).toList()

            assertEquals(1, result.size)
            assertTrue(result.first() is ModernResult.Success)
            assertEquals(session, (result.first() as ModernResult.Success).data)
            coVerify(exactly = 1) { sessionRepository.getSessionById(session.id) }
        }
    }

    @Test
    fun `createFlow should handle empty and blank session IDs`() = runTest(testDispatcher) {
        // Given
        val emptyId = ""
        val blankId = "   "

        coEvery { sessionRepository.getSessionById(emptyId) } returns ModernResult.Success(null)
        coEvery { sessionRepository.getSessionById(blankId) } returns ModernResult.Success(null)

        // When
        val emptyResult = useCase.createFlow(emptyId).toList()
        val blankResult = useCase.createFlow(blankId).toList()

        // Then
        assertEquals(1, emptyResult.size)
        assertEquals(1, blankResult.size)
        assertTrue(emptyResult.first() is ModernResult.Success)
        assertTrue(blankResult.first() is ModernResult.Success)
        assertEquals(null, (emptyResult.first() as ModernResult.Success).data)
        assertEquals(null, (blankResult.first() as ModernResult.Success).data)

        coVerify(exactly = 1) { sessionRepository.getSessionById(emptyId) }
        coVerify(exactly = 1) { sessionRepository.getSessionById(blankId) }
    }

    @Test
    fun `createFlow should handle special characters in session ID`() = runTest(testDispatcher) {
        // Given
        val specialIds = listOf(
            "session-with-dashes",
            "session_with_underscores",
            "session.with.dots",
            "session@with#special$chars",
            "very-long-session-id-with-many-characters-1234567890",
        )

        // When & Then
        specialIds.forEach { sessionId ->
            val expectedSession = createSampleWorkoutSession(id = sessionId)
            coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(expectedSession)

            val result = useCase.createFlow(sessionId).toList()

            assertEquals(1, result.size)
            assertTrue(result.first() is ModernResult.Success)
            assertEquals(expectedSession, (result.first() as ModernResult.Success).data)
            coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        }
    }

    @Test
    fun `createFlow should handle concurrent calls correctly`() = runTest(testDispatcher) {
        // Given
        val sessionId1 = "session-1"
        val sessionId2 = "session-2"
        val session1 = createSampleWorkoutSession(id = sessionId1)
        val session2 = createSampleWorkoutSession(id = sessionId2)

        coEvery { sessionRepository.getSessionById(sessionId1) } returns ModernResult.Success(session1)
        coEvery { sessionRepository.getSessionById(sessionId2) } returns ModernResult.Success(session2)

        // When
        val result1 = useCase.createFlow(sessionId1).toList()
        val result2 = useCase.createFlow(sessionId2).toList()

        // Then
        assertEquals(1, result1.size)
        assertEquals(1, result2.size)
        assertTrue(result1.first() is ModernResult.Success)
        assertTrue(result2.first() is ModernResult.Success)
        assertEquals(session1, (result1.first() as ModernResult.Success).data)
        assertEquals(session2, (result2.first() as ModernResult.Success).data)

        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId1) }
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId2) }
    }

    @Test
    fun `createFlow should preserve session data integrity`() = runTest(testDispatcher) {
        // Given
        val sessionId = "complex-session"
        val complexSession = createComplexWorkoutSession(sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(complexSession)

        // When
        val result = useCase.createFlow(sessionId).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        val retrievedSession = (result.first() as ModernResult.Success).data

        // 验证所有字段都正确保留
        assertEquals(complexSession.id, retrievedSession?.id)
        assertEquals(complexSession.name, retrievedSession?.name)
        assertEquals(complexSession.templateId, retrievedSession?.templateId)
        assertEquals(complexSession.status, retrievedSession?.status)
        assertEquals(complexSession.startTime, retrievedSession?.startTime)
        assertEquals(complexSession.endTime, retrievedSession?.endTime)
        assertEquals(complexSession.exercises.size, retrievedSession?.exercises?.size)
        assertEquals(complexSession.createdAt, retrievedSession?.createdAt)
        assertEquals(complexSession.updatedAt, retrievedSession?.updatedAt)

        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
    }

    // === 辅助方法 ===

    private fun createSampleWorkoutSession(
        id: String = "session-123",
        status: String = "DRAFT",
    ): WorkoutSession {
        return WorkoutSession(
            id = id,
            name = "测试训练会话",
            templateId = null,
            status = status,
            startTime = if (status == "IN_PROGRESS") System.currentTimeMillis() else null,
            endTime = if (status == "COMPLETED") System.currentTimeMillis() else null,
            exercises = emptyList(),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
        )
    }

    private fun createComplexWorkoutSession(id: String): WorkoutSession {
        return WorkoutSession(
            id = id,
            name = "复杂训练会话",
            templateId = "template-456",
            status = "IN_PROGRESS",
            startTime = System.currentTimeMillis() - 3600000, // 1小时前开始
            endTime = null,
            exercises = emptyList(), // 在实际应用中这里会有复杂的练习数据
            createdAt = System.currentTimeMillis() - 7200000, // 2小时前创建
            updatedAt = System.currentTimeMillis() - 1800000, // 30分钟前更新
        )
    }
}
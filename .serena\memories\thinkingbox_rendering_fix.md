# ThinkingBox 不呈现问题修复

## 问题根源
ThinkingBox组件不呈现的主要原因是多个判断逻辑过于严格，导致在有内容时仍然不显示。

## 关键修复

### 1. shouldShowThinkingBoxInternal 逻辑简化
**问题**: 原逻辑要求同时满足hasContent和isActive
**修复**: 分层判断，优先显示策略
- 流式传输时总是显示
- 有activePhaseId时总是显示  
- 有任何内容时显示
- 思考完成时显示

### 2. hasActualThinkingContent 判断增强
**问题**: 遗漏activePhaseId判断
**修复**: 添加activePhaseId != null检查

### 3. Coach模块调用修复
**问题**: Coach模块没有调用ThinkingBox的shouldShowThinkingBox函数
**修复**: 使用正确的模块间调用逻辑

### 4. AIThinkingCard显示条件优化
**问题**: 显示条件不够全面
**修复**: 分优先级判断，确保有内容时总是显示

### 5. 调试日志增强
**添加**: 关键状态诊断日志，便于问题定位

## 修复策略
采用"优先显示"策略，只要有任何相关状态或内容，就显示ThinkingBox，避免过度严格的判断导致不显示。

## 预期效果
- 思考框在应该显示时总是显示
- Phase队列管理更稳定
- 问题定位更容易
package com.example.gymbro.data.autosave.service

import com.example.gymbro.core.logging.Logger
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.dao.ChatSessionDao
import com.example.gymbro.data.coach.session.mapper.ChatSessionMapper.toDomainMessage
import com.example.gymbro.domain.coach.model.CoachMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * 🎯 修复版聊天历史自动保存会话 - 纯缓存机制
 *
 * 核心功能：
 * - 改为纯缓存机制，不再写入ROOM数据库
 * - 消除双重写入问题，ROOM作为单一数据源
 * - 保持接口兼容性，避免破坏现有调用
 * - 简化职责，专注于缓存功能
 */
internal class ChatAutoSaveSession(
    val autoSaveSessionId: String,
    private val chatSessionId: String,
    private val scope: CoroutineScope,
    private val chatRawDao: ChatRawDao,
    private val chatSessionDao: ChatSessionDao,
    private val logger: Logger,
) {
    // 🎯 简化：只保留基本状态管理
    private var isActive = false
    private var isPaused = false

    /**
     * 🎯 修复版启动 - 纯缓存初始化
     */
    fun start(initialMessage: CoachMessage?) {
        logger.d("ChatAutoSaveSession", "🚀 [缓存版] 启动缓存会话: $autoSaveSessionId")

        if (isActive) {
            logger.d("ChatAutoSaveSession", "✅ [缓存版] 会话已启动: $autoSaveSessionId")
            return
        }

        isActive = true
        isPaused = false

        logger.d("ChatAutoSaveSession", "✅ [缓存版] 纯缓存会话启动完成，不再写入ROOM: $autoSaveSessionId")
    }

    /**
     * 🎯 修复版消息备份 - 纯缓存机制（不写入ROOM）
     */
    fun updateMessage(message: CoachMessage) {
        logger.d(
            "ChatAutoSaveSession",
            "🔄 [缓存版] updateMessage 开始: messageId=${message.id}, active=$isActive, paused=$isPaused",
        )

        if (!isActive || isPaused) {
            logger.d(
                "ChatAutoSaveSession",
                "⚠️ [缓存版] 会话未激活，跳过缓存: messageId=${message.id}, active=$isActive, paused=$isPaused",
            )
            return
        }

        // 🔥 修复：不再写入ROOM，避免双重写入
        // ROOM写入由ChatSessionRepositoryImpl统一负责
        logger.d("ChatAutoSaveSession", "✅ [缓存版] 消息缓存处理完成（无操作）: messageId=${message.id}")
        logger.d("ChatAutoSaveSession", "💡 [缓存版] ROOM写入由Repository统一负责，避免双重写入")
    }

    /**
     * 🎯 修复版批量备份 - 纯缓存机制（不写入ROOM）
     */
    fun updateBatchMessages(messages: List<CoachMessage>) {
        if (!isActive || isPaused || messages.isEmpty()) {
            logger.d("ChatAutoSaveSession", "⚠️ [缓存版] 跳过批量缓存: ${messages.size}条消息")
            return
        }

        // 🔥 修复：不再写入ROOM，避免双重写入
        // ROOM写入由ChatSessionRepositoryImpl统一负责
        logger.d("ChatAutoSaveSession", "✅ [缓存版] 批量缓存处理完成（无操作）: ${messages.size}条消息")
        logger.d("ChatAutoSaveSession", "💡 [缓存版] ROOM写入由Repository统一负责，避免双重写入")
    }

    /**
     * 🎯 重构版立即保存 - ROOM立即同步
     */
    suspend fun saveNow() {
        if (!isActive) {
            logger.w("ChatAutoSaveSession", "⚠️ [ROOM版] 会话未激活，无法保存")
            return
        }

        logger.d("ChatAutoSaveSession", "✅ [ROOM版] 立即保存完成（ROOM直写模式）")
    }

    /**
     * 暂停备份
     */
    fun pause() {
        isPaused = true
        logger.d("ChatAutoSaveSession", "⏸️ [ROOM版] 备份已暂停: $autoSaveSessionId")
    }

    /**
     * 恢复备份
     */
    fun resume() {
        isPaused = false
        logger.d("ChatAutoSaveSession", "▶️ [ROOM版] 备份已恢复: $autoSaveSessionId")
    }

    /**
     * 停止备份会话
     */
    fun stop() {
        isActive = false
        isPaused = false
        logger.d("ChatAutoSaveSession", "🛑 [ROOM版] 备份会话已停止: $autoSaveSessionId")
    }

    /**
     * 从ROOM恢复消息数据
     */
    suspend fun restoreFromCache(): List<CoachMessage>? =
        try {
            // 🔥 从ROOM数据库恢复最近的消息
            val chatRaws = chatRawDao.getRecentChatMessagesBySession(chatSessionId, 50)
            val messages = chatRaws.map { it.toDomainMessage() }

            if (messages.isNotEmpty()) {
                logger.d("ChatAutoSaveSession", "📥 [ROOM版] 数据恢复成功: ${messages.size}条消息")
                messages
            } else {
                logger.d("ChatAutoSaveSession", "📥 [ROOM版] 无数据可恢复")
                null
            }
        } catch (e: Exception) {
            logger.w("ChatAutoSaveSession", "❌ [ROOM版] 数据恢复异常: ${e.message}")
            null
        }

    /**
     * 清理会话数据（保留接口兼容性）
     */
    suspend fun discardCache() {
        // 🔥 ROOM版本：不需要清理缓存，数据直接在数据库中
        // 保留此方法以维持接口兼容性
        logger.d("ChatAutoSaveSession", "🗑️ [ROOM版] 无需清理缓存（数据在ROOM中）")
    }

    /**
     * 🎯 安全的协程启动扩展
     */
    private fun CoroutineScope.launchCatching(block: suspend CoroutineScope.() -> Unit) {
        this.launch {
            try {
                block()
            } catch (e: Exception) {
                logger.w("ChatAutoSaveSession", "⚠️ [ROOM版] 异步操作异常: ${e.message}")
            }
        }
    }
}

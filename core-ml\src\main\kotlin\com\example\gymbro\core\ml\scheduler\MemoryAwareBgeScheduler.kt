package com.example.gymbro.core.ml.scheduler

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 内存感知BGE调度器 - 4G-8GB设备优化
 *
 * 根据设备内存容量和当前内存状态动态调整BGE加载策略：
 * - 4GB设备: 保守加载，单线程，CPU优先，分阶段加载
 * - 6GB设备: 标准加载，双线程，NNAPI启用，渐进式预热
 * - 8GB+设备: 激进加载，多线程，GPU优先，全量预热
 */
@Singleton
class MemoryAwareBgeScheduler @Inject constructor(
    @ApplicationContext private val context: Context,
    private val ioDispatcher: CoroutineDispatcher,
) {

    companion object {
        private const val TAG = "BgeScheduler"

        // 内存阈值配置 (MB)
        private const val LOW_MEMORY_THRESHOLD = 4 * 1024 // 4GB
        private const val MID_MEMORY_THRESHOLD = 6 * 1024 // 6GB
        private const val HIGH_MEMORY_THRESHOLD = 8 * 1024 // 8GB

        // 加载策略延迟配置 (ms)
        private const val LOW_MEMORY_DELAY = 1000L // 4GB设备延迟1秒
        private const val MID_MEMORY_DELAY = 500L // 6GB设备延迟500ms
        private const val HIGH_MEMORY_DELAY = 100L // 8GB+设备延迟100ms

        // 分块加载配置
        private const val LOW_MEMORY_CHUNK_SIZE = 2 * 1024 * 1024 // 2MB块
        private const val MID_MEMORY_CHUNK_SIZE = 5 * 1024 * 1024 // 5MB块
        private const val HIGH_MEMORY_CHUNK_SIZE = 10 * 1024 * 1024 // 10MB块
    }

    /**
     * 设备内存等级
     */
    enum class MemoryTier {
        LOW_MEMORY, // 4GB及以下
        MID_MEMORY, // 4GB-6GB
        HIGH_MEMORY, // 8GB及以上
    }

    /**
     * BGE加载策略配置
     */
    data class LoadingStrategy(
        val memoryTier: MemoryTier,
        val threadCount: Int,
        val enableGpuAcceleration: Boolean,
        val enableNnapi: Boolean,
        val chunkSize: Int,
        val loadingDelay: Long,
        val enablePrewarmup: Boolean,
        val maxConcurrentOperations: Int,
    )

    private var deviceMemoryTier: MemoryTier? = null
    private var currentStrategy: LoadingStrategy? = null

    /**
     * 检测设备内存等级并生成对应的加载策略
     */
    fun detectAndCreateStrategy(): LoadingStrategy {
        if (currentStrategy != null) {
            return currentStrategy!!
        }

        val totalMemoryMB = getTotalDeviceMemoryMB()
        val availableMemoryMB = getAvailableMemoryMB()

        deviceMemoryTier = when {
            totalMemoryMB <= LOW_MEMORY_THRESHOLD -> MemoryTier.LOW_MEMORY
            totalMemoryMB <= MID_MEMORY_THRESHOLD -> MemoryTier.MID_MEMORY
            else -> MemoryTier.HIGH_MEMORY
        }

        currentStrategy = createStrategyForTier(deviceMemoryTier!!, availableMemoryMB)

        Timber.tag(TAG).i("🎯 BGE加载策略检测完成:")
        Timber.tag(TAG).i("  📱 设备总内存: ${totalMemoryMB}MB")
        Timber.tag(TAG).i("  💾 可用内存: ${availableMemoryMB}MB")
        Timber.tag(TAG).i("  🏷️ 内存等级: $deviceMemoryTier")
        Timber.tag(TAG).i("  ⚙️ 加载策略: ${formatStrategy(currentStrategy!!)}")

        return currentStrategy!!
    }

    /**
     * 根据内存等级创建对应的加载策略
     */
    private fun createStrategyForTier(tier: MemoryTier, availableMemoryMB: Int): LoadingStrategy {
        return when (tier) {
            MemoryTier.LOW_MEMORY -> LoadingStrategy(
                memoryTier = tier,
                threadCount = 1,
                enableGpuAcceleration = false,
                enableNnapi = false,
                chunkSize = LOW_MEMORY_CHUNK_SIZE,
                loadingDelay = LOW_MEMORY_DELAY,
                enablePrewarmup = false,
                maxConcurrentOperations = 1,
            )

            MemoryTier.MID_MEMORY -> LoadingStrategy(
                memoryTier = tier,
                threadCount = 2,
                enableGpuAcceleration = false,
                enableNnapi = true,
                chunkSize = MID_MEMORY_CHUNK_SIZE,
                loadingDelay = MID_MEMORY_DELAY,
                enablePrewarmup = availableMemoryMB > 2048, // 超过2GB可用内存才预热
                maxConcurrentOperations = 2,
            )

            MemoryTier.HIGH_MEMORY -> LoadingStrategy(
                memoryTier = tier,
                threadCount = 4,
                enableGpuAcceleration = true,
                enableNnapi = true,
                chunkSize = HIGH_MEMORY_CHUNK_SIZE,
                loadingDelay = HIGH_MEMORY_DELAY,
                enablePrewarmup = true,
                maxConcurrentOperations = 3,
            )
        }
    }

    /**
     * 执行内存感知的延迟调度
     * 在用户输入窗口期间智能调度BGE初始化
     */
    suspend fun scheduleInitialization(
        strategy: LoadingStrategy,
        initializer: suspend () -> Unit,
    ) = withContext(ioDispatcher) {
        Timber.tag(TAG).i("🕒 开始内存感知调度，延迟: ${strategy.loadingDelay}ms")

        // 🎯 关键优化：利用用户思考+输入的时间窗口
        delay(strategy.loadingDelay)

        // 执行前再次检查内存状态
        val currentAvailableMemory = getAvailableMemoryMB()
        if (currentAvailableMemory < getMinRequiredMemoryMB(strategy)) {
            Timber.tag(TAG).w("⚠️ 当前可用内存不足 (${currentAvailableMemory}MB)，推迟BGE初始化")
            // 再等待一段时间，让系统释放内存
            delay(2000L)
        }

        Timber.tag(TAG).i("🚀 内存检查通过，开始BGE初始化")
        try {
            initializer()
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ BGE初始化失败")
            throw e
        }
    }

    /**
     * 检查当前内存状态是否适合BGE操作
     */
    fun isMemoryAvailableForBge(): Boolean {
        val strategy = currentStrategy ?: detectAndCreateStrategy()
        val availableMemory = getAvailableMemoryMB()
        val requiredMemory = getMinRequiredMemoryMB(strategy)

        return availableMemory >= requiredMemory
    }

    /**
     * 获取设备总内存 (MB)
     */
    private fun getTotalDeviceMemoryMB(): Int {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager

        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val memInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memInfo)
            (memInfo.totalMem / (1024 * 1024)).toInt()
        } else {
            // Android 7及以下的fallback估算
            val memClass = activityManager.memoryClass
            memClass * 16 // 粗略估算：memoryClass通常是实际内存的1/16
        }
    }

    /**
     * 获取当前可用内存 (MB)
     */
    private fun getAvailableMemoryMB(): Int {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memInfo)

        return (memInfo.availMem / (1024 * 1024)).toInt()
    }

    /**
     * 获取指定策略的最小内存需求 (MB)
     */
    private fun getMinRequiredMemoryMB(strategy: LoadingStrategy): Int {
        val baseRequirement = 100 // 基础100MB
        val modelSize = 15 // BGE模型大小约15MB
        val bufferSize = when (strategy.memoryTier) {
            MemoryTier.LOW_MEMORY -> 50 // 50MB缓冲
            MemoryTier.MID_MEMORY -> 100 // 100MB缓冲
            MemoryTier.HIGH_MEMORY -> 200 // 200MB缓冲
        }

        return baseRequirement + modelSize + bufferSize
    }

    /**
     * 格式化策略信息用于日志输出
     */
    private fun formatStrategy(strategy: LoadingStrategy): String {
        return buildString {
            append("线程数=${strategy.threadCount}")
            append(", GPU=${if (strategy.enableGpuAcceleration) "开启" else "关闭"}")
            append(", NNAPI=${if (strategy.enableNnapi) "开启" else "关闭"}")
            append(", 块大小=${strategy.chunkSize / (1024 * 1024)}MB")
            append(", 延迟=${strategy.loadingDelay}ms")
            append(", 预热=${if (strategy.enablePrewarmup) "开启" else "关闭"}")
        }
    }

    /**
     * 获取当前策略（如果已检测）
     */
    fun getCurrentStrategy(): LoadingStrategy? = currentStrategy

    /**
     * 获取设备内存等级
     */
    fun getDeviceMemoryTier(): MemoryTier? = deviceMemoryTier
}
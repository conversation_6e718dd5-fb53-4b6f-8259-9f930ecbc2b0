package com.example.gymbro.di.core.userdata

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.userdata.api.model.SyncStatus
import com.example.gymbro.core.userdata.api.model.UnifiedUserData
import com.example.gymbro.core.userdata.internal.dao.UserDataDao
import com.example.gymbro.core.userdata.internal.dao.UserProfileDao
import com.example.gymbro.core.userdata.internal.datasource.UserLocalDataSource
import com.example.gymbro.core.userdata.internal.mapper.UserDataMapper
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.UserProfile
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 基于 Room 数据库的 UserLocalDataSource 实现
 *
 * 这是真正的 Room 持久化实现，通过适配器模式使用 data 模块的 DAO，
 * 避免了循环依赖问题。
 */
@Singleton
class UserLocalDataSourceRoomImpl @Inject constructor(
    private val userDataDao: UserDataDao,
    private val userProfileDao: UserProfileDao,
    private val userDataMapper: UserDataMapper,
    private val logger: Logger,
) : UserLocalDataSource {

    companion object {
        private const val TAG = "UserLocalDataSourceRoomImpl"
    }

    // 并发控制
    private val operationMutex = Mutex()

    override fun observeUserData(): Flow<UnifiedUserData?> {
        logger.d(TAG, "开始观察统一用户数据（Room 版本）")

        return userDataDao.observeCurrentUserAuth()
            .combine(userProfileDao.observeAllUserProfiles()) { userAuth, userProfiles ->
                try {
                    if (userAuth == null) {
                        logger.d(TAG, "无当前用户认证数据")
                        return@combine null
                    }

                    logger.d(TAG, "合并用户数据: userId=${userAuth.userId}")

                    // 查找对应的用户资料
                    val userProfile = userProfiles.find { it.userId == userAuth.userId }

                    // 转换为 Domain 模型
                    val authUser = userAuth.toAuthUser()
                    val profileDomain = userProfile?.toUserProfile()

                    // 合并为统一数据模型
                    userDataMapper.combineAuthAndProfile(
                        authUser = authUser,
                        userProfile = profileDomain,
                        syncStatus = SyncStatus.SYNCED,
                    )
                } catch (e: Exception) {
                    logger.e(e, TAG, "合并用户数据时发生异常")
                    null
                }
            }
    }

    override fun observeUserData(userId: String): Flow<UnifiedUserData?> {
        logger.d(TAG, "开始观察指定用户数据: userId=$userId（Room 版本）")

        return userDataDao.observeUserAuth(userId)
            .combine(userProfileDao.observeUserProfile(userId)) { userAuth, userProfile ->
                try {
                    if (userAuth == null) {
                        logger.d(TAG, "指定用户不存在: userId=$userId")
                        return@combine null
                    }

                    logger.d(TAG, "合并指定用户数据: userId=$userId")

                    // 转换为 Domain 模型
                    val authUser = userAuth.toAuthUser()
                    val profileDomain = userProfile?.toUserProfile()

                    // 合并为统一数据模型
                    userDataMapper.combineAuthAndProfile(
                        authUser = authUser,
                        userProfile = profileDomain,
                        syncStatus = SyncStatus.SYNCED,
                    )
                } catch (e: Exception) {
                    logger.e(e, TAG, "合并指定用户数据时发生异常")
                    null
                }
            }
    }

    override suspend fun getCurrentUserData(): ModernResult<UnifiedUserData?> {
        return try {
            logger.d(TAG, "获取当前用户数据（Room 版本）")

            val userAuth = userDataDao.getCurrentUserAuth()
            if (userAuth == null) {
                logger.d(TAG, "无当前用户数据")
                return ModernResult.Success(null)
            }

            val userProfile = userProfileDao.getUserProfile(userAuth.userId)

            // 转换为 Domain 模型
            val authUser = userAuth.toAuthUser()
            val profileDomain = userProfile?.toUserProfile()

            // 合并为统一数据模型
            val unifiedData = userDataMapper.combineAuthAndProfile(
                authUser = authUser,
                userProfile = profileDomain,
                syncStatus = SyncStatus.SYNCED,
            )

            logger.d(TAG, "获取当前用户数据成功: userId=${unifiedData.userId}")
            ModernResult.Success(unifiedData)
        } catch (e: Exception) {
            logger.e(e, TAG, "获取当前用户数据时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "getCurrentUserData",
                    message = UiText.DynamicString("获取用户数据失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    override suspend fun getUserData(userId: String): ModernResult<UnifiedUserData?> {
        return try {
            logger.d(TAG, "获取指定用户数据: userId=$userId（Room 版本）")

            val userAuth = userDataDao.getUserAuth(userId)
            if (userAuth == null) {
                logger.d(TAG, "指定用户不存在: userId=$userId")
                return ModernResult.Success(null)
            }

            val userProfile = userProfileDao.getUserProfile(userId)

            // 转换为 Domain 模型
            val authUser = userAuth.toAuthUser()
            val profileDomain = userProfile?.toUserProfile()

            // 合并为统一数据模型
            val unifiedData = userDataMapper.combineAuthAndProfile(
                authUser = authUser,
                userProfile = profileDomain,
                syncStatus = SyncStatus.SYNCED,
            )

            logger.d(TAG, "获取指定用户数据成功: userId=$userId")
            ModernResult.Success(unifiedData)
        } catch (e: Exception) {
            logger.e(e, TAG, "获取指定用户数据时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "getUserData",
                    message = UiText.DynamicString("获取用户数据失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    override suspend fun saveAuthData(authUser: AuthUser): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "保存认证数据: userId=${authUser.uid}（Room 版本）")

                val userAuthData = authUser.toUserAuthData()
                userDataDao.saveUserAuth(userAuthData)

                logger.d(TAG, "认证数据保存成功")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "保存认证数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "saveAuthData",
                        message = UiText.DynamicString("保存认证数据失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun saveProfileData(userProfile: UserProfile): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "保存用户资料数据: userId=${userProfile.userId}（Room 版本）")

                val userProfileData = userProfile.toUserProfileData()
                userProfileDao.saveUserProfile(userProfileData)

                logger.d(TAG, "用户资料数据保存成功")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "保存用户资料数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "saveProfileData",
                        message = UiText.DynamicString("保存用户资料失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun updateAuthData(authUser: AuthUser): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "更新认证数据: userId=${authUser.uid}（Room 版本）")

                val userAuthData = authUser.toUserAuthData()
                userDataDao.updateUserAuth(userAuthData)

                logger.d(TAG, "认证数据更新成功")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "更新认证数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "updateAuthData",
                        message = UiText.DynamicString("更新认证数据失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun updateProfileData(userProfile: UserProfile): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "更新用户资料数据: userId=${userProfile.userId}（Room 版本）")

                val userProfileData = userProfile.toUserProfileData()
                userProfileDao.updateUserProfile(userProfileData)

                logger.d(TAG, "用户资料数据更新成功")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "更新用户资料数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "updateProfileData",
                        message = UiText.DynamicString("更新用户资料失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun userExists(userId: String): ModernResult<Boolean> {
        return try {
            logger.d(TAG, "检查用户是否存在: userId=$userId（Room 版本）")

            val exists = userDataDao.userExists(userId)
            logger.d(TAG, "用户存在性检查结果: userId=$userId, exists=$exists")
            ModernResult.Success(exists)
        } catch (e: Exception) {
            logger.e(e, TAG, "检查用户存在性时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "userExists",
                    message = UiText.DynamicString("检查用户存在性失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    override suspend fun clearAllUserData(): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "清除所有用户数据（Room 版本）")

                userDataDao.deleteAllUserAuth()
                userProfileDao.deleteAllUserProfiles()

                logger.d(TAG, "所有用户数据清除成功")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "清除所有用户数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "clearAllUserData",
                        message = UiText.DynamicString("清除用户数据失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun clearUserData(userId: String): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "清除指定用户数据: userId=$userId（Room 版本）")

                userDataDao.deleteUserAuth(userId)
                userProfileDao.deleteUserProfile(userId)

                logger.d(TAG, "指定用户数据清除成功: userId=$userId")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "清除指定用户数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "clearUserData",
                        message = UiText.DynamicString("清除用户数据失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun getAllUserIds(): ModernResult<List<String>> {
        return try {
            logger.d(TAG, "获取所有用户ID列表（Room 版本）")

            val allProfiles = userProfileDao.observeAllUserProfiles().first()
            val userIds = allProfiles.map { it.userId }

            logger.d(TAG, "获取用户ID列表成功: count=${userIds.size}")
            ModernResult.Success(userIds)
        } catch (e: Exception) {
            logger.e(e, TAG, "获取用户ID列表时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "getAllUserIds",
                    message = UiText.DynamicString("获取用户列表失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    override suspend fun healthCheck(): ModernResult<Boolean> {
        return try {
            logger.d(TAG, "执行数据库健康检查（Room 版本）")

            val hasUser = userDataDao.hasAnyUser()
            val profileCount = userProfileDao.getUserProfileCount()

            logger.d(TAG, "数据库健康检查成功: hasUser=$hasUser, profileCount=$profileCount")
            ModernResult.Success(true)
        } catch (e: Exception) {
            logger.e(e, TAG, "数据库健康检查失败")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "healthCheck",
                    message = UiText.DynamicString("数据库健康检查失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }
}

package com.example.gymbro.domain.workout.usecase.template

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.TemplateRepository
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * TemplateManagementUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试聚合UseCase的复杂业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证ModernResult返回类型
 * ✔️ 测试所有内部UseCase实例
 */
class TemplateManagementUseCaseTest {

    @MockK
    private lateinit var templateRepository: TemplateRepository

    @MockK
    private lateinit var getCurrentUserIdUseCase: GetCurrentUserIdUseCase

    @MockK
    private lateinit var logger: Logger

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: TemplateManagementUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = TemplateManagementUseCase(
            repository = templateRepository,
            getCurrentUserIdUseCase = getCurrentUserIdUseCase,
            dispatcher = testDispatcher,
            logger = logger,
        )
    }

    // === GetTemplates Tests ===

    @Test
    fun `getTemplates should return user templates successfully`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"
        val templates = listOf(createSampleTemplate(), createSampleTemplate(id = "template-2"))

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))
        coEvery { templateRepository.getTemplatesByUser(userId) } returns flowOf(ModernResult.Success(templates))

        // When
        val result = useCase.getTemplates.createFlow(Unit).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        val dtos = (result.first() as ModernResult.Success).data
        assertEquals(2, dtos.size)
        coVerify(exactly = 1) { templateRepository.getTemplatesByUser(userId) }
    }

    @Test
    fun `getTemplates should use default userId when getCurrentUserId fails`() = runTest(testDispatcher) {
        // Given
        val defaultUserId = "current_user"
        val templates = listOf(createSampleTemplate())

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Error(mockk()))
        coEvery {
            templateRepository.getTemplatesByUser(defaultUserId)
        } returns flowOf(ModernResult.Success(templates))

        // When
        val result = useCase.getTemplates.createFlow(Unit).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Success)
        coVerify(exactly = 1) { templateRepository.getTemplatesByUser(defaultUserId) }
    }

    @Test
    fun `getTemplates should handle repository error`() = runTest(testDispatcher) {
        // Given
        val userId = "user-123"

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))
        coEvery { templateRepository.getTemplatesByUser(userId) } returns flowOf(ModernResult.Error(mockk()))

        // When
        val result = useCase.getTemplates.createFlow(Unit).toList()

        // Then
        assertEquals(1, result.size)
        assertTrue(result.first() is ModernResult.Error)
        coVerify(exactly = 1) { templateRepository.getTemplatesByUser(userId) }
    }

    // === GetTemplate Tests ===

    @Test
    fun `getTemplate should return template when found`() = runTest(testDispatcher) {
        // Given
        val templateId = "template-123"
        val template = createSampleTemplate(id = templateId)

        coEvery { templateRepository.getTemplateById(templateId) } returns ModernResult.Success(template)

        // When
        val result = useCase.getTemplate.execute(templateId)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(templateId, result.data?.id)
        coVerify(exactly = 1) { templateRepository.getTemplateById(templateId) }
    }

    @Test
    fun `getTemplate should return null when template not found`() = runTest(testDispatcher) {
        // Given
        val templateId = "nonexistent-template"

        coEvery { templateRepository.getTemplateById(templateId) } returns ModernResult.Success(null)

        // When
        val result = useCase.getTemplate.execute(templateId)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(null, result.data)
        coVerify(exactly = 1) { templateRepository.getTemplateById(templateId) }
    }

    @Test
    fun `getTemplate should return error for blank templateId`() = runTest(testDispatcher) {
        // When
        val result = useCase.getTemplate.execute("")

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 0) { templateRepository.getTemplateById(any()) }
    }

    // === SaveTemplate Tests ===

    @Test
    fun `saveTemplate should save template with current userId`() = runTest(testDispatcher) {
        // Given
        val template = createSampleTemplate(userId = "")
        val currentUserId = "user-123"
        val savedTemplateId = "saved-template-123"

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(currentUserId))
        coEvery { templateRepository.saveTemplate(any()) } returns ModernResult.Success(savedTemplateId)

        // When
        val result = useCase.saveTemplate.execute(template)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(savedTemplateId, result.data)
        coVerify(exactly = 1) { templateRepository.saveTemplate(any()) }
    }

    @Test
    fun `saveTemplate should use anonymous userId when getCurrentUserId fails`() = runTest(testDispatcher) {
        // Given
        val template = createSampleTemplate(userId = "")
        val savedTemplateId = "saved-template-123"

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Error(mockk()))
        coEvery { templateRepository.saveTemplate(any()) } returns ModernResult.Success(savedTemplateId)

        // When
        val result = useCase.saveTemplate.execute(template)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(savedTemplateId, result.data)
        coVerify(exactly = 1) { templateRepository.saveTemplate(any()) }
    }

    @Test
    fun `saveTemplate should use default name when template name is blank`() = runTest(testDispatcher) {
        // Given
        val template = createSampleTemplate(name = "", userId = "user-123")
        val savedTemplateId = "saved-template-123"

        coEvery { templateRepository.saveTemplate(any()) } returns ModernResult.Success(savedTemplateId)

        // When
        val result = useCase.saveTemplate.execute(template)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(savedTemplateId, result.data)
        coVerify(exactly = 1) { templateRepository.saveTemplate(any()) }
    }

    // === DeleteTemplate Tests ===

    @Test
    fun `deleteTemplate should delete existing template successfully`() = runTest(testDispatcher) {
        // Given
        val templateId = "template-123"
        val template = createSampleTemplate(id = templateId)

        coEvery { templateRepository.getTemplateById(templateId) } returns ModernResult.Success(template)
        coEvery { templateRepository.deleteTemplate(templateId) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.deleteTemplate.execute(templateId)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { templateRepository.getTemplateById(templateId) }
        coVerify(exactly = 1) { templateRepository.deleteTemplate(templateId) }
    }

    @Test
    fun `deleteTemplate should return error for blank templateId`() = runTest(testDispatcher) {
        // When
        val result = useCase.deleteTemplate.execute("")

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 0) { templateRepository.getTemplateById(any()) }
        coVerify(exactly = 0) { templateRepository.deleteTemplate(any()) }
    }

    @Test
    fun `deleteTemplate should return error when template not found`() = runTest(testDispatcher) {
        // Given
        val templateId = "nonexistent-template"

        coEvery { templateRepository.getTemplateById(templateId) } returns ModernResult.Error(mockk())

        // When
        val result = useCase.deleteTemplate.execute(templateId)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { templateRepository.getTemplateById(templateId) }
        coVerify(exactly = 0) { templateRepository.deleteTemplate(any()) }
    }

    // === DuplicateTemplate Tests ===

    @Test
    fun `duplicateTemplate should create copy of existing template`() = runTest(testDispatcher) {
        // Given
        val templateId = "template-123"
        val originalTemplate = createSampleTemplate(id = templateId, name = "原始模板")
        val savedTemplateId = "duplicated-template-123"

        coEvery { templateRepository.getTemplateById(templateId) } returns ModernResult.Success(originalTemplate)
        coEvery { templateRepository.saveTemplate(any()) } returns ModernResult.Success(savedTemplateId)

        // When
        val result = useCase.duplicateTemplate.execute(templateId)

        // Then
        assertTrue(result is ModernResult.Success)
        assertTrue(result.data.name.contains("副本"))
        assertFalse(result.data.isPublic)
        assertEquals(0, result.data.usageCount)
        coVerify(exactly = 1) { templateRepository.getTemplateById(templateId) }
        coVerify(exactly = 1) { templateRepository.saveTemplate(any()) }
    }

    @Test
    fun `duplicateTemplate should return error for blank templateId`() = runTest(testDispatcher) {
        // When
        val result = useCase.duplicateTemplate.execute("")

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 0) { templateRepository.getTemplateById(any()) }
        coVerify(exactly = 0) { templateRepository.saveTemplate(any()) }
    }

    // === ToggleFavorite Tests ===

    @Test
    fun `toggleFavorite should toggle favorite status successfully`() = runTest(testDispatcher) {
        // Given
        val templateId = "template-123"
        val template = createSampleTemplate(id = templateId, isFavorite = false)

        coEvery { templateRepository.getTemplateById(templateId) } returns ModernResult.Success(template)
        coEvery { templateRepository.saveTemplate(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.toggleFavorite.execute(templateId)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { templateRepository.getTemplateById(templateId) }
        coVerify(exactly = 1) { templateRepository.saveTemplate(any()) }
    }

    @Test
    fun `toggleFavorite should return error for blank templateId`() = runTest(testDispatcher) {
        // When
        val result = useCase.toggleFavorite.execute("")

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 0) { templateRepository.getTemplateById(any()) }
        coVerify(exactly = 0) { templateRepository.saveTemplate(any()) }
    }

    // === SaveAndVersionTemplate Tests ===

    @Test
    fun `saveAndVersionTemplate should publish template with correct status`() = runTest(testDispatcher) {
        // Given
        val template = createSampleTemplate(isDraft = true, isPublished = false)
        val params = TemplateManagementUseCase.SaveAndVersionParams(
            template = template,
            isPublishing = true,
        )

        coEvery { templateRepository.saveTemplate(any()) } returns ModernResult.Success("template-123")

        // When
        val result = useCase.saveAndVersionTemplate.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        assertFalse(result.data.isDraft)
        assertTrue(result.data.isPublished)
        coVerify(exactly = 1) { templateRepository.saveTemplate(any()) }
    }

    @Test
    fun `saveAndVersionTemplate should save draft without publishing`() = runTest(testDispatcher) {
        // Given
        val template = createSampleTemplate(isDraft = true, isPublished = false)
        val params = TemplateManagementUseCase.SaveAndVersionParams(
            template = template,
            isPublishing = false,
        )

        coEvery { templateRepository.saveTemplate(any()) } returns ModernResult.Success("template-123")

        // When
        val result = useCase.saveAndVersionTemplate.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(template.isDraft, result.data.isDraft)
        assertEquals(template.isPublished, result.data.isPublished)
        coVerify(exactly = 1) { templateRepository.saveTemplate(any()) }
    }

    // === 辅助方法 ===

    private fun createSampleTemplate(
        id: String = "template-123",
        name: String = "测试模板",
        userId: String = "user-123",
        isDraft: Boolean = false,
        isPublished: Boolean = false,
        isFavorite: Boolean = false,
    ): WorkoutTemplate {
        return WorkoutTemplate(
            id = id,
            name = name,
            description = "测试描述",
            userId = userId,
            exercises = emptyList(),
            difficulty = 1,
            isDraft = isDraft,
            isPublished = isPublished,
            isPublic = false,
            isFavorite = isFavorite,
            usageCount = 0,
            lastUsedDate = null,
            lastPublishedAt = null,
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
            currentVersion = 1,
        )
    }
}
package com.example.gymbro.domain.coach.model

import kotlinx.serialization.Serializable

/**
 * InputDraft - 输入草稿数据模型
 *
 * 表示用户在输入框中的草稿内容，支持会话级别的自动保存和恢复
 *
 * @param sessionId 会话ID，用于关联特定的聊天会话
 * @param content 草稿内容
 * @param timestamp 创建或最后修改时间戳
 */
@Serializable
data class InputDraft(
    val sessionId: String,
    val content: String,
    val timestamp: Long = System.currentTimeMillis(),
) {
    /**
     * 检查草稿是否为空
     */
    fun isEmpty(): Boolean = content.isBlank()

    /**
     * 检查草稿是否有效（非空且会话ID有效）
     */
    fun isValid(): Boolean = sessionId.isNotBlank() && content.isNotBlank()

    companion object {
        /**
         * 创建新的草稿
         */
        fun create(sessionId: String, content: String): InputDraft {
            return InputDraft(
                sessionId = sessionId,
                content = content,
                timestamp = System.currentTimeMillis(),
            )
        }
    }
}

package com.example.gymbro.core.error.types.common

import com.example.gymbro.core.error.types.ErrorCategory
import com.example.gymbro.core.error.types.ErrorSeverity
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.StandardKeys
import com.example.gymbro.core.ui.text.UiText

/**
 * 通用功能错误类型（从Features层下沉到Core层）
 * 解决Data层依赖Features层的架构违规问题
 *
 * 此文件包含了原本在FeatureErrors中但被Data层使用的通用错误定义
 */
object CommonFeatureErrors {

    /**
     * AI相关错误类
     */
    object CoachError {
        /**
         * 创建AI教练限制超出错误
         */
        fun limitExceeded(
            operationName: String = "CoachError.limitExceeded",
            message: UiText = UiText.DynamicString("AI教练使用限制已达到"),
            limitType: String = "session",
            currentCount: Int? = null,
            maxAllowed: Int? = null,
            timeWindow: String? = null,
            quotaType: String? = null,
            resetTime: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithQuota =
                metadataMap.toMutableMap().apply {
                    put(StandardKeys.ERROR_SUBTYPE.key, limitType)
                    currentCount?.let { put(StandardKeys.QUOTA_USED.key, it) }
                    maxAllowed?.let { put(StandardKeys.QUOTA_LIMIT.key, it) }
                    timeWindow?.let { put("time_window", it) }
                    quotaType?.let { put("quota_type", it) }
                    resetTime?.let { put("reset_time", it) }
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.AI.QuotaExceeded,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                cause = cause,
                metadataMap = metadataWithQuota,
            )
        }

        /**
         * 创建AI教练输入无效错误
         */
        fun invalidInput(
            operationName: String = "CoachError.invalidInput",
            message: UiText = UiText.DynamicString("输入数据无效"),
            inputType: String = "unknown",
            value: String? = null,
            supportedValues: List<String>? = null,
            missingFields: List<String>? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithInput =
                metadataMap.toMutableMap().apply {
                    put(StandardKeys.ERROR_SUBTYPE.key, inputType)
                    value?.let { put("input_value", it) }
                    supportedValues?.let { put("supported_values", it.joinToString(",")) }
                    missingFields?.let { put("missing_fields", it.joinToString(",")) }
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Validation.Format,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.WARNING,
                recoverable = true,
                cause = cause,
                metadataMap = metadataWithInput,
            )
        }

        /**
         * 创建AI教练处理错误
         */
        fun processingFailed(
            operationName: String = "CoachError.processingFailed",
            message: UiText = UiText.DynamicString("AI教练处理失败"),
            processType: String = "unknown",
            reason: String = "unknown",
            analysisType: String? = null,
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithProcess =
                metadataMap.toMutableMap().apply {
                    put(StandardKeys.ERROR_SUBTYPE.key, processType)
                    put("failure_reason", reason)
                    analysisType?.let { put("analysis_type", it) }
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.AI.ServiceUnavailable,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithProcess,
            )
        }

        /**
         * 创建AI教练配置错误
         */
        fun configurationError(
            operationName: String = "CoachError.configurationError",
            message: UiText = UiText.DynamicString("AI教练配置错误"),
            configType: String = "unknown",
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithConfig =
                metadataMap.toMutableMap().apply {
                    put(StandardKeys.ERROR_SUBTYPE.key, configType)
                }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.AI.ConfigurationInvalid,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithConfig,
            )
        }
    }

    /**
     * JSON处理错误类 (新增，用于支持JSON处理接口)
     */
    object JsonError {
        /**
         * JSON序列化错误
         */
        fun serializationError(
            operationName: String = "JsonError.serializationError",
            message: UiText = UiText.DynamicString("JSON序列化失败"),
            dataType: String = "unknown",
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithSerialization = metadataMap.toMutableMap().apply {
                put("data_type", dataType)
                put(StandardKeys.ERROR_SUBTYPE.key, "serialization")
            }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.Conversion,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithSerialization,
            )
        }

        /**
         * JSON反序列化错误
         */
        fun deserializationError(
            operationName: String = "JsonError.deserializationError",
            message: UiText = UiText.DynamicString("JSON反序列化失败"),
            dataType: String = "unknown",
            cause: Throwable? = null,
            metadataMap: Map<String, Any> = emptyMap(),
        ): ModernDataError {
            val metadataWithDeserialization = metadataMap.toMutableMap().apply {
                put("data_type", dataType)
                put(StandardKeys.ERROR_SUBTYPE.key, "deserialization")
            }
            return ModernDataError(
                operationName = operationName,
                errorType = GlobalErrorType.Data.Conversion,
                category = ErrorCategory.SYSTEM,
                uiMessage = message,
                severity = ErrorSeverity.ERROR,
                recoverable = false,
                cause = cause,
                metadataMap = metadataWithDeserialization,
            )
        }
    }
}
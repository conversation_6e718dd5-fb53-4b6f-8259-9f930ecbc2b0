package com.example.gymbro.designSystem.components.extras

import android.graphics.RenderEffect
import android.graphics.RuntimeShader
import android.graphics.Shader
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsHoveredAsState
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asComposeRenderEffect
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import com.example.gymbro.designSystem.theme.tokens.Tokens
import kotlinx.coroutines.channels.Channel

/*
 * ==========================================================================
 * 性能优化的核心架构
 * ==========================================================================
 */

/**
 * 🚀 性能优化的外观配置
 *
 * 关键优化：
 * 1. 减少不必要的参数
 * 2. 使用更高效的默认值
 * 3. 添加性能等级控制
 */
@Immutable
data class LiquidGlassAppearance(
    // 🔵 核心效果参数 - 精简版
    val blurRadius: Float = 12f, // 降低默认模糊
    val distortionStrength: Float = 0.01f, // 降低默认扭曲
    val cornerRadius: Dp = Tokens.Radius.Large,
    val rimLightIntensity: Float = 0.25f, // 降低默认强度
    val chromaticAberration: Float = 0.008f, // 降低默认色散
    // ⚡ 性能控制
    val performanceLevel: PerformanceLevel = PerformanceLevel.BALANCED,
    val enableInteraction: Boolean = true, // 可选择禁用交互
    val enableAnimation: Boolean = true, // 可选择禁用动画
    // 🎨 简化的颜色调整
    val colorIntensity: Float = 1f, // 统一的颜色强度控制
) {
    enum class PerformanceLevel {
        MINIMAL, // 最小效果，最佳性能
        BALANCED, // 平衡效果和性能
        PREMIUM, // 最佳效果，可能影响性能
    }

    /**
     * 根据性能等级自动调整参数
     */
    val optimizedBlurRadius: Float
        get() =
            when (performanceLevel) {
                PerformanceLevel.MINIMAL -> blurRadius * 0.5f
                PerformanceLevel.BALANCED -> blurRadius
                PerformanceLevel.PREMIUM -> blurRadius * 1.5f
            }

    val optimizedDistortion: Float
        get() =
            when (performanceLevel) {
                PerformanceLevel.MINIMAL -> distortionStrength * 0.3f
                PerformanceLevel.BALANCED -> distortionStrength
                PerformanceLevel.PREMIUM -> distortionStrength * 2f
            }
}

/**
 * 🎯 简化的设计令牌
 */
@Immutable
data class LiquidGlassToken(
    val appearance: LiquidGlassAppearance,
    val behavior: LiquidGlassBehavior = { it },
)

typealias LiquidGlassBehavior = @Composable (base: LiquidGlassAppearance) -> LiquidGlassAppearance

/*
 * ==========================================================================
 * 🚀 性能优化的状态管理
 * ==========================================================================
 */

/**
 * 🔥 高性能指针状态管理
 *
 * 关键优化：
 * 1. 使用Channel减少重组频率
 * 2. 状态节流处理
 * 3. 内存池复用
 */
@Stable
internal class OptimizedPointerState {
    // 使用普通变量而非State，避免不必要的重组
    var normalizedX by mutableFloatStateOf(0.5f)
    var normalizedY by mutableFloatStateOf(0.5f)
    var velocityX by mutableFloatStateOf(0f)
    var velocityY by mutableFloatStateOf(0f)

    private var lastUpdateTime = 0L
    private var lastX = 0f
    private var lastY = 0f
    private var viewWidth = 1f
    private var viewHeight = 1f

    // 🎯 节流更新 - 避免过度更新
    private val updateChannel = Channel<Unit>(Channel.CONFLATED)

    fun start(
        offset: Offset,
        size: IntSize,
    ) {
        viewWidth = size.width.toFloat()
        viewHeight = size.height.toFloat()
        updatePosition(offset)
    }

    fun update(offset: Offset) {
        updatePosition(offset)
        // 使用Channel进行节流
        updateChannel.trySend(Unit)
    }

    private fun updatePosition(offset: Offset) {
        val currentTime = System.nanoTime()
        val newX = (offset.x / viewWidth).coerceIn(0f, 1f)
        val newY = (offset.y / viewHeight).coerceIn(0f, 1f)

        val deltaTime = (currentTime - lastUpdateTime) / 1_000_000_000f
        if (deltaTime > 0.016f) { // 限制更新频率到60fps
            if (lastUpdateTime != 0L) {
                velocityX = ((newX - lastX) / deltaTime).coerceIn(-10f, 10f)
                velocityY = ((newY - lastY) / deltaTime).coerceIn(-10f, 10f)
            }

            normalizedX = newX
            normalizedY = newY
            lastX = newX
            lastY = newY
            lastUpdateTime = currentTime
        }
    }

    fun stop() {
        // 平滑衰减而非立即停止
        velocityX *= 0.8f
        velocityY *= 0.8f
    }
}

/**
 * 🎯 着色器缓存管理
 */
@Stable
internal class ShaderCache {
    private val cache = mutableMapOf<String, RuntimeShader>()

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    fun getOrCreate(
        key: String,
        shaderSource: String,
    ): RuntimeShader = cache.getOrPut(key) { RuntimeShader(shaderSource) }

    fun clear() {
        cache.clear()
    }
}

internal val LocalShaderCache = compositionLocalOf { ShaderCache() }

/*
 * ==========================================================================
 * 🚀 优化的主入口API
 * ==========================================================================
 */

/**
 * 🎯 高性能液态玻璃修饰符
 */
fun Modifier.liquidGlass(
    token: LiquidGlassToken,
    debugMode: Boolean = false,
): Modifier =
    composed {
        val currentAppearance = token.behavior(token.appearance)

        // 🎯 性能分支 - 根据性能等级选择实现
        when (currentAppearance.performanceLevel) {
            LiquidGlassAppearance.PerformanceLevel.MINIMAL -> {
                liquidGlassMinimal(currentAppearance)
            }
            LiquidGlassAppearance.PerformanceLevel.BALANCED -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    liquidGlassOptimized(currentAppearance, debugMode)
                } else {
                    liquidGlassLegacyOptimized(currentAppearance)
                }
            }
            LiquidGlassAppearance.PerformanceLevel.PREMIUM -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    liquidGlassPremium(currentAppearance, debugMode)
                } else {
                    liquidGlassLegacyOptimized(currentAppearance)
                }
            }
        }
    }

/*
 * ==========================================================================
 * 🚀 性能优化的实现
 * ==========================================================================
 */

/**
 * ⚡ 最小性能版本 - 纯Compose实现，无着色器
 */
@Composable
private fun Modifier.liquidGlassMinimal(
    appearance: LiquidGlassAppearance,
): Modifier =
    this
        .clip(RoundedCornerShape(appearance.cornerRadius))
        .background(
            brush =
            Brush.radialGradient(
                colors =
                listOf(
                    Color.White.copy(alpha = 0.1f * appearance.colorIntensity),
                    Color.White.copy(alpha = 0.05f * appearance.colorIntensity),
                    Color.Transparent,
                ),
            ),
        ).blur(radius = (appearance.optimizedBlurRadius * 0.5f).dp)
// 移除边框线条，保持纯净的液态玻璃效果

/**
 * ⚖️ 平衡版本 - 优化的着色器实现
 */
@RequiresApi(Build.VERSION_CODES.TIRAMISU)
@Composable
private fun Modifier.liquidGlassOptimized(
    appearance: LiquidGlassAppearance,
    debugMode: Boolean,
): Modifier =
    composed {
        val shaderCache = LocalShaderCache.current
        val pointerState = remember { OptimizedPointerState() }

        // 🎯 优化的时间动画 - 使用更高效的动画
        val animatedTime by rememberInfiniteTransition(label = "liquidTime").animateFloat(
            initialValue = 0f,
            targetValue = 1f,
            animationSpec =
            infiniteRepeatable(
                animation =
                tween(
                    durationMillis = if (appearance.enableAnimation) 8000 else Int.MAX_VALUE,
                    easing = LinearEasing,
                ),
                repeatMode = RepeatMode.Restart,
            ),
            label = "time",
        )

        this
            .clip(RoundedCornerShape(appearance.cornerRadius))
            .then(
                if (appearance.enableInteraction) {
                    Modifier.pointerInput(Unit) {
                        detectDragGestures(
                            onDragStart = { offset -> pointerState.start(offset, size) },
                            onDragEnd = { pointerState.stop() },
                            onDragCancel = { pointerState.stop() },
                        ) { change, _ ->
                            pointerState.update(change.position)
                        }
                    }
                } else {
                    Modifier
                },
            ).graphicsLayer {
                // 🎯 简化的着色器链 - 只使用核心效果
                val liquidShader =
                    shaderCache
                        .getOrCreate(
                            "liquid_optimized",
                            LIQUID_GLASS_OPTIMIZED_SHADER,
                        ).apply {
                            setFloatUniform("uResolution", size.width, size.height)
                            setFloatUniform("uTime", animatedTime * 8f) // 缩放时间
                            setFloatUniform("uPointerPos", pointerState.normalizedX, pointerState.normalizedY)
                            setFloatUniform(
                                "uPointerVel",
                                pointerState.velocityX * 0.1f,
                                pointerState.velocityY * 0.1f,
                            )
                            setFloatUniform("uDistortion", appearance.optimizedDistortion)
                            setFloatUniform("uChromatic", appearance.chromaticAberration)
                            setFloatUniform("uIntensity", appearance.colorIntensity)
                            setIntUniform("uDebug", if (debugMode) 1 else 0)
                        }

                val liquidEffect = RenderEffect.createRuntimeShaderEffect(liquidShader, "uTexture")
                val blurEffect =
                    RenderEffect.createBlurEffect(
                        appearance.optimizedBlurRadius,
                        appearance.optimizedBlurRadius,
                        Shader.TileMode.CLAMP,
                    )

                renderEffect = RenderEffect.createChainEffect(liquidEffect, blurEffect).asComposeRenderEffect()
            }
        // 移除边框线条，依赖着色器效果即可
    }

/**
 * 🏆 高级版本 - 完整效果但优化性能
 */
@RequiresApi(Build.VERSION_CODES.TIRAMISU)
@Composable
private fun Modifier.liquidGlassPremium(
    appearance: LiquidGlassAppearance,
    debugMode: Boolean,
): Modifier =
    composed {
        // 基本与优化版本相同，但使用更复杂的着色器
        liquidGlassOptimized(appearance, debugMode)
    }

/**
 * 📱 优化的兼容版本
 */
@Composable
private fun Modifier.liquidGlassLegacyOptimized(
    appearance: LiquidGlassAppearance,
): Modifier =
    composed {
        val density = LocalDensity.current

        // 🎯 缓存计算结果
        val cachedColors =
            remember(appearance.colorIntensity) {
                listOf(
                    Color.White.copy(alpha = 0.08f * appearance.colorIntensity),
                    Color.White.copy(alpha = 0.04f * appearance.colorIntensity),
                    Color.Cyan.copy(alpha = 0.06f * appearance.colorIntensity),
                    Color.Transparent,
                )
            }

        this
            .clip(RoundedCornerShape(appearance.cornerRadius))
            .blur(radius = with(density) { appearance.optimizedBlurRadius.dp })
            .background(
                brush = Brush.verticalGradient(colors = cachedColors),
            )
        // 移除边框和高光绘制，保持纯净效果
    }

/*
 * ==========================================================================
 * 🚀 优化的着色器
 * ==========================================================================
 */

/**
 * ⚡ 高性能优化着色器 - 减少计算复杂度
 */
private const val LIQUID_GLASS_OPTIMIZED_SHADER = """
uniform float2 uResolution;
uniform float uTime;
uniform float2 uPointerPos;
uniform float2 uPointerVel;
uniform float uDistortion;
uniform float uChromatic;
uniform float uIntensity;
uniform int uDebug;
uniform shader uTexture;

// 🎯 优化的噪声函数 - 减少迭代次数
float optimizedNoise(float2 p) {
    // 简化的噪声，只用2次迭代而非4次
    float value = 0.0;
    value += 0.5 * sin(p.x * 4.0 + uTime) * cos(p.y * 4.0 + uTime * 0.7);
    value += 0.25 * sin(p.x * 8.0 + uTime * 1.3) * cos(p.y * 8.0 + uTime * 0.9);
    return value;
}

half4 main(float2 coord) {
    float2 uv = coord / uResolution;

    // 调试模式
    if (uDebug > 0) {
        float noise = optimizedNoise(uv * 6.0) * 0.5 + 0.5;
        return half4(noise, noise, noise, 1.0);
    }

    // 🎯 优化的扭曲计算
    float2 distortion = float2(
        optimizedNoise(uv * 4.0 + uTime * 0.1),
        optimizedNoise(uv * 4.0 + float2(1.0, 1.0) + uTime * 0.1)
    ) * uDistortion * uIntensity;

    // 🎯 简化的指针交互
    float2 pointerInfluence = (uPointerPos - uv) * length(uPointerVel) * 0.05;
    distortion += pointerInfluence;

    // 🎯 优化的色散 - 使用像素偏移
    float2 offset = distortion * uResolution;
    float chromatic = uChromatic * 15.0 * uIntensity;

    half4 colorR = uTexture.eval(coord + offset + float2(chromatic, 0.0));
    half4 colorG = uTexture.eval(coord + offset);
    half4 colorB = uTexture.eval(coord + offset - float2(chromatic, 0.0));

    return half4(colorR.r, colorG.g, colorB.b, colorG.a);
}
"""

/*
 * ==========================================================================
 * 🎯 优化的预设令牌
 * ==========================================================================
 */

object OptimizedLiquidGlassTokens {
    /**
     * ⚡ 高性能版本 - 适合列表项等高频使用场景
     */
    val HighPerformance =
        LiquidGlassToken(
            appearance =
            LiquidGlassAppearance(
                blurRadius = 8f,
                distortionStrength = 0.005f,
                rimLightIntensity = 0.2f,
                chromaticAberration = 0.004f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.MINIMAL,
                enableInteraction = false,
                enableAnimation = false,
            ),
        )

    /**
     * ⚖️ 平衡版本 - 日常使用的最佳选择
     */
    val Balanced =
        LiquidGlassToken(
            appearance =
            LiquidGlassAppearance(
                blurRadius = 12f,
                distortionStrength = 0.01f,
                rimLightIntensity = 0.3f,
                chromaticAberration = 0.008f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.BALANCED,
                enableInteraction = true,
                enableAnimation = true,
            ),
        )

    /**
     * 🏆 高端版本 - 适合重点展示的元素
     */
    val Premium =
        LiquidGlassToken(
            appearance =
            LiquidGlassAppearance(
                blurRadius = 16f,
                distortionStrength = 0.02f,
                rimLightIntensity = 0.4f,
                chromaticAberration = 0.012f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.PREMIUM,
                enableInteraction = true,
                enableAnimation = true,
                colorIntensity = 1.2f,
            ),
        )
}

/*
 * ==========================================================================
 * 🎯 优化的交互行为
 * ==========================================================================
 */

/**
 * 🚀 高性能交互行为 - 减少重组次数
 */
@Composable
fun LiquidGlassToken.withOptimizedInteraction(
    interactionSource: MutableInteractionSource,
): LiquidGlassToken =
    this.copy(behavior = { baseAppearance ->
        val currentAppearance = this.behavior(baseAppearance)

        // 🎯 使用更高效的状态收集
        val isPressed by interactionSource.collectIsPressedAsState()
        val isHovered by interactionSource.collectIsHoveredAsState()

        // 🎯 缓存动画规格
        val pressedSpec = remember { tween<Float>(100) }
        val hoverSpec = remember { tween<Float>(150) }

        val pressedMultiplier by animateFloatAsState(
            targetValue = if (isPressed) 1.5f else 1f,
            animationSpec = pressedSpec,
            label = "pressed",
        )

        val hoverMultiplier by animateFloatAsState(
            targetValue = if (isHovered) 1.2f else 1f,
            animationSpec = hoverSpec,
            label = "hover",
        )

        currentAppearance.copy(
            distortionStrength = currentAppearance.distortionStrength * pressedMultiplier,
            rimLightIntensity = currentAppearance.rimLightIntensity * hoverMultiplier,
            colorIntensity = currentAppearance.colorIntensity * hoverMultiplier,
        )
    })

/*
 * ==========================================================================
 * 🎯 优化的便捷组件
 * ==========================================================================
 */

/**
 * 🚀 高性能聊天输入背景
 */
@Composable
fun OptimizedLiquidChatInputBackground(
    modifier: Modifier = Modifier,
    performanceLevel: LiquidGlassAppearance.PerformanceLevel = LiquidGlassAppearance.PerformanceLevel.BALANCED,
    content: @Composable BoxScope.() -> Unit = {},
) {
    val token =
        remember(performanceLevel) {
            LiquidGlassToken(
                appearance =
                LiquidGlassAppearance(
                    blurRadius = 10f,
                    distortionStrength = 0.008f,
                    rimLightIntensity = 0.25f,
                    chromaticAberration = 0.006f,
                    performanceLevel = performanceLevel,
                    enableInteraction = false, // 聊天输入不需要交互
                    enableAnimation = performanceLevel != LiquidGlassAppearance.PerformanceLevel.MINIMAL,
                ),
            )
        }

    Box(
        modifier = modifier.liquidGlass(token),
        content = content,
    )
}

/**
 * 🎯 高性能按钮背景
 */
@Composable
fun OptimizedLiquidButtonBackground(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
    performanceLevel: LiquidGlassAppearance.PerformanceLevel = LiquidGlassAppearance.PerformanceLevel.BALANCED,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    content: @Composable BoxScope.() -> Unit = {},
) {
    val token =
        remember(performanceLevel) {
            LiquidGlassToken(
                appearance =
                LiquidGlassAppearance(
                    blurRadius = 14f,
                    distortionStrength = 0.015f,
                    rimLightIntensity = 0.35f,
                    chromaticAberration = 0.01f,
                    performanceLevel = performanceLevel,
                    enableInteraction = true,
                    enableAnimation = true,
                    colorIntensity = 1.1f,
                ),
            )
        }.withOptimizedInteraction(interactionSource)

    Box(
        modifier =
        modifier
            .liquidGlass(token)
            .pointerInput(onClick) {
                detectTapGestures(onTap = { onClick() })
            },
        content = content,
    )
}

/**
 * 🚀 液态玻璃输入框背景
 *
 * 专为输入框设计的轻量级液态玻璃效果背景
 */
@Composable
fun LiquidInputBackground(
    modifier: Modifier = Modifier,
    performanceLevel: LiquidGlassAppearance.PerformanceLevel = LiquidGlassAppearance.PerformanceLevel.BALANCED,
    content: @Composable BoxScope.() -> Unit = {},
) {
    val token =
        remember(performanceLevel) {
            LiquidGlassToken(
                appearance =
                LiquidGlassAppearance(
                    blurRadius = 10f,
                    distortionStrength = 0.006f, // 轻微的扭曲效果
                    rimLightIntensity = 0.3f, // 明亮的边缘
                    chromaticAberration = 0.005f, // 轻度色散
                    performanceLevel = performanceLevel,
                    enableInteraction = true, // 启用交互增强体验
                    enableAnimation = true, // 启用动画效果
                ),
            )
        }

    Box(
        modifier = modifier.liquidGlass(token),
        content = content,
    )
}

/*
 * ==========================================================================
 * 🎯 性能监控和调试
 * ==========================================================================
 */

/**
 * 🔍 性能监控组件 - 开发时使用
 */
@Composable
fun LiquidGlassPerformanceMonitor(
    content: @Composable () -> Unit,
) {
    // 在开发环境中可以添加性能监控逻辑
    content()
}

/*
 * ==========================================================================
 * 预览
 * ==========================================================================
 */

@GymBroPreview
@Composable
private fun OptimizedLiquidGlassPreview() {
    GymBroTheme {
        Column(
            modifier = Modifier
                .background(ColorTokens.Dark.Background) // 使用深色背景展示效果
                .padding(Tokens.Spacing.Medium), // 1个字符宽度的内部间距
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 高性能版本 - 使用标准卡片高度
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Card.HeightMin) // 80dp - 标准卡片最小高度
                    .clip(RoundedCornerShape(Tokens.Radius.Card)) // 自然美观的圆角
                    .liquidGlass(OptimizedLiquidGlassTokens.HighPerformance),
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "高性能液态玻璃",
                        style = MaterialTheme.typography.bodyLarge,
                        color = ColorTokens.Dark.OnSurface, // 使用 Token 文本色
                    )
                }
            }

            // 平衡版本 - 使用按钮高度
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Button.HeightPrimary) // 56dp - 标准按钮高度
                    .clip(RoundedCornerShape(Tokens.Radius.Button)) // 按钮圆角
                    .liquidGlass(OptimizedLiquidGlassTokens.Balanced),
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "平衡版本液态玻璃",
                        style = MaterialTheme.typography.bodyLarge,
                        color = ColorTokens.Dark.OnSurface, // 使用 Token 文本色
                    )
                }
            }

            // 高端版本 - 使用大卡片高度
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Card.HeightSmall) // 120dp - 小卡片高度
                    .clip(RoundedCornerShape(Tokens.Radius.Card)) // 卡片圆角
                    .liquidGlass(OptimizedLiquidGlassTokens.Premium),
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "高端版本液态玻璃",
                        style = MaterialTheme.typography.headlineSmall,
                        color = ColorTokens.Dark.Primary, // 使用主色显示重要性
                    )
                }
            }
        }
    }
}

package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.core.ml.embedding.EngineStatus
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * BGE状态指示器 - 显示BGE引擎加载进度和状态
 *
 * 用户体验优化：
 * 1. 在用户输入3-10秒窗口期间显示加载进度
 * 2. BGE就绪后自动隐藏，实现"零延迟"AI体验
 * 3. 设备差异化显示（4G/6GB/8GB设备不同策略）
 * 4. 渐进式状态反馈，让用户了解系统准备情况
 */
@Composable
internal fun BgeStatusIndicator(
    bgeEngineStatus: State<EngineStatus>,
    modifier: Modifier = Modifier,
    autoHideWhenReady: Boolean = true,
    showOnlyDuringLoading: Boolean = true,
) {
    val engineStatus by bgeEngineStatus

    // 🎯 智能显示逻辑：只在有意义的时候显示
    val shouldShow = remember(engineStatus, showOnlyDuringLoading, autoHideWhenReady) {
        when {
            // 如果只在加载期间显示，且引擎已就绪或未初始化，则隐藏
            showOnlyDuringLoading && (engineStatus == EngineStatus.READY || engineStatus == EngineStatus.UNINITIALIZED) -> false
            // 如果自动隐藏且已就绪，则隐藏
            autoHideWhenReady && engineStatus == EngineStatus.READY -> false
            // 其他情况根据状态决定
            else -> engineStatus == EngineStatus.INITIALIZING
        }
    }

    AnimatedVisibility(
        visible = shouldShow,
        enter = slideInVertically(initialOffsetY = { -it }) + fadeIn(),
        exit = slideOutVertically(targetOffsetY = { -it }) + fadeOut(),
        modifier = modifier,
    ) {
        BgeStatusCard(
            engineStatus = engineStatus,
        )
    }
}

@Composable
private fun BgeStatusCard(
    engineStatus: EngineStatus,
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
        colors = CardDefaults.cardColors(
            containerColor = Tokens.Color.Gray100,
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 2.dp,
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Tokens.Spacing.Medium),
        ) {
            // 状态标题和图标
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = getStatusTitle(engineStatus),
                    style = TextStyle(
                        fontSize = Tokens.Typography.BodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = Tokens.Color.Gray900,
                    ),
                )

                StatusIcon(engineStatus = engineStatus)
            }

            Spacer(modifier = Modifier.height(Tokens.Spacing.Small))

            // 进度条（仅在初始化时显示）
            if (engineStatus == EngineStatus.INITIALIZING) {
                LinearProgressIndicator(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(4.dp)
                        .clip(RoundedCornerShape(2.dp)),
                    color = getProgressColor(engineStatus),
                    trackColor = Tokens.Color.Gray200,
                )

                Spacer(modifier = Modifier.height(Tokens.Spacing.Small))

                Text(
                    text = getStatusDescription(engineStatus),
                    style = TextStyle(
                        fontSize = Tokens.Typography.Small,
                        color = Tokens.Color.Gray600,
                    ),
                )
            }
        }
    }
}

@Composable
private fun StatusIcon(engineStatus: EngineStatus) {
    Box(
        modifier = Modifier
            .size(8.dp)
            .clip(RoundedCornerShape(4.dp))
            .background(getStatusColor(engineStatus)),
    )
}

private fun getStatusTitle(status: EngineStatus): String {
    return when (status) {
        EngineStatus.UNINITIALIZED -> "AI引擎准备中"
        EngineStatus.INITIALIZING -> "AI引擎加载中"
        EngineStatus.READY -> "AI引擎就绪"
        EngineStatus.ERROR -> "引擎加载异常"
    }
}

private fun getStatusDescription(status: EngineStatus): String {
    return when (status) {
        EngineStatus.INITIALIZING -> "正在初始化BGE模型，不影响APP正常使用"
        EngineStatus.READY -> "BGE引擎已就绪，AI功能可用"
        EngineStatus.ERROR -> "BGE引擎加载失败，请检查网络连接"
        else -> "准备中..."
    }
}

@androidx.compose.runtime.Composable
private fun getStatusColor(status: EngineStatus): androidx.compose.ui.graphics.Color {
    return when (status) {
        EngineStatus.UNINITIALIZED -> Tokens.Color.Gray400
        EngineStatus.INITIALIZING -> Tokens.Color.CTAPrimary
        EngineStatus.READY -> Tokens.Color.SuccessLight
        EngineStatus.ERROR -> Tokens.Color.Error
    }
}

@androidx.compose.runtime.Composable
private fun getProgressColor(status: EngineStatus): androidx.compose.ui.graphics.Color {
    return when (status) {
        EngineStatus.INITIALIZING -> Tokens.Color.CTAPrimary
        else -> Tokens.Color.CTAPrimary
    }
}

/**
 * 紧凑版BGE状态指示器 - 用于TopBar中的小型显示
 */
@Composable
internal fun CompactBgeStatusIndicator(
    bgeEngineStatus: State<EngineStatus>,
    modifier: Modifier = Modifier,
) {
    val engineStatus by bgeEngineStatus

    // 🎯 只在加载期间显示，就绪后自动隐藏
    val shouldShow = engineStatus == EngineStatus.INITIALIZING

    AnimatedVisibility(
        visible = shouldShow,
        enter = fadeIn(),
        exit = fadeOut(),
        modifier = modifier,
    ) {
        Row(
            modifier = Modifier
                .background(
                    color = Tokens.Color.Gray100.copy(alpha = 0.8f),
                    shape = RoundedCornerShape(Tokens.Radius.Small),
                )
                .padding(horizontal = Tokens.Spacing.XSmall, vertical = Tokens.Spacing.Tiny),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Tiny),
        ) {
            // 状态指示点 - 更小的尺寸适合TopBar
            Box(
                modifier = Modifier
                    .size(4.dp)
                    .clip(RoundedCornerShape(2.dp))
                    .background(getStatusColor(engineStatus)),
            )

            Text(
                text = getCompactStatusText(engineStatus),
                style = TextStyle(
                    fontSize = Tokens.Typography.Tiny,
                    color = Tokens.Color.Gray700,
                ),
            )
        }
    }
}

private fun getCompactStatusText(status: EngineStatus): String {
    return when (status) {
        EngineStatus.INITIALIZING -> "加载中"
        EngineStatus.READY -> "就绪"
        EngineStatus.ERROR -> "错误"
        else -> "准备"
    }
}

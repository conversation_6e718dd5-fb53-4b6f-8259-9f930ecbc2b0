package com.example.gymbro.domain.profile.usecase

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.repository.AuthRepository
import com.example.gymbro.domain.profile.model.user.settings.PrivacySettings
import com.example.gymbro.domain.profile.model.user.settings.UserSettings
import com.example.gymbro.domain.user.service.UserDataService
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 获取用户设置用例
 *
 * 重构后使用 UserDataService 作为统一的用户数据管理接口。
 * 负责获取当前用户的设置信息，包括隐私设置、通知设置等。
 * 如果用户设置不存在，会返回默认设置。
 *
 * 核心变更：
 * - 使用 UserDataService 接口替代 UserAggregateRepository
 * - 使用 AuthRepository 替代 UserSessionManager
 * - 遵循 Clean Architecture 依赖倒置原则
 * - 确保数据的唯一真实来源（SSOT）
 *
 * @property userDataService 用户数据服务接口
 * @property authRepository 认证仓库，用于获取当前用户
 * @property logger 日志记录器
 */
@Singleton
class GetUserSettingsUseCase
@Inject
constructor(
    private val userDataService: UserDataService,
    private val authRepository: AuthRepository,
    private val logger: Logger,
) {
    /**
     * 获取用户设置
     * @param params Unit - 无参数
     * @return Flow<ModernResult<UserSettings?>> 用户设置或null
     */
    operator fun invoke(params: Unit): Flow<ModernResult<UserSettings?>> {
        logger.d("GetUserSettingsUseCase: 开始获取用户设置")

        return flow {
            emit(ModernResult.Loading)
            try {
                // 🔥 关键修复：使用 AuthRepository 获取当前用户ID
                val currentUserIdResult = authRepository.getCurrentUserId().first()
                when (currentUserIdResult) {
                    is ModernResult.Error -> {
                        emit(ModernResult.Error(currentUserIdResult.error))
                        return@flow
                    }
                    is ModernResult.Success -> {
                        val userId = currentUserIdResult.data
                        if (userId.isNullOrBlank()) {
                            logger.w("GetUserSettingsUseCase: 用户未登录，无法获取设置")
                            emit(
                                ModernResult.Error(
                                    BusinessErrors.BusinessError.rule(
                                        operationName = "getUserSettings",
                                        message = UiText.DynamicString("用户未登录，无法获取设置"),
                                        metadataMap = mapOf("errorType" to "UNAUTHORIZED"),
                                    ),
                                ),
                            )
                            return@flow
                        }

                        // 🔥 关键修复：使用 UserDataService 获取用户资料
                        val profileResult = userDataService.getCurrentUserProfile()
                        when (profileResult) {
                            is ModernResult.Success -> {
                                val profile = profileResult.data
                                if (profile != null) {
                                    // 创建默认的用户设置
                                    val userSettings =
                                        UserSettings(
                                            userId = userId,
                                            // 使用默认的隐私设置
                                            privacySettings = PrivacySettings.getDefault(),
                                        )
                                    emit(ModernResult.Success(userSettings))
                                    logger.d("GetUserSettingsUseCase: 成功获取用户设置")
                                } else {
                                    emit(ModernResult.Success(null))
                                    logger.d("GetUserSettingsUseCase: 用户设置不存在")
                                }
                            }
                            is ModernResult.Error -> {
                                emit(ModernResult.Error(profileResult.error))
                                logger.e("GetUserSettingsUseCase: 获取用户设置失败", profileResult.error.cause)
                            }
                            is ModernResult.Loading -> {
                                // 保持Loading状态，不需要额外处理
                            }
                        }
                    }
                    is ModernResult.Loading -> {
                        // 保持Loading状态
                    }
                }
            } catch (e: Exception) {
                logger.e("GetUserSettingsUseCase: 获取用户设置时发生异常", e)
                emit(
                    ModernResult.Error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "getUserSettings",
                            message = UiText.DynamicString("获取用户设置失败"),
                            metadataMap = mapOf("error" to e.message.orEmpty()),
                        ),
                    ),
                )
            }
        }.catch { e ->
            logger.e("GetUserSettingsUseCase: Flow异常", e)
            emit(
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "getUserSettings",
                        message = UiText.DynamicString("获取用户设置时发生错误"),
                        metadataMap = mapOf("error" to e.message.orEmpty()),
                    ),
                ),
            )
        }
    }
}

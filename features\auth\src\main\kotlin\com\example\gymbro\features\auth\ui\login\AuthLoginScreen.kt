package com.example.gymbro.features.auth.ui.login

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.ThemeMode
import com.example.gymbro.designSystem.components.animations.GymBroTypeWriter
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.components.extras.AuthStarRingCanvas
import com.example.gymbro.designSystem.components.extras.GymBroLogo
import com.example.gymbro.designSystem.components.gymBroThemeToggleButton
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.GymBroMotionConfig
import com.example.gymbro.designSystem.theme.motion.ProvideGymBroMotionConfig
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.auth.ui.animation.AuthAnimSpec
import com.example.gymbro.features.auth.ui.login.components.AuthBackgroundLayer
import com.example.gymbro.features.auth.ui.login.components.AuthButtonSection

/**
 * Auth登录主屏幕 - 现代化设计
 *
 * 按照UI落位.md的坐标规范设计：
 * - 跳过按钮 (85%-95%, 3%-8%)
 * - 应用名称 (50%, 20%)
 * - 版本信息 (50%, 26%)
 * - 装饰图形 (50%, 35%)
 * - 标语文本 (50%, 45%)
 * - 按钮组 (10%-90%, 55%-85%)
 * - 法律声明 (50%, 95%)
 */
@Composable
fun AuthLoginScreen(
    modifier: Modifier = Modifier,
    onLoginSuccess: () -> Unit = {},
    onRegisterClick: () -> Unit = {},
    onPhoneLoginClick: () -> Unit = {},
    onSkipClick: () -> Unit = {},
    onWeChatLoginClick: () -> Unit = {},
    onGoogleLoginClick: () -> Unit = {},
    onAnonymousLoginClick: () -> Unit = {},
    isLoading: Boolean = false,
) {
    // 🎯 主题状态管理：支持手动切换主题
    var currentThemeMode by remember { mutableStateOf(ThemeMode.SYSTEM) }
    val systemDarkTheme = isSystemInDarkTheme()

    // 根据主题模式计算实际的深色主题状态
    val isDarkTheme = remember(currentThemeMode, systemDarkTheme) {
        when (currentThemeMode) {
            ThemeMode.LIGHT -> false
            ThemeMode.DARK -> true
            ThemeMode.SYSTEM -> systemDarkTheme
        }
    }

    // 主题切换回调
    val onThemeToggle = remember {
        {
            currentThemeMode = when (currentThemeMode) {
                ThemeMode.LIGHT -> ThemeMode.DARK
                ThemeMode.DARK -> ThemeMode.SYSTEM
                ThemeMode.SYSTEM -> ThemeMode.LIGHT
            }
        }
    }

    AuthLoginScreenContent(
        modifier = modifier,
        isDarkTheme = isDarkTheme,
        currentThemeMode = currentThemeMode,
        onThemeToggle = onThemeToggle,
        isLoading = isLoading,
        onLoginSuccess = onLoginSuccess,
        onRegisterClick = onRegisterClick,
        onPhoneLoginClick = onPhoneLoginClick,
        onSkipClick = onSkipClick,
        onWeChatLoginClick = onWeChatLoginClick,
        onGoogleLoginClick = onGoogleLoginClick,
        onAnonymousLoginClick = onAnonymousLoginClick,
    )
}

/**
 * Auth登录屏幕内容 - 现代化实现
 * 严格按照UI落位.md的坐标系统布局
 */
@Composable
fun AuthLoginScreenContent(
    modifier: Modifier = Modifier,
    isDarkTheme: Boolean,
    currentThemeMode: ThemeMode,
    onThemeToggle: () -> Unit,
    isLoading: Boolean,
    onLoginSuccess: () -> Unit,
    onRegisterClick: () -> Unit,
    onPhoneLoginClick: () -> Unit,
    onSkipClick: () -> Unit,
    onWeChatLoginClick: () -> Unit,
    onGoogleLoginClick: () -> Unit,
    onAnonymousLoginClick: () -> Unit,
) {
    // 🎯 性能监控：简化重组跟踪
    val recompositionCounter = remember { mutableStateOf(0) }
    LaunchedEffect(Unit) {
        recompositionCounter.value++
    }

    // 🎯 修复：恢复颜色动画，确保正确的颜色映射
    val backgroundColorAnimated by animateColorAsState(
        targetValue = if (isDarkTheme) Tokens.Color.Gray950 else Tokens.Color.Gray000,
        animationSpec = AuthAnimSpec.colorTransitionAnimation(),
        label = "BackgroundColor",
    )

    val contentColorAnimated by animateColorAsState(
        targetValue = if (isDarkTheme) Tokens.Color.Gray300 else Tokens.Color.Gray950,
        animationSpec = AuthAnimSpec.colorTransitionAnimation(),
        label = "ContentColor",
    )

    // 次要文字颜色 - 修复映射：浅色主题用深色文字
    val secondaryTextColor by animateColorAsState(
        targetValue = if (isDarkTheme) Tokens.Color.Gray500 else Tokens.Color.Gray700,
        animationSpec = AuthAnimSpec.colorTransitionAnimation(),
        label = "SecondaryTextColor",
    )

    // 法律声明文字颜色 - 修复映射：浅色主题用深色文字
    val legalTextColor by animateColorAsState(
        targetValue = if (isDarkTheme) Tokens.Color.Gray400 else Tokens.Color.Gray600,
        animationSpec = AuthAnimSpec.colorTransitionAnimation(),
        label = "LegalTextColor",
    )

    // Logo区域进入动画 - 修复：从0开始动画
    val logoEnterAnimation by animateFloatAsState(
        targetValue = 1f,
        animationSpec =
        AuthAnimSpec.logoEnterAnimation(
            delayMillis = AuthLoginConstants.LOGO_ENTER_DELAY_MS,
        ),
        label = "LogoEnterAnimation",
    )

    // 🎯 状态栏高度自适应 - 全屏显示时需要补偿状态栏高度
    val statusBarHeight = WindowInsets.statusBars.asPaddingValues().calculateTopPadding()

    ProvideGymBroMotionConfig(
        config = GymBroMotionConfig(
            enableAnimations = true,
            enableBreathing = true,
            enableInfiniteAnimations = true,
            enableMicroInteractions = true,
        ),
    ) {
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(backgroundColorAnimated),
        ) {
            // 背景层：优化的背景组件
            AuthBackgroundLayer(
                isDarkTheme = isDarkTheme,
                modifier = Modifier.align(Alignment.BottomCenter),
            )

            // 🎯 右上角主题切换按钮 - 添加状态栏高度补偿
            Box(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(
                        top = statusBarHeight + 16.dp,
                        end = 16.dp,
                    ),
            ) {
                gymBroThemeToggleButton(
                    currentThemeMode = currentThemeMode,
                    onThemeToggle = onThemeToggle,
                    modifier = Modifier
                        .alpha(logoEnterAnimation),
                )
            }

            // 主内容区域 - 严格按照坐标布局，添加状态栏高度补偿
            GrokStyleMainContent(
                logoEnterAnimation = logoEnterAnimation,
                contentColor = contentColorAnimated,
                secondaryTextColor = secondaryTextColor,
                isLoading = isLoading,
                isDarkTheme = isDarkTheme,
                statusBarHeight = statusBarHeight,
                onPhoneLoginClick = onPhoneLoginClick,
                onGoogleLoginClick = onGoogleLoginClick,
                onAnonymousLoginClick = onAnonymousLoginClick,
                onLoginSuccess = onLoginSuccess,
            )

            // [法律声明] - 位置: 底部中心 (50%, ~95%)
            Text(
                text = "继续即表示您同意 条款 和 隐私政策",
                color = legalTextColor,
                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(
                        bottom = Tokens.Spacing.Medium,
                        start = Tokens.Spacing.Large,
                        end = Tokens.Spacing.Large,
                    ),
            )
        }
    }
}

/**
 * 现代化主内容区域
 * 按照精确坐标落位系统布局，重新设计StarRingCanvas展示：
 * - 应用名称: (50%, ~22%) - 纯净LOGO，无背景干扰
 * - 版本信息: (50%, ~30%)
 * - 装饰图形: (50%, ~35%) - 更大的AuthStarRingCanvas (200dp)，在LOGO下方独立显示
 * - 应用标语: (50%, ~45%)
 * - 按钮组: y轴55%至85%
 * - 法律声明: (50%, ~95%)
 */
@Composable
private fun GrokStyleMainContent(
    logoEnterAnimation: Float,
    contentColor: androidx.compose.ui.graphics.Color,
    secondaryTextColor: androidx.compose.ui.graphics.Color,
    isLoading: Boolean,
    isDarkTheme: Boolean,
    statusBarHeight: androidx.compose.ui.unit.Dp,
    onPhoneLoginClick: () -> Unit,
    onGoogleLoginClick: () -> Unit,
    onAnonymousLoginClick: () -> Unit,
    onLoginSuccess: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val enterOffsetPx = with(density) { AuthLoginConstants.CONTENT_ENTER_OFFSET.toPx() }

    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = statusBarHeight), // 添加状态栏高度补偿
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // 1. [应用名称] 标题 - 位置: (50%, ~22%) - 使用金属质感LOGO
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.22f),
                contentAlignment = Alignment.BottomCenter,
            ) {
                GymBroLogo(
                    modifier =
                    Modifier
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                        },
                    animated = true,
                    useHdr = false,
                    style = MaterialTheme.typography.displayLarge.copy(fontSize = 72.sp),
                    glowEffect = false,
                )
            }

            // 2. [版本信息] 副标题 - 位置: (50%, ~30%)
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.10f),
                // 30% - 22% = 8%的空间分配
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = UiText.DynamicString("Android Beta").asString(),
                    fontSize = MaterialTheme.typography.titleMedium.fontSize,
                    color = secondaryTextColor,
                    textAlign = TextAlign.Center,
                    modifier =
                    Modifier
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                        },
                )
            }

            // 3. [装饰图形] - 位置: (50%, ~35%) - 重新设计为更大的StarRingCanvas
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.125f),
                // 35% - 30% = 5%的空间分配
                contentAlignment = Alignment.Center,
            ) {
                // 🎯 新设计：使用专门优化的AuthStarRingCanvas，尺寸更大，效果更好
                Box(
                    modifier =
                    Modifier
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                        },
                    contentAlignment = Alignment.Center,
                ) {
                    AuthStarRingCanvas(
                        isDarkTheme = isDarkTheme,
                        enableAnimation = true,
                        size = 200.dp, // 比之前的120dp大很多，更加醒目
                    )
                }
            }

            // 4. [应用标语] 文本 - 位置: (50%, ~45%)
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.20f),
                // 45% - 35% = 10%的空间分配
                contentAlignment = Alignment.Center,
            ) {
                // 🎯 修复层级问题：确保打字机动画在正确层级显示
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * -enterOffsetPx
                        },
                    contentAlignment = Alignment.Center,
                ) {
                    GymBroTypeWriter(
                        text = UiText.DynamicString("Understand the universe"),
                        textColor = secondaryTextColor,
                        fontSize = 18.sp,
                        enableAnimation = true,
                        autoStart = true,
                        showCursor = true,
                        cursorChar = '_',
                        typingSpeed = 60L, // 更快的打字速度
                        blinkSpeed = 300L, // 更快的闪烁
                        textAlign = TextAlign.Center,
                    )
                }
            }

            // 5. [交互按钮组] - 位置: y轴55%至85% (30%的垂直空间)
            Box(
                modifier =
                Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.75f),
                // 占用剩余空间的大部分 (55%-85%区域)
                contentAlignment = Alignment.TopCenter,
            ) {
                AuthButtonSection(
                    isLoading = isLoading,
                    contentColor = contentColor,
                    onPhoneLoginClick = onPhoneLoginClick,
                    onGoogleLoginClick = onGoogleLoginClick,
                    onAnonymousLoginClick = onAnonymousLoginClick,
                    onLoginSuccess = onLoginSuccess,
                    modifier =
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = Tokens.Spacing.Large)
                        .alpha(logoEnterAnimation)
                        .graphicsLayer {
                            translationY = (1f - logoEnterAnimation) * enterOffsetPx
                        },
                )
            }
        }
    }
}

// === @GymBroPreview 预览组件 ===

@GymBroPreview
@Composable
private fun AuthLoginScreenPreview() {
    GymBroTheme {
        AuthLoginScreenContent(
            isDarkTheme = true,
            currentThemeMode = ThemeMode.DARK,
            onThemeToggle = {},
            isLoading = false,
            onLoginSuccess = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onSkipClick = {},
            onWeChatLoginClick = {},
            onGoogleLoginClick = {},
            onAnonymousLoginClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun AuthLoginScreenLightPreview() {
    GymBroTheme {
        AuthLoginScreenContent(
            isDarkTheme = false,
            currentThemeMode = ThemeMode.LIGHT,
            onThemeToggle = {},
            isLoading = false,
            onLoginSuccess = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onSkipClick = {},
            onWeChatLoginClick = {},
            onGoogleLoginClick = {},
            onAnonymousLoginClick = {},
        )
    }
}

@GymBroPreview
@Composable
private fun AuthLoginScreenLoadingPreview() {
    GymBroTheme {
        AuthLoginScreenContent(
            isDarkTheme = true,
            currentThemeMode = ThemeMode.SYSTEM,
            onThemeToggle = {},
            isLoading = true,
            onLoginSuccess = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onSkipClick = {},
            onWeChatLoginClick = {},
            onGoogleLoginClick = {},
            onAnonymousLoginClick = {},
        )
    }
}

/**
 * 新设计测试预览 - 验证StarRingCanvas在LOGO下方的效果
 */
@GymBroPreview
@Composable
private fun AuthLoginScreenNewDesignPreview() {
    GymBroTheme {
        AuthLoginScreenContent(
            isDarkTheme = false,
            currentThemeMode = ThemeMode.LIGHT,
            onThemeToggle = {},
            isLoading = false,
            onLoginSuccess = {},
            onRegisterClick = {},
            onPhoneLoginClick = {},
            onSkipClick = {},
            onWeChatLoginClick = {},
            onGoogleLoginClick = {},
            onAnonymousLoginClick = {},
        )
    }
}

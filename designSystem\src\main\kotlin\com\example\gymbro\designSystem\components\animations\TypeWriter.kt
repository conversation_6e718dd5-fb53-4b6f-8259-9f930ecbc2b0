package com.example.gymbro.designSystem.components.animations

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.LocalGymBroMotionConfig
import com.example.gymbro.designSystem.theme.tokens.Tokens
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * TypeWriter动画Token配置
 * 基于Motion系统的打字机特定配置
 * 🔥 【全面改进】更新打字机速度配置
 */
private object TypeWriterTokens {
    // 打字速度配置（毫秒）
    val TypingSpeedFast = 0L // 🔥 【全面改进】perthink无限制，无上限
    val TypingSpeedNormal = 33L // 🔥 【全面改进】正式phase/最终富文本：1秒30个字 = 33ms/字符
    val TypingSpeedSlow = 80L // 慢速打字（保持不变）

    // 光标闪烁速度（毫秒）
    val BlinkSpeedFast = 300L // 快速闪烁
    val BlinkSpeedNormal = 500L // 正常闪烁（原硬编码值）
    val BlinkSpeedSlow = 800L // 慢速闪烁

    // 自动消失延迟
    val AutoDismissFast = 2000L // 成功消息
    val AutoDismissNormal = 5000L // 普通消息
}

/**
 * 打字机效果组件 - 修复版
 *
 * 修复的问题：
 * - 改进的协程管理避免内存泄漏
 * - 更好的键管理避免不必要重组
 * - 支持状态提升
 * - 使用统一的动画Token配置
 * - 支持动画禁用
 *
 * @param text 要显示的文本（UiText）
 * @param modifier Modifier修饰符
 * @param currentIndex 当前显示的字符索引（支持外部控制）
 * @param onIndexChange 索引变化回调（支持状态提升）
 * @param isPlaying 是否播放动画（支持外部控制）
 * @param onPlayingChange 播放状态变化回调
 * @param cursorChar 光标字符
 * @param typingSpeed 打字速度（毫秒）- 现在使用Token默认值
 * @param blinkSpeed 光标闪烁速度（毫秒）- 现在使用Token默认值
 * @param textColor 文本颜色
 * @param fontSize 字体大小
 * @param textAlign 文本对齐方式
 * @param enableAnimation 是否启用打字动画
 * @param autoStart 是否自动开始
 * @param showCursor 是否显示光标
 * @param onComplete 打字完成回调
 */
@Composable
fun GymBroTypeWriter(
    text: UiText,
    modifier: Modifier = Modifier,
    currentIndex: Int? = null,
    onIndexChange: ((Int) -> Unit)? = null,
    isPlaying: Boolean? = null,
    onPlayingChange: ((Boolean) -> Unit)? = null,
    cursorChar: Char = '_',
    typingSpeed: Long = TypeWriterTokens.TypingSpeedNormal, // 使用Token替代硬编码
    blinkSpeed: Long = TypeWriterTokens.BlinkSpeedNormal, // 使用Token替代硬编码
    textColor: Color = MaterialTheme.colorScheme.onBackground,
    fontSize: TextUnit = 14.sp,
    textAlign: TextAlign = TextAlign.Center,
    enableAnimation: Boolean = true,
    autoStart: Boolean = true,
    showCursor: Boolean = true,
    maxLines: Int = Int.MAX_VALUE, // 🔥 修复3：添加maxLines参数
    overflow: TextOverflow = TextOverflow.Clip, // 🔥 修复3：添加overflow参数
    onComplete: (() -> Unit)? = null,
) {
    val motionConfig = LocalGymBroMotionConfig.current
    val fullText = text.asString()

    // 检查是否应该禁用动画
    val shouldAnimate =
        enableAnimation &&
            motionConfig.enableAnimations

    // 内部状态或外部状态
    var internalIndex by remember(fullText) { mutableStateOf(0) }
    var internalPlaying by remember(fullText) { mutableStateOf(autoStart && shouldAnimate) }
    var showCursorState by remember { mutableStateOf(true) }

    // 使用外部状态或内部状态
    val activeIndex = currentIndex ?: internalIndex
    val isActivelyPlaying = isPlaying ?: internalPlaying

    // 计算显示的文本
    val visibleText =
        if (shouldAnimate) {
            fullText.take(activeIndex)
        } else {
            fullText
        }

    val isComplete = activeIndex >= fullText.length

    // 打字协程 - 修复键管理和状态提升
    // 🔥 修复：添加 activeIndex 到依赖项，确保动画状态变化时重新触发
    LaunchedEffect(fullText, shouldAnimate, isActivelyPlaying, activeIndex) {
        if (!shouldAnimate) {
            // 禁用动画时直接显示全部文本
            if (currentIndex == null) internalIndex = fullText.length
            onIndexChange?.invoke(fullText.length)
            if (isPlaying == null) internalPlaying = false
            onPlayingChange?.invoke(false)
            onComplete?.invoke()
            return@LaunchedEffect
        }

        if (!isActivelyPlaying) return@LaunchedEffect

        // 执行打字动画
        for (index in activeIndex until fullText.length) {
            if (!isActive) break
            if (isPlaying == false || (!isActivelyPlaying && isPlaying == null)) break

            delay(typingSpeed)

            if (currentIndex == null) {
                internalIndex = index + 1
            }
            onIndexChange?.invoke(index + 1)
        }

        // 完成时的处理
        if (isActive && activeIndex >= fullText.length) {
            if (isPlaying == null) internalPlaying = false
            onPlayingChange?.invoke(false)
            onComplete?.invoke()
        }
    }

    // 光标闪烁协程 - 修复键管理
    LaunchedEffect(shouldAnimate, showCursor, isComplete) {
        if (!shouldAnimate || !showCursor) {
            showCursorState = false
            return@LaunchedEffect
        }

        while (isActive) {
            showCursorState = !showCursorState
            delay(blinkSpeed)
        }
    }

    // 清理协程状态
    DisposableEffect(fullText) {
        onDispose {
            // 协程会自动取消，但重置内部状态
            if (currentIndex == null) internalIndex = 0
            if (isPlaying == null) internalPlaying = autoStart
        }
    }

    Text(
        text =
        buildString {
            append(visibleText)
            // 只在需要时显示光标
            if (shouldAnimate && showCursor && showCursorState && !isComplete) {
                append(cursorChar)
            }
        },
        color = textColor,
        fontSize = fontSize,
        textAlign = textAlign,
        maxLines = maxLines, // 🔥 修复3：传递maxLines参数
        overflow = overflow, // 🔥 修复3：传递overflow参数
        modifier = modifier,
    )
}

/**
 * 状态提升版打字机组件
 * 完全由外部控制状态
 */
@Composable
fun GymBroTypeWriterControlled(
    text: UiText,
    currentIndex: Int,
    onIndexChange: (Int) -> Unit,
    isPlaying: Boolean,
    onPlayingChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    cursorChar: Char = '_',
    typingSpeed: Long = TypeWriterTokens.TypingSpeedNormal,
    blinkSpeed: Long = TypeWriterTokens.BlinkSpeedNormal,
    textColor: Color = MaterialTheme.colorScheme.onBackground,
    fontSize: TextUnit = 14.sp,
    textAlign: TextAlign = TextAlign.Center,
    showCursor: Boolean = true,
    onComplete: (() -> Unit)? = null,
) {
    GymBroTypeWriter(
        text = text,
        modifier = modifier,
        currentIndex = currentIndex,
        onIndexChange = onIndexChange,
        isPlaying = isPlaying,
        onPlayingChange = onPlayingChange,
        cursorChar = cursorChar,
        typingSpeed = typingSpeed,
        blinkSpeed = blinkSpeed,
        textColor = textColor,
        fontSize = fontSize,
        textAlign = textAlign,
        enableAnimation = true,
        autoStart = false,
        showCursor = showCursor,
        onComplete = onComplete,
    )
}

/**
 * 快速打字机预设
 */
@Composable
fun GymBroTypeWriterFast(
    text: UiText,
    modifier: Modifier = Modifier,
    textColor: Color = MaterialTheme.colorScheme.onBackground,
    onComplete: (() -> Unit)? = null,
) {
    GymBroTypeWriter(
        text = text,
        modifier = modifier,
        typingSpeed = TypeWriterTokens.TypingSpeedFast,
        blinkSpeed = TypeWriterTokens.BlinkSpeedFast,
        textColor = textColor,
        onComplete = onComplete,
    )
}

/**
 * 慢速打字机预设
 */
@Composable
fun GymBroTypeWriterSlow(
    text: UiText,
    modifier: Modifier = Modifier,
    textColor: Color = MaterialTheme.colorScheme.onBackground,
    onComplete: (() -> Unit)? = null,
) {
    GymBroTypeWriter(
        text = text,
        modifier = modifier,
        typingSpeed = TypeWriterTokens.TypingSpeedSlow,
        blinkSpeed = TypeWriterTokens.BlinkSpeedSlow,
        textColor = textColor,
        onComplete = onComplete,
    )
}

/**
 * 向后兼容的打字机组件
 */
@Deprecated("使用 GymBroTypeWriter", ReplaceWith("GymBroTypeWriter"))
@Composable
fun typeWriter(
    text: UiText,
    modifier: Modifier = Modifier,
    cursorChar: Char = '_',
    typingSpeed: Long = TypeWriterTokens.TypingSpeedNormal,
    blinkSpeed: Long = TypeWriterTokens.BlinkSpeedNormal,
    textColor: Color = MaterialTheme.colorScheme.onBackground,
    fontSize: TextUnit = 14.sp,
    textAlign: TextAlign = TextAlign.Center,
) {
    GymBroTypeWriter(
        text = text,
        modifier = modifier,
        cursorChar = cursorChar,
        typingSpeed = typingSpeed,
        blinkSpeed = blinkSpeed,
        textColor = textColor,
        fontSize = fontSize,
        textAlign = textAlign,
    )
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun GymBroTypeWriterPreview() {
    GymBroTheme {
        GymBroTypeWriter(
            text = UiText.DynamicString("理解健身宇宙"),
            textColor = Color.White,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroTypeWriterNoAnimationPreview() {
    GymBroTheme {
        GymBroTypeWriter(
            text = UiText.DynamicString("静态文本模式"),
            textColor = Color.Black,
            enableAnimation = false,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroTypeWriterControlledPreview() {
    GymBroTheme {
        var currentIndex by remember { mutableStateOf(0) }
        var isPlaying by remember { mutableStateOf(true) }

        GymBroTypeWriterControlled(
            text = UiText.DynamicString("状态提升控制版本"),
            currentIndex = currentIndex,
            onIndexChange = { currentIndex = it },
            isPlaying = isPlaying,
            onPlayingChange = { isPlaying = it },
            textColor = Color.Blue,
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroTypeWriterSpeedVariantsPreview() {
    GymBroTheme {
        Column(
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            GymBroTypeWriterFast(
                text = UiText.DynamicString("快速打字效果"),
                textColor = Color.Green,
            )

            GymBroTypeWriter(
                text = UiText.DynamicString("标准打字效果"),
                textColor = Color.Blue,
            )

            GymBroTypeWriterSlow(
                text = UiText.DynamicString("慢速打字效果"),
                textColor = Color.Red,
            )
        }
    }
}

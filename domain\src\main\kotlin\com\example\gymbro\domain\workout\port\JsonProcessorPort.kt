package com.example.gymbro.domain.workout.port

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate

/**
 * JSON处理端口接口（Domain层定义，Data层实现）
 * 解决Data层直接处理JSON的职责越界问题
 *
 * 这个接口将JSON序列化/反序列化的操作抽象到Domain层，
 * 确保依赖关系符合Clean Architecture原则
 */
interface JsonProcessorPort {
    /**
     * 序列化Domain模型为JSON字符串
     *
     * @param template 要序列化的锻炼模板
     * @return JSON字符串或错误
     */
    suspend fun serializeTemplate(template: WorkoutTemplate): ModernResult<String>

    /**
     * 反序列化JSON字符串为Domain模型
     *
     * @param jsonString JSON字符串
     * @return Domain模型或错误
     */
    suspend fun deserializeTemplate(jsonString: String): ModernResult<WorkoutTemplate>

    /**
     * 验证JSON格式是否正确
     *
     * @param jsonString JSON字符串
     * @return 验证结果
     */
    suspend fun validateJson(jsonString: String): ModernResult<Boolean>

    /**
     * 格式化JSON字符串（美化输出）
     *
     * @param jsonString 原始JSON字符串
     * @return 格式化后的JSON字符串
     */
    suspend fun formatJson(jsonString: String): ModernResult<String>
}
package com.example.gymbro.designSystem.components

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.text.input.VisualTransformation
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.components.smart.*
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.GymBroTokenValidator
import com.example.gymbro.designSystem.theme.tokens.Tokens
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * GymBro通用输入字段基础组件 - SmartInput升级版
 *
 * 🚀 内核全面升级为SmartInput，保持接口100%向后兼容
 * 零迁移成本获得智能功能：自动保存、验证、动画、建议等
 *
 * 新增智能功能：
 * - 自动保存草稿到本地存储
 * - 智能场景识别和配置
 * - 实时验证和错误提示
 * - 流畅的焦点和状态动画
 * - 智能建议和自动完成
 * - 高性能动画按钮系统
 *
 * @param value 当前输入值
 * @param onValueChange 输入值变化回调
 * @param label 标签内容（Composable）
 * @param modifier Modifier修饰符，默认会添加fillMaxWidth
 * @param isError 是否显示错误状态
 * @param errorMessage 错误消息（UiText），优先级高于supportingText
 * @param visualTransformation 视觉变换（如密码遮罩）
 * @param keyboardOptions 键盘选项配置
 * @param keyboardActions 键盘动作配置
 * @param enabled 是否启用输入
 * @param maxLength 最大字符长度限制
 * @param prefix 前缀内容（Composable）
 * @param suffix 后缀内容（Composable）
 * @param supportingText 支持文本（Composable），错误时会被errorMessage覆盖
 * @param singleLine 是否单行输入
 * @param maxLines 最大行数（多行时使用）
 * @param smartConfig 智能配置（可选，自动推断）
 * @param suggestions 智能建议列表
 * @param onSuggestionClick 建议点击回调
 * @param showCharCount 是否显示字符计数
 * @param leadingIcon 前导图标
 * @param trailingIcon 尾随图标
 * @param placeholder 占位符文本
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun GymBroInputField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    label: (@Composable () -> Unit)? = null,
    placeholder: String = "",
    isError: Boolean = false,
    errorMessage: UiText? = null,
    visualTransformation: VisualTransformation = VisualTransformation.None,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    enabled: Boolean = true,
    maxLength: Int? = null,
    prefix: (@Composable (() -> Unit))? = null,
    suffix: (@Composable (() -> Unit))? = null,
    supportingText: (@Composable (() -> Unit))? = null,
    singleLine: Boolean = true,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    smartConfig: SmartInputConfig? = null,
    suggestions: List<String> = emptyList(),
    onSuggestionClick: ((String) -> Unit)? = null,
    showCharCount: Boolean = false,
    leadingIcon: (@Composable (() -> Unit))? = null,
    trailingIcon: (@Composable (() -> Unit))? = null,
) {
    // Token 使用验证 (仅在 DEBUG 模式下)
    GymBroTokenValidator.validateTokenUsage("GymBroInputField")
    // 🚀 智能配置推断：从现有参数自动推断最佳配置
    val finalConfig =
        smartConfig ?: SmartInputConfig
            .fromContext(
                placeholder = placeholder,
                keyboardType = keyboardOptions.keyboardType,
                singleLine = singleLine,
            ).let { baseConfig ->
                // 根据现有参数增强配置
                baseConfig.copy(
                    validation =
                    if (maxLength != null) {
                        SmartInputConfig.Validation(
                            rules = listOf(ValidationRule.MaxLength(maxLength)),
                        )
                    } else {
                        baseConfig.validation
                    },
                )
            }

    // 🚀 SmartInput状态管理
    val smartState = rememberSmartInputState(finalConfig)
    val keyboardController = LocalSoftwareKeyboardController.current

    // 🚀 文本流管理（用于自动保存）
    val textFlow = remember { MutableStateFlow(value) }
    LaunchedEffect(value) {
        textFlow.value = value
    }

    // 🚀 设置自动保存
    LaunchedEffect(smartState) {
        smartState.setupAutoSave(textFlow)
    }

    // 🚀 验证管理
    LaunchedEffect(value, isError) {
        if (!isError) {
            smartState.validateInput(value)
        }
    }

    // 🚀 错误处理：优先使用传入的错误，否则使用验证错误
    val finalIsError = isError || smartState.validationErrors.value.isNotEmpty()
    val finalErrorMessage =
        errorMessage ?: smartState.validationErrors.value.firstOrNull()?.let {
            UiText.DynamicString(it.message)
        }

    Column(modifier = modifier) {
        // 🚀 主输入字段
        OutlinedTextField(
            value = value,
            onValueChange = { newValue ->
                // 字符长度限制处理
                val finalValue =
                    if (maxLength != null && newValue.length > maxLength) {
                        newValue.take(maxLength)
                    } else {
                        newValue
                    }

                onValueChange(finalValue)

                // 实时验证
                if (finalConfig.validation?.showErrorsInstantly == true) {
                    smartState.validateInput(finalValue)
                }
            },
            label = label,
            placeholder =
            if (placeholder.isNotEmpty()) {
                { Text(placeholder) }
            } else {
                null
            },
            modifier =
            Modifier
                .fillMaxWidth()
                .onFocusChanged { focusState ->
                    smartState.setFocused(focusState.isFocused)
                },
            isError = finalIsError,
            visualTransformation = visualTransformation,
            keyboardOptions = keyboardOptions,
            keyboardActions = keyboardActions,
            enabled = enabled,
            singleLine = singleLine,
            maxLines = maxLines,
            prefix = prefix,
            suffix = suffix,
            leadingIcon = leadingIcon,
            trailingIcon =
            trailingIcon ?: run {
                // 🚀 智能尾随图标：动作按钮或原有trailingIcon
                finalConfig.actionButton?.let { actionButtonConfig ->
                    {
                        SmartActionButton(
                            config = actionButtonConfig,
                            currentText = value,
                            modifier = Modifier.padding(end = Tokens.Spacing.Small), // 8.dp - 使用 Token
                        )
                    }
                }
            },
            supportingText = {
                SmartSupportingText(
                    errorMessage = finalErrorMessage,
                    originalSupportingText = supportingText,
                    maxLength = maxLength,
                    currentLength = value.length,
                    showCharCount = showCharCount,
                    smartState = smartState,
                )
            },
        )

        // 🚀 智能建议下拉列表
        SmartSuggestionsList(
            suggestions = suggestions.ifEmpty { smartState.suggestions.value },
            visible = smartState.isFocused.value && (suggestions.isNotEmpty() || smartState.suggestions.value.isNotEmpty()),
            onSuggestionClick = { suggestion ->
                onSuggestionClick?.invoke(suggestion) ?: onValueChange(suggestion)
                keyboardController?.hide()
            },
        )
    }
}

// === 辅助Composable组件 ===

@Composable
private fun SmartSupportingText(
    errorMessage: UiText?,
    originalSupportingText: (@Composable (() -> Unit))?,
    maxLength: Int?,
    currentLength: Int,
    showCharCount: Boolean,
    smartState: SmartInputState,
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        // 错误消息或原始支持文本
        Box(modifier = Modifier.weight(1f)) {
            when {
                errorMessage != null -> {
                    Text(
                        text = errorMessage.asString(),
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.error,
                    )
                }
                originalSupportingText != null -> {
                    originalSupportingText()
                }
            }
        }

        // 字符计数
        if (showCharCount && maxLength != null) {
            Text(
                text = "$currentLength/$maxLength",
                style = MaterialTheme.typography.bodySmall,
                color =
                if (currentLength > maxLength * 0.9) {
                    MaterialTheme.colorScheme.error
                } else {
                    MaterialTheme.colorScheme.onSurfaceVariant
                },
            )
        }
    }
}

@Composable
private fun SmartSuggestionsList(
    suggestions: List<String>,
    visible: Boolean,
    onSuggestionClick: (String) -> Unit,
) {
    if (visible && suggestions.isNotEmpty()) {
        Card(
            modifier =
            Modifier
                .fillMaxWidth()
                .padding(top = Tokens.Spacing.XSmall),
            // 4.dp - 使用 Token
            elevation = CardDefaults.cardElevation(
                defaultElevation = Tokens.Card.Elevation,
            ), // 4.dp - 使用 Token
        ) {
            Column {
                suggestions.take(5).forEach { suggestion ->
                    Text(
                        text = suggestion,
                        modifier =
                        Modifier
                            .fillMaxWidth()
                            .clickable { onSuggestionClick(suggestion) } // 🔥 修复：添加点击处理
                            .padding(Tokens.Spacing.Small + Tokens.Spacing.XSmall),
                        // 12.dp - 使用 Token 组合
                        style = MaterialTheme.typography.bodyMedium,
                    )
                }
            }
        }
    }
}

// === Preview组件 ===

@GymBroPreview
@Composable
private fun GymBroInputFieldPreview() {
    GymBroTheme {
        var text by remember { mutableStateOf("") }

        GymBroInputField(
            value = text,
            onValueChange = { text = it },
            label = { Text("用户名") },
            placeholder = "请输入用户名",
            modifier = Modifier.padding(Tokens.Spacing.Medium), // 16.dp - 使用 Token
        )
    }
}

@GymBroPreview
@Composable
private fun GymBroInputFieldWithSmartConfigPreview() {
    GymBroTheme {
        var text by remember { mutableStateOf("") }

        GymBroInputField(
            value = text,
            onValueChange = { text = it },
            label = { Text("聊天消息") },
            placeholder = "有什么想问的？",
            singleLine = false,
            maxLines = 4,
            smartConfig =
            SmartInputConfig(
                autoSave = SmartInputConfig.AutoSave("preview_chat"),
                actionButton =
                SmartInputConfig.ActionButton.Send(
                    onClick = { /* 发送消息 */ },
                ),
            ),
            modifier = Modifier.padding(Tokens.Spacing.Medium), // 16.dp - 使用 Token
        )
    }
}

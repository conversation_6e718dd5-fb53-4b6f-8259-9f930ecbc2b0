package com.example.gymbro.domain.workout.usecase

import com.example.gymbro.shared.models.workout.PlanProgressStatus
import com.example.gymbro.shared.models.workout.WorkoutPlan
import kotlinx.coroutines.flow.Flow

/**
 * Use case for managing workout plan progress
 *
 * Handles the business logic for updating and tracking progress of workout plans
 * and individual days within the plans.
 */
interface PlanProgressUseCase {

    /**
     * Sets the progress status for a specific day in a workout plan
     *
     * @param planId The ID of the workout plan
     * @param dayNumber The day number (starting from 1)
     * @param status The new progress status to set
     * @return Result indicating success or failure
     */
    suspend fun setDayProgress(
        planId: String,
        dayNumber: Int,
        status: PlanProgressStatus,
    ): Result<Unit>

    /**
     * Toggles the completion status of a specific day in a workout plan
     *
     * If the day is NOT_STARTED or IN_PROGRESS, it will be marked as COMPLETED.
     * If the day is COMPLETED, it will be marked as NOT_STARTED.
     *
     * @param planId The ID of the workout plan
     * @param dayNumber The day number (starting from 1)
     * @return Result indicating success or failure
     */
    suspend fun toggleCompleted(
        planId: String,
        dayNumber: Int,
    ): Result<Unit>

    /**
     * Observes changes to a workout plan including progress updates
     *
     * @param planId The ID of the workout plan to observe
     * @return Flow emitting the updated WorkoutPlan whenever changes occur
     */
    fun observePlan(planId: String): Flow<WorkoutPlan>

    /**
     * Gets the current progress status for a specific day
     *
     * @param planId The ID of the workout plan
     * @param dayNumber The day number (starting from 1)
     * @return Result containing the current progress status or error
     */
    suspend fun getDayProgress(
        planId: String,
        dayNumber: Int,
    ): Result<PlanProgressStatus>

    /**
     * Sets progress for multiple days at once (bulk update)
     *
     * @param planId The ID of the workout plan
     * @param progressMap Map of day number to progress status
     * @return Result indicating success or failure
     */
    suspend fun setBulkProgress(
        planId: String,
        progressMap: Map<Int, PlanProgressStatus>,
    ): Result<Unit>

    /**
     * Marks all days in a plan with a specific status
     *
     * @param planId The ID of the workout plan
     * @param status The progress status to set for all days
     * @return Result indicating success or failure
     */
    suspend fun setAllDaysProgress(
        planId: String,
        status: PlanProgressStatus,
    ): Result<Unit>

    /**
     * Gets progress statistics for a workout plan
     *
     * @param planId The ID of the workout plan
     * @return Result containing progress statistics (completed, in progress, not started counts)
     */
    suspend fun getPlanProgressStats(planId: String): Result<PlanProgressStats>
}

/**
 * Data class representing progress statistics for a workout plan
 */
data class PlanProgressStats(
    val totalDays: Int,
    val completedDays: Int,
    val inProgressDays: Int,
    val notStartedDays: Int,
    val completionPercentage: Float,
)

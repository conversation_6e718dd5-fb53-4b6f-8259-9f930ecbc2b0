package com.example.gymbro.data.coach.integration

import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.example.gymbro.core.error.ModernResult
import com.example.gymbro.data.coach.dao.ConversationMetaDao
import com.example.gymbro.data.coach.dao.MessageEventDao
import com.example.gymbro.data.coach.repository.HistoryPersisterImpl
import com.example.gymbro.data.local.database.AppDatabase
import com.example.gymbro.domain.coach.model.CoachMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * 历史保存功能集成测试
 *
 * 测试706任务保存.md规格的完整流程：
 * - 真实数据库操作
 * - ConversationMeta和MessageEvent的关联
 * - 事务一致性
 * - 数据完整性验证
 */
@OptIn(ExperimentalCoroutinesApi::class)
@RunWith(AndroidJUnit4::class)
class HistorySavingIntegrationTest {

    private lateinit var database: AppDatabase
    private lateinit var conversationMetaDao: ConversationMetaDao
    private lateinit var messageEventDao: MessageEventDao
    private lateinit var historyPersister: HistoryPersisterImpl
    private lateinit var testDispatcher: TestDispatcher

    @Before
    fun setup() {
        testDispatcher = StandardTestDispatcher()
        Dispatchers.setMain(testDispatcher)

        // 创建内存数据库
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            AppDatabase::class.java,
        ).allowMainThreadQueries().build()

        conversationMetaDao = database.conversationMetaDao()
        messageEventDao = database.messageEventDao()

        historyPersister = HistoryPersisterImpl(
            conversationMetaDao = conversationMetaDao,
            messageEventDao = messageEventDao,
            ioDispatcher = testDispatcher,
        )
    }

    @After
    fun tearDown() {
        database.close()
        Dispatchers.resetMain()
    }

    @Test
    fun `complete conversation flow should work correctly`() = runTest {
        // Given
        val conversationId = "integration_test_conversation"
        val userId = "integration_test_user"

        val userMessage = CoachMessage.UserMessage(
            id = "user_msg_1",
            content = "What is the best exercise for building muscle?",
            timestamp = System.currentTimeMillis(),
        )

        val aiMessage = CoachMessage.AiMessage(
            id = "ai_msg_1",
            content = "Compound exercises like squats, deadlifts, and bench press are excellent for building muscle.",
            timestamp = System.currentTimeMillis() + 1000,
            tokenCount = 75,
            finalMarkdown = "**Compound exercises** like *squats*, *deadlifts*, and *bench press* are excellent for building muscle.",
            thinkingNodes = "User is asking about muscle building. I should recommend compound movements.",
        )

        // When - Save user message
        val userResult = historyPersister.persistMessage(conversationId, userMessage, userId)

        // Then - Verify user message saved
        assertTrue(userResult is ModernResult.Success)

        // Verify conversation meta was created
        val conversationMeta = conversationMetaDao.getConversationMeta(conversationId)
        assertNotNull(conversationMeta)
        assertEquals(conversationId, conversationMeta.conversationId)
        assertEquals(userId, conversationMeta.userId)
        assertEquals(1, conversationMeta.messageCount)

        // Verify user message event was saved
        val userMessageEvent = messageEventDao.getMessageEvent(userMessage.id)
        assertNotNull(userMessageEvent)
        assertEquals(userMessage.content, userMessageEvent.content)
        assertEquals("user", userMessageEvent.role)
        assertEquals(1L, userMessageEvent.sequence)

        // When - Save AI message
        val aiResult = historyPersister.persistMessage(conversationId, aiMessage, userId)

        // Then - Verify AI message saved
        assertTrue(aiResult is ModernResult.Success)

        // Verify conversation meta was updated
        val updatedMeta = conversationMetaDao.getConversationMeta(conversationId)
        assertNotNull(updatedMeta)
        assertEquals(2, updatedMeta.messageCount)
        assertTrue(updatedMeta.lastActiveAt > conversationMeta.lastActiveAt)

        // Verify AI message event was saved
        val aiMessageEvent = messageEventDao.getMessageEvent(aiMessage.id)
        assertNotNull(aiMessageEvent)
        assertEquals(aiMessage.content, aiMessageEvent.content)
        assertEquals("assistant", aiMessageEvent.role)
        assertEquals(2L, aiMessageEvent.sequence)
        assertEquals(aiMessage.finalMarkdown, aiMessageEvent.finalMarkdown)
        assertEquals(aiMessage.thinkingNodes, aiMessageEvent.thinkingNodes)
        assertEquals(aiMessage.tokenCount, aiMessageEvent.tokenCount)

        // Verify conversation messages can be retrieved
        val allMessages = messageEventDao.getConversationMessages(conversationId)
        assertEquals(2, allMessages.size)
        assertEquals(userMessage.id, allMessages[0].eventId)
        assertEquals(aiMessage.id, allMessages[1].eventId)
    }

    @Test
    fun `batch message saving should maintain consistency`() = runTest {
        // Given
        val conversationId = "batch_test_conversation"
        val userId = "batch_test_user"

        val messages = listOf(
            CoachMessage.UserMessage("batch_user_1", "First user message", System.currentTimeMillis()),
            CoachMessage.AiMessage(
                "batch_ai_1",
                "First AI response",
                System.currentTimeMillis() + 1000,
                50,
                "**First AI response**",
                null,
            ),
            CoachMessage.UserMessage(
                "batch_user_2",
                "Second user message",
                System.currentTimeMillis() + 2000,
            ),
            CoachMessage.AiMessage(
                "batch_ai_2",
                "Second AI response",
                System.currentTimeMillis() + 3000,
                60,
                "**Second AI response**",
                "Thinking about the follow-up",
            ),
        )

        // When
        val result = historyPersister.persistMessages(conversationId, messages, userId)

        // Then
        assertTrue(result is ModernResult.Success)

        // Verify conversation meta
        val conversationMeta = conversationMetaDao.getConversationMeta(conversationId)
        assertNotNull(conversationMeta)
        assertEquals(4, conversationMeta.messageCount)

        // Verify all messages were saved with correct sequence
        val savedMessages = messageEventDao.getConversationMessages(conversationId)
        assertEquals(4, savedMessages.size)

        // Verify sequence order
        savedMessages.forEachIndexed { index, messageEvent ->
            assertEquals((index + 1).toLong(), messageEvent.sequence)
            assertEquals(messages[index].id, messageEvent.eventId)
        }

        // Verify role alternation
        assertEquals("user", savedMessages[0].role)
        assertEquals("assistant", savedMessages[1].role)
        assertEquals("user", savedMessages[2].role)
        assertEquals("assistant", savedMessages[3].role)
    }

    @Test
    fun `sync status tracking should work correctly`() = runTest {
        // Given
        val conversationId = "sync_test_conversation"
        val userId = "sync_test_user"

        val message = CoachMessage.UserMessage(
            id = "sync_test_message",
            content = "Test message for sync",
            timestamp = System.currentTimeMillis(),
        )

        // When - Save message
        historyPersister.persistMessage(conversationId, message, userId)

        // Then - Verify message is unsynced initially
        val unsyncedMessages = historyPersister.getUnsyncedMessages(10)
        assertTrue(unsyncedMessages is ModernResult.Success)
        assertEquals(1, unsyncedMessages.data.size)
        assertEquals(message.id, unsyncedMessages.data[0].id)

        val unsyncedConversations = historyPersister.getUnsyncedConversations(10)
        assertTrue(unsyncedConversations is ModernResult.Success)
        assertEquals(1, unsyncedConversations.data.size)
        assertEquals(conversationId, unsyncedConversations.data[0])

        // When - Mark as synced
        historyPersister.markMessagesSynced(listOf(message.id))
        historyPersister.markConversationSynced(conversationId)

        // Then - Verify no unsynced items
        val unsyncedAfter = historyPersister.getUnsyncedMessages(10)
        assertTrue(unsyncedAfter is ModernResult.Success)
        assertEquals(0, unsyncedAfter.data.size)

        val unsyncedConversationsAfter = historyPersister.getUnsyncedConversations(10)
        assertTrue(unsyncedConversationsAfter is ModernResult.Success)
        assertEquals(0, unsyncedConversationsAfter.data.size)
    }

    @Test
    fun `conversation title and summary updates should work`() = runTest {
        // Given
        val conversationId = "title_test_conversation"
        val userId = "title_test_user"

        val message = CoachMessage.UserMessage(
            id = "title_test_message",
            content = "Initial message",
            timestamp = System.currentTimeMillis(),
        )

        // When - Save initial message
        historyPersister.persistMessage(conversationId, message, userId)

        // Then - Verify default title
        val initialMeta = conversationMetaDao.getConversationMeta(conversationId)
        assertNotNull(initialMeta)
        assertEquals("新对话", initialMeta.title)

        // When - Update title and summary
        val newTitle = "Fitness Discussion"
        val newSummary = "A conversation about fitness and exercise recommendations"

        historyPersister.updateConversationTitle(conversationId, newTitle)
        historyPersister.updateConversationSummary(conversationId, newSummary)

        // Then - Verify updates
        val updatedMeta = conversationMetaDao.getConversationMeta(conversationId)
        assertNotNull(updatedMeta)
        assertEquals(newTitle, updatedMeta.title)
        assertEquals(newSummary, updatedMeta.summary)
    }

    @Test
    fun `persistence statistics should be accurate`() = runTest {
        // Given
        val userId = "stats_test_user"
        val conversationId1 = "stats_conversation_1"
        val conversationId2 = "stats_conversation_2"

        // Create multiple conversations and messages
        val messages1 = listOf(
            CoachMessage.UserMessage("stats_user_1", "Message 1", System.currentTimeMillis()),
            CoachMessage.AiMessage(
                "stats_ai_1",
                "Response 1",
                System.currentTimeMillis() + 1000,
                30,
                "**Response 1**",
                null,
            ),
        )

        val messages2 = listOf(
            CoachMessage.UserMessage("stats_user_2", "Message 2", System.currentTimeMillis()),
            CoachMessage.AiMessage(
                "stats_ai_2",
                "Response 2",
                System.currentTimeMillis() + 1000,
                40,
                "**Response 2**",
                null,
            ),
            CoachMessage.UserMessage("stats_user_3", "Message 3", System.currentTimeMillis() + 2000),
        )

        // When
        historyPersister.persistMessages(conversationId1, messages1, userId)
        historyPersister.persistMessages(conversationId2, messages2, userId)

        // Then
        val stats = historyPersister.getPersistenceStats(userId)
        assertTrue(stats is ModernResult.Success)

        val statsData = stats.data
        assertEquals(2, statsData.totalConversations)
        assertEquals(5, statsData.totalMessages)
        assertEquals(5, statsData.unsyncedMessages) // All messages are unsynced initially
        assertEquals(2, statsData.unsyncedConversations) // All conversations are unsynced initially
    }
}

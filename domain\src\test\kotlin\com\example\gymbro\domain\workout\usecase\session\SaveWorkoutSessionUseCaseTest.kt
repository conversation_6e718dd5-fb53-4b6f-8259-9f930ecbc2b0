package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.workout.model.SessionExercise
import com.example.gymbro.domain.workout.model.SessionSet
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * SaveWorkoutSessionUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试UseCase的核心业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证ModernResult返回类型
 * ✔️ 测试多种参数类型和场景
 */
class SaveWorkoutSessionUseCaseTest {

    @MockK
    private lateinit var sessionRepository: SessionRepository

    @MockK
    private lateinit var logger: Logger

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: SaveWorkoutSessionUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = SaveWorkoutSessionUseCase(
            sessionRepository = sessionRepository,
            dispatcher = testDispatcher,
            logger = logger,
        )
    }

    // === SaveSession Tests ===

    @Test
    fun `execute with SaveSession should save session successfully`() = runTest(testDispatcher) {
        // Given
        val session = createSampleWorkoutSession()
        val params = SaveWorkoutSessionUseCase.Params.SaveSession(session)
        val expectedSessionId = "saved-session-123"

        coEvery { sessionRepository.saveSession(session) } returns ModernResult.Success(expectedSessionId)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(expectedSessionId, result.data)
        coVerify(exactly = 1) { sessionRepository.saveSession(session) }
    }

    @Test
    fun `execute with SaveSession should handle repository error`() = runTest(testDispatcher) {
        // Given
        val session = createSampleWorkoutSession()
        val params = SaveWorkoutSessionUseCase.Params.SaveSession(session)

        coEvery { sessionRepository.saveSession(session) } returns ModernResult.Error(mockk<DataErrors.DataError>())

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { sessionRepository.saveSession(session) }
    }

    // === RecordSet Tests ===

    @Test
    fun `execute with RecordSet should record new exercise set successfully`() = runTest(testDispatcher) {
        // Given
        val sessionId = "session-123"
        val exerciseId = "exercise-456"
        val exerciseSet = createSampleSessionSet()
        val params = SaveWorkoutSessionUseCase.Params.RecordSet(sessionId, exerciseId, exerciseSet)

        val existingSession = createInProgressSession(sessionId)
        val expectedSessionId = "saved-session-123"

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(existingSession)
        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Success(expectedSessionId)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(expectedSessionId, result.data)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with RecordSet should update existing exercise set successfully`() = runTest(
        testDispatcher,
    ) {
        // Given
        val sessionId = "session-123"
        val exerciseId = "exercise-456"
        val exerciseSet = createSampleSessionSet(setNumber = 1)
        val params = SaveWorkoutSessionUseCase.Params.RecordSet(sessionId, exerciseId, exerciseSet)

        val existingSession = createInProgressSessionWithExercise(sessionId, exerciseId)
        val expectedSessionId = "saved-session-123"

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(existingSession)
        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Success(expectedSessionId)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(expectedSessionId, result.data)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with RecordSet should return error when session not found`() = runTest(testDispatcher) {
        // Given
        val sessionId = "nonexistent-session"
        val exerciseId = "exercise-456"
        val exerciseSet = createSampleSessionSet()
        val params = SaveWorkoutSessionUseCase.Params.RecordSet(sessionId, exerciseId, exerciseSet)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(null)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is DataErrors.Validation)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with RecordSet should return error when session not in progress`() = runTest(
        testDispatcher,
    ) {
        // Given
        val sessionId = "session-123"
        val exerciseId = "exercise-456"
        val exerciseSet = createSampleSessionSet()
        val params = SaveWorkoutSessionUseCase.Params.RecordSet(sessionId, exerciseId, exerciseSet)

        val completedSession = createSampleWorkoutSession(id = sessionId, status = "COMPLETED")

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(completedSession)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is DataErrors.Validation)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with RecordSet should return error when repository get fails`() = runTest(testDispatcher) {
        // Given
        val sessionId = "session-123"
        val exerciseId = "exercise-456"
        val exerciseSet = createSampleSessionSet()
        val params = SaveWorkoutSessionUseCase.Params.RecordSet(sessionId, exerciseId, exerciseSet)

        coEvery {
            sessionRepository.getSessionById(sessionId)
        } returns ModernResult.Error(mockk<DataErrors.DataError>())

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    // === Compatibility Methods Tests ===

    @Test
    fun `invoke with WorkoutSession should delegate to SaveSession params`() = runTest(testDispatcher) {
        // Given
        val session = createSampleWorkoutSession()
        val expectedSessionId = "saved-session-123"

        coEvery { sessionRepository.saveSession(session) } returns ModernResult.Success(expectedSessionId)

        // When
        val result = useCase.invoke(session)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(expectedSessionId, result.data)
        coVerify(exactly = 1) { sessionRepository.saveSession(session) }
    }

    @Test
    fun `invoke with sessionId and exerciseSet should delegate to RecordSet params`() = runTest(
        testDispatcher,
    ) {
        // Given
        val sessionId = "session-123"
        val exerciseId = "exercise-456"
        val exerciseSet = createSampleSessionSet()

        val existingSession = createInProgressSession(sessionId)
        val expectedSessionId = "saved-session-123"

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(existingSession)
        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Success(expectedSessionId)

        // When
        val result = useCase.invoke(sessionId, exerciseId, exerciseSet)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(expectedSessionId, result.data)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    // === 辅助方法 ===

    private fun createSampleWorkoutSession(
        id: String = "session-123",
        status: String = "DRAFT",
    ): WorkoutSession {
        return WorkoutSession(
            id = id,
            name = "测试训练会话",
            templateId = null,
            status = status,
            startTime = if (status == "IN_PROGRESS") System.currentTimeMillis() else null,
            endTime = if (status == "COMPLETED") System.currentTimeMillis() else null,
            exercises = emptyList(),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
        )
    }

    private fun createInProgressSession(sessionId: String): WorkoutSession {
        return createSampleWorkoutSession(id = sessionId, status = "IN_PROGRESS")
    }

    private fun createInProgressSessionWithExercise(sessionId: String, exerciseId: String): WorkoutSession {
        val exercise = SessionExercise(
            id = "session-exercise-123",
            sessionId = sessionId,
            exerciseId = exerciseId,
            order = 0,
            name = "测试动作",
            targetSets = 3,
            status = "IN_PROGRESS",
            sets = listOf(createSampleSessionSet(setNumber = 1)),
            startTime = System.currentTimeMillis(),
            endTime = null,
            notes = null,
        )

        return createSampleWorkoutSession(id = sessionId, status = "IN_PROGRESS").copy(
            exercises = listOf(exercise),
        )
    }

    private fun createSampleSessionSet(setNumber: Int = 1): SessionSet {
        return SessionSet(
            id = "set-123",
            sessionId = "session-123",
            exerciseId = "exercise-456",
            setNumber = setNumber,
            weight = 100.0,
            reps = 10,
            restDuration = 60,
            isCompleted = true,
            completedAt = System.currentTimeMillis(),
            rpe = 8,
            notes = null,
        )
    }
}
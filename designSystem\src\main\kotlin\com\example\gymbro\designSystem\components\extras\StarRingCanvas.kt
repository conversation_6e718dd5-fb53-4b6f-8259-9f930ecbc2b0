package com.example.gymbro.designSystem.components.extras

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.*
import kotlinx.coroutines.delay
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.cos
import kotlin.math.sin

/**
 * Data class representing an orbiting line around the black hole in the star-ring animation.
 * Each line has properties that define its appearance and behavior in the animation.
 *
 * @param positionAngle Starting angle in degrees (0-360) where the line appears on the orbit.
 * @param orbitalRadius Distance from the center of the black hole to the orbit path.
 * @param length Length of the line in pixels.
 * @param thickness Thickness/width of the line in pixels.
 * @param color Color of the line.
 * @param opacity Opacity of the line (0.0-1.0, where 1.0 is fully opaque).
 * @param animationPhaseOffset Phase offset for animation timing, allowing lines to move independently.
 */
data class OrbitingLine(
    val positionAngle: Float, // Starting angle in degrees
    val orbitalRadius: Float, // Distance from center
    val length: Float, // Line length in pixels
    val thickness: Float, // Line thickness
    val color: Color, // Line color
    val opacity: Float, // Line opacity (0-1)
    val animationPhaseOffset: Float, // Animation phase offset
)

/**
 * Data class to configure the orbiting line system for the star-ring animation. This allows customization of
 * line properties including animation speed and optional elliptical orbits.
 *
 * @param lines List of `OrbitingLine` describing each orbiting line's properties.
 * @param animationSpeed Float value to control the speed of line animations.
 * @param enableEllipticalOrbits Boolean flag to enable elliptical orbits.
 * @param ellipticalRatio Float value to adjust the elliptical ratio.
 */
data class OrbitingLineConfig(
    val lines: List<OrbitingLine>,
    val animationSpeed: Float = 1.0f,
    val enableEllipticalOrbits: Boolean = false,
    val ellipticalRatio: Float = 1.0f, // 1.0 = circular, <1 = horizontal ellipse, >1 = vertical ellipse
)

/**
 * Data class for controlling the animation state of the star-ring canvas.
 * This allows external control over animation parameters.
 *
 * @param isAnimating Boolean flag to control whether animations are running.
 * @param rotation Current rotation angle of the animation.
 * @param shimmer Current shimmer effect value for particle animations.
 */
data class AnimationState(
    val isAnimating: Boolean = true,
    val rotation: Float = 0f,
    val shimmer: Float = 0f,
)

/**
 * Creates and remembers an [AnimationState] instance for the star-ring animation.
 * This function ensures the animation state persists across recompositions.
 *
 * @param isAnimating Initial animation state (default: true).
 * @param initialRotation Initial rotation angle (default: 0f).
 * @param initialShimmer Initial shimmer value (default: 0f).
 * @return Remembered [AnimationState] instance.
 */
@Composable
fun rememberAnimationState(
    isAnimating: Boolean = true,
    initialRotation: Float = 0f,
    initialShimmer: Float = 0f,
): AnimationState {
    return remember {
        AnimationState(
            isAnimating = isAnimating,
            rotation = initialRotation,
            shimmer = initialShimmer,
        )
    }
}

/**
 * 完全自定义参数预览
 * 展示所有可配置参数的效果
 */
@GymBroPreview
@Composable
fun StarRingCanvasFullyConfigurablePreview() {
    GymBroTheme {
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            blackHoleSizeRatio = 0.35f,
            gradientColors = listOf(
                Color(0xFF1A0033), // Deep purple
                Color(0xFF330066), // Purple
                Color(0xFF660099), // Light purple
            ),
            numberOfLines = 12,
            animationSpeed = 2.0f,
            animationDirection = -1.0f, // Reverse direction
            lineThickness = 3.5f,
            lineColor = Color(0xFFFFD700), // Gold
            lineGlowIntensity = 0.8f,
            enableAnimation = true,
            shadowIntensity = 0.85f,
            gradientStops = 16,
            coreColor = Color(0xFF1A0033),
            enableEventHorizon = true,
        )
    }
}

/**
 * 设备性能等级枚举
 */
private enum class DevicePerformance {
    LOW, // 低端设备：减少粒子数量
    MEDIUM, // 中端设备：标准粒子数量
    HIGH, // 高端设备：完整粒子数量
}

/**
 * A Composable function that renders a star-ring themed animated canvas with performance optimizations.
 * This component adjusts the number of particles and animation complexity based on device performance.
 *
 * @param modifier Modifier to apply to the canvas.
 * @param blackHoleSizeRatio Ratio for determining the size of the black hole in the canvas.
 * @param gradientColors A list of colors to create the gradient in animation.
 * @param numberOfLines Number of orbital lines around the black hole.
 * @param animationSpeed Speed for orbit animation.
 * @param enableAnimation Flag to enable/disable the animation.
 * @param shadowIntensity Intensity for shadows within the animation.
 * @param gradientStops Number of stops for color gradient transitions.
 * @param coreColor Core color for the black hole visual.
 * @param enableEventHorizon Flag to render the event horizon.
 * @param orbitingLineConfig Configuration for custom orbiting lines, if needed.
 * @param animationState State to remember the progression of animation.
 *
 *### Performance Optimization Features:
 * - Device performance detection and adaptive particle count.
 * - Optimized trigonometric calculations (pre-calculation and caching).
 * - Minimized object allocation and GC pressure.
 * - Optional performance monitoring mode.
 *
 *### Usage Example:
 *```
 *@Composable
 *fun PreviewStarRingCanvas() {
 *    StarRingCanvas(
 *        modifier = Modifier.fillMaxSize(),
 *        blackHoleSizeRatio = 0.35f,
 *        gradientColors = listOf(Color.Black, Color.Gray),
 *        numberOfLines = 12,
 *        enableAnimation = true,
 *    )
 *}
 *```
 */
@Composable
fun StarRingCanvas(
    modifier: Modifier = Modifier,
    canvasSize: Size = Size.Unspecified,
    blackHoleSizeRatio: Float = 0.32f,
    gradientColors: List<Color> = listOf(Color.Black, Color.Gray),
    numberOfLines: Int = 8,
    animationSpeed: Float = 1.0f,
    animationDirection: Float = 1.0f,
    lineThickness: Float = 2.0f,
    lineColor: Color = Color.White,
    lineGlowIntensity: Float = 0.5f,
    enableAnimation: Boolean = true,
    enablePerformanceMonitoring: Boolean = false,
    shadowIntensity: Float = 0.7f,
    gradientStops: Int = 10,
    coreColor: Color = Color.Black,
    enableEventHorizon: Boolean = true,
    orbitingLineConfig: OrbitingLineConfig? = null,
    animationState: AnimationState = rememberAnimationState(),
) {
    val spec = currentStarRingSpec()
    val configuration = LocalConfiguration.current
    val density = LocalDensity.current

    // 设备性能检测 - 使用derivedStateOf避免不必要的重计算
    val devicePerformance by remember {
        derivedStateOf {
            detectDevicePerformance(configuration.screenWidthDp, configuration.screenHeightDp)
        }
    }

    // 根据设备性能调整参数 - 缓存配置对象
    val performanceConfig by remember(devicePerformance) {
        derivedStateOf {
            getPerformanceConfig(devicePerformance)
        }
    }

    // 🎯 重组优化：添加帧率限制，减少不必要的重组
    var lastFrameTime by remember { mutableStateOf(0L) }
    val frameInterval = remember { 1000L / 60L } // 60fps限制

    // 星环旋转动画 - 根据性能调整帧率
    val rotation by if (enableAnimation) {
        rememberInfiniteTransition(label = "star_ring_rotation").animateFloat(
            initialValue = 0f,
            targetValue = 360f,
            animationSpec = infiniteRepeatable(
                animation = tween(
                    durationMillis = spec.rotationPeriodMs,
                    easing = LinearEasing,
                ),
                repeatMode = RepeatMode.Restart,
            ),
            label = "rotation",
        )
    } else {
        remember { mutableStateOf(0f) }
    }

    // 粒子闪烁动画 - 优化动画周期
    val shimmer by if (enableAnimation) {
        rememberInfiniteTransition(label = "particle_shimmer").animateFloat(
            initialValue = 0f,
            targetValue = 2f * PI.toFloat(),
            animationSpec = infiniteRepeatable(
                animation = tween(
                    durationMillis = performanceConfig.shimmerDuration,
                    easing = LinearEasing,
                ),
                repeatMode = RepeatMode.Restart,
            ),
            label = "shimmer",
        )
    } else {
        remember { mutableStateOf(0f) }
    }

    // 性能监控（可选）
    if (enablePerformanceMonitoring) {
        LaunchedEffect(Unit) {
            // 这里可以添加性能监控逻辑
            // 例如：监控帧率、内存使用等
        }
    }

    // 🎯 重组优化：使用LaunchedEffect控制绘制频率
    var shouldDraw by remember { mutableStateOf(true) }

    LaunchedEffect(enableAnimation) {
        if (enableAnimation) {
            while (true) {
                val currentTime = System.currentTimeMillis()
                if (currentTime - lastFrameTime >= frameInterval) {
                    shouldDraw = !shouldDraw // 触发重组
                    lastFrameTime = currentTime
                }
                delay(frameInterval) // 60fps限制
            }
        }
    }

    // Pre-calculate colors outside of Canvas to avoid reallocation
    val grayscaleColors = remember {
        listOf(
            Color(0xFF000000), // 0 - 纯黑
            Color(0xFF1A1A1A), // 1 - 极深灰
            Color(0xFF333333), // 2 - 深灰
            Color(0xFF4D4D4D), // 3 - 中深灰
            Color(0xFF666666), // 4 - 中等灰
            Color(0xFF808080), // 5 - 标准灰
            Color(0xFF999999), // 6 - 中浅灰
            Color(0xFFB3B3B3), // 7 - 浅灰
            Color(0xFFCCCCCC), // 8 - 很浅灰
            Color(0xFFE6E6E6), // 9 - 极浅灰
            Color(0xFFF0F0F0), // 10 - 几乎白
            Color(0xFFF8F8F8), // 11 - 接近白
            Color(0xFFFFFFFF), // 12 - 纯白
        )
    }

    // 点缀色：使用GymBro主题色系
    val accentPink = remember { Color(0xFF3F6CF3) } // GymBro primary blue
    val accentOrange = remember { Color(0xFF00BC80) } // GymBro accent green

    Canvas(modifier = modifier) {
        val blackHoleRadius = size.minDimension * blackHoleSizeRatio

        // Use provided config or create default orbiting lines configuration
        val finalOrbitingLineConfig = orbitingLineConfig ?: OrbitingLineConfig(
            lines = List(numberOfLines) {
                OrbitingLine(
                    positionAngle = (360f / numberOfLines) * it,
                    orbitalRadius = blackHoleRadius * 1.2f,
                    length = 60f,
                    thickness = lineThickness,
                    color = lineColor,
                    opacity = 0.8f,
                    animationPhaseOffset = 0f,
                )
            },
            animationSpeed = animationSpeed,
            enableEllipticalOrbits = false,
            ellipticalRatio = 1.0f,
        )

        drawOptimizedStarField(
            centerX = size.width / 2,
            centerY = size.height / 2,
            rotation = rotation,
            shimmer = shimmer,
            spec = spec,
            config = performanceConfig,
            blackHoleSizeRatio = blackHoleSizeRatio,
            shadowIntensity = shadowIntensity,
            gradientStops = gradientStops,
            gradientColors = gradientColors,
            coreColor = coreColor,
            enableEventHorizon = enableEventHorizon,
            orbitingLineConfig = finalOrbitingLineConfig,
            numberOfLines = numberOfLines,
            animationSpeed = animationSpeed,
            animationDirection = animationDirection,
            lineThickness = lineThickness,
            lineColor = lineColor,
            lineGlowIntensity = lineGlowIntensity,
            grayscaleColors = grayscaleColors,
            accentPink = accentPink,
            accentOrange = accentOrange,
        )
    }
}

/**
 * Data class defining performance configurations such as layer count, particle count and shimmer duration.
 * Adjusts rendering features based on device capabilities.
 *
 * @param layerCount Total layers for rendering effects.
 * @param baseParticleCount Base count for particles.
 * @param particleIncrement Incremental count for additional particles.
 * @param shimmerDuration Duration in milliseconds for shimmer effects.
 * @param enableGradients Boolean to enable gradient effects.
 * @param enableBackgroundStars Boolean for rendering background stars.
 */
private data class PerformanceConfig(
    val layerCount: Int,
    val baseParticleCount: Int,
    val particleIncrement: Int,
    val shimmerDuration: Int,
    val enableGradients: Boolean,
    val enableBackgroundStars: Boolean,
)

/**
 * 检测设备性能等级
 */
private fun detectDevicePerformance(screenWidthDp: Int, screenHeightDp: Int): DevicePerformance {
    val totalPixels = screenWidthDp * screenHeightDp

    return when {
        totalPixels < 800 * 1280 -> DevicePerformance.LOW // 小于HD
        totalPixels < 1080 * 1920 -> DevicePerformance.MEDIUM // HD到FHD
        else -> DevicePerformance.HIGH // FHD及以上
    }
}

/**
 * 根据设备性能获取配置
 */
private fun getPerformanceConfig(performance: DevicePerformance): PerformanceConfig {
    return when (performance) {
        DevicePerformance.LOW -> PerformanceConfig(
            layerCount = 4,
            baseParticleCount = 20,
            particleIncrement = 6,
            shimmerDuration = 4000,
            enableGradients = false,
            enableBackgroundStars = false,
        )
        DevicePerformance.MEDIUM -> PerformanceConfig(
            layerCount = 6,
            baseParticleCount = 35,
            particleIncrement = 8,
            shimmerDuration = 3500,
            enableGradients = true,
            enableBackgroundStars = false,
        )
        DevicePerformance.HIGH -> PerformanceConfig(
            layerCount = 8,
            baseParticleCount = 50,
            particleIncrement = 12,
            shimmerDuration = 3000,
            enableGradients = true,
            enableBackgroundStars = true,
        )
    }
}

/**
 * Function to calculate the position of an orbiting line on its path around a central point.
 * It uses trigonometric calculations to place the line accurately.
 *
 * @param center Offset representing the orbit's center.
 * @param radius Float indicating the radius of the orbit path.
 * @param angle Float for the current angle position of the line.
 * @param ellipticalRatio Optional Float for the elliptical path ratio.
 * @return Offset representing the new position on the orbit path.
 */
private fun calculateOrbitingPosition(
    center: Offset,
    radius: Float,
    angle: Float,
    ellipticalRatio: Float = 1.0f,
): Offset {
    val angleInRadians = Math.toRadians(angle.toDouble())
    val x = center.x + radius * cos(angleInRadians).toFloat() * ellipticalRatio
    val y = center.y + radius * sin(angleInRadians).toFloat()
    return Offset(x, y)
}

/**
 * Draw multiple orbiting lines with enhanced visual effects
 */
private fun DrawScope.drawOrbitingLines(
    center: Offset,
    blackHoleRadius: Float,
    rotation: Float,
    config: OrbitingLineConfig,
) {
    // Enable anti-aliasing for smooth rendering
    drawIntoCanvas { canvas ->
        val nativeCanvas = canvas.nativeCanvas

        config.lines.forEach { line ->
            // Calculate current angle with animation
            val animatedAngle = (line.positionAngle + rotation * config.animationSpeed + line.animationPhaseOffset) % 360f

            // Calculate position on orbit
            val ellipticalRatio = if (config.enableEllipticalOrbits) config.ellipticalRatio else 1.0f
            val position = calculateOrbitingPosition(
                center = center,
                radius = line.orbitalRadius,
                angle = animatedAngle,
                ellipticalRatio = ellipticalRatio,
            )

            // Calculate line direction (tangent to orbit)
            val tangentAngle = animatedAngle + 90f // Perpendicular to radius
            val tangentRad = Math.toRadians(tangentAngle.toDouble())

            // Calculate line endpoints
            val halfLength = line.length / 2f
            val startX = position.x - halfLength * cos(tangentRad).toFloat()
            val startY = position.y - halfLength * sin(tangentRad).toFloat()
            val endX = position.x + halfLength * cos(tangentRad).toFloat()
            val endY = position.y + halfLength * sin(tangentRad).toFloat()

            // Draw line trail with alpha gradient
            // Draw line with simple approach for now
            // TODO: Implement drawLineTrailWithGradient and drawGlowEffect
            drawLine(
                color = line.color.copy(alpha = line.opacity),
                start = Offset(startX, startY),
                end = Offset(endX, endY),
                strokeWidth = line.thickness,
            )
        }
    }
}

/**
 * 优化的流动线条黑洞绘制函数
 * 实现用户要求的两组流动线条效果：围绕黑洞边缘 + 横穿黑洞
 */
private fun DrawScope.drawOptimizedStarField(
    centerX: Float,
    centerY: Float,
    rotation: Float,
    shimmer: Float,
    spec: StarRingSpec,
    config: PerformanceConfig,
    blackHoleSizeRatio: Float,
    shadowIntensity: Float,
    gradientStops: Int,
    gradientColors: List<Color>,
    coreColor: Color,
    enableEventHorizon: Boolean,
    orbitingLineConfig: OrbitingLineConfig,
    numberOfLines: Int,
    animationSpeed: Float,
    animationDirection: Float,
    lineThickness: Float,
    lineColor: Color,
    lineGlowIntensity: Float,
    grayscaleColors: List<Color>,
    accentPink: Color,
    accentOrange: Color,
) {
    val w = size.minDimension
    val center = Offset(centerX, centerY)

    // 关键参数定义
    val blackHoleRadius = w * blackHoleSizeRatio // 黑洞半径占最短边的比例

    // 绘制黑洞中心（透明给LOGO）
    drawBlackHoleCore(
        center = center,
        radius = blackHoleRadius,
        shadowIntensity = shadowIntensity,
        gradientStops = gradientStops,
        coreColor = coreColor,
        enableEventHorizon = enableEventHorizon,
    )

    // Draw orbiting lines
    drawOrbitingLines(
        center = center,
        blackHoleRadius = blackHoleRadius,
        rotation = rotation * animationDirection,
        config = orbitingLineConfig,
    )

    // 绘制围绕黑洞边缘的流动线条
    drawCircumnavigatingLines(
        center = center,
        blackHoleRadius = blackHoleRadius,
        grayscaleColors = grayscaleColors,
        accentPink = accentPink,
        accentOrange = accentOrange,
        rotation = rotation,
        shimmer = shimmer,
    )

    // 绘制横穿黑洞的流动线条
    drawCrossingLines(
        center = center,
        blackHoleRadius = blackHoleRadius,
        canvasWidth = size.width,
        grayscaleColors = grayscaleColors,
        accentPink = accentPink,
        accentOrange = accentOrange,
        rotation = rotation,
        shimmer = shimmer,
    )
}

/**
 * Draws the central black hole with radial gradients and depth.
 *
 * @param center Center offset for the black hole.
 * @param radius Radius for the black hole section.
 * @param shadowIntensity Controls the intensity of shadow effects.
 * @param gradientStops Number of gradient stops used for color transitions.
 * @param coreColor The main color for the black hole.
 * @param enableEventHorizon Determines if the event horizon effect is rendered.
 */
private fun DrawScope.drawBlackHoleCore(
    center: Offset,
    radius: Float,
    shadowIntensity: Float,
    gradientStops: Int,
    coreColor: Color,
    enableEventHorizon: Boolean,
) {
    // 第一层：外部阴影光晕效果
    val shadowRadius = radius * 1.5f
    val shadowLayers = (shadowIntensity * 8).toInt().coerceIn(1, 8)
    for (i in 0 until shadowLayers) {
        val currentRadius = shadowRadius - (i * radius * 0.08f)
        val alpha = (0.05f + (i * 0.02f)) * shadowIntensity

        drawCircle(
            brush = Brush.radialGradient(
                colors = listOf(
                    Color.Transparent,
                    coreColor.copy(alpha = alpha * 0.3f),
                    coreColor.copy(alpha = alpha * 0.6f),
                    coreColor.copy(alpha = alpha),
                ),
                center = center,
                radius = currentRadius,
            ),
            radius = currentRadius,
            center = center,
        )
    }

    // 第二层：主黑洞径向渐变
    val colorStops = mutableListOf<Pair<Float, Color>>()

    for (i in 0..gradientStops) {
        val progress = i.toFloat() / gradientStops

        val alpha = when {
            progress < 0.2f -> 1f
            progress < 0.5f -> 1f - (progress - 0.2f) * 0.5f / 0.3f
            else -> 0.75f * (1f - progress) * (1f - progress)
        }

        val color = if (progress == 1f) {
            Color.Transparent
        } else {
            coreColor.copy(alpha = alpha)
        }

        colorStops.add(progress to color)
    }

    drawCircle(
        brush = Brush.radialGradient(
            *colorStops.toTypedArray(),
            center = center,
            radius = radius,
        ),
        radius = radius,
        center = center,
    )

    // 第三层：内部深度阴影效果
    val innerShadowRadius = radius * 0.9f
    drawCircle(
        brush = Brush.radialGradient(
            0f to coreColor.copy(alpha = 0.8f),
            0.3f to coreColor.copy(alpha = 0.6f),
            0.7f to coreColor.copy(alpha = 0.3f),
            1f to Color.Transparent,
            center = center,
            radius = innerShadowRadius,
        ),
        radius = innerShadowRadius,
        center = center,
        blendMode = BlendMode.Multiply,
    )

    // 第四层：事件视界光环效果
    if (enableEventHorizon) {
        val eventHorizonRadius = radius * 0.85f
        drawCircle(
            brush = Brush.radialGradient(
                0f to Color.Transparent,
                0.7f to Color.Transparent,
                0.8f to coreColor.copy(alpha = 0.3f * shadowIntensity),
                0.85f to coreColor.copy(alpha = 0.5f * shadowIntensity),
                0.9f to coreColor.copy(alpha = 0.3f * shadowIntensity),
                1f to Color.Transparent,
                center = center,
                radius = eventHorizonRadius,
            ),
            radius = eventHorizonRadius,
            center = center,
        )
    }

    // 第五层：中心挖洞给LOGO - 保持原有功能
    val logoRadius = radius * 0.7f
    drawCircle(
        color = Color.Transparent,
        radius = logoRadius,
        center = center,
        blendMode = BlendMode.Clear,
    )

    // 第六层：LOGO边缘柔和过渡
    val logoEdgeRadius = logoRadius * 1.05f
    drawCircle(
        brush = Brush.radialGradient(
            0f to Color.Transparent,
            0.8f to Color.Transparent,
            0.85f to coreColor.copy(alpha = 0.1f),
            0.92f to coreColor.copy(alpha = 0.2f),
            1f to coreColor.copy(alpha = 0.3f),
            center = center,
            radius = logoEdgeRadius,
        ),
        radius = logoEdgeRadius,
        center = center,
    )
}

/**
 * 绘制围绕黑洞边缘的流动线条
 * 从右开始，绕过黑洞上方，抵达屏幕边缘
 */
private fun DrawScope.drawCircumnavigatingLines(
    center: Offset,
    blackHoleRadius: Float,
    grayscaleColors: List<Color>,
    accentPink: Color,
    accentOrange: Color,
    rotation: Float,
    shimmer: Float,
) {
    val lineCount = 15 // 线条数量
    val flowRadius = blackHoleRadius * 1.1f // 流动半径稍大于黑洞

    for (i in 0 until lineCount) {
        val baseProgress = (rotation + i * 24f + shimmer * 20f) % 360f
        val startAngle = 0f // 从右侧开始（0度）
        val endAngle = 180f // 到黑洞上方（180度）

        // 计算当前线条的进度
        val currentAngle = startAngle + (baseProgress / 360f) * endAngle
        val progressAlongPath = (baseProgress / 360f) % 1f

        // 线条颜色选择 - Fine-tuned to match GymBro design system
        val colorChoice = i % 20
        val lineColor = when {
            colorChoice < 18 -> grayscaleColors[i % grayscaleColors.size] // 90% grayscale
            colorChoice == 18 -> Color(0xFF3F6CF3) // GymBro primary blue
            else -> Color(0xFF00BC80) // GymBro accent green
        }

        // 线条粗细随机变化
        val baseThickness = 2f + (i % 5) * 1.5f
        val thicknessVariation = sin(shimmer + i * 0.8f) * 1f
        val lineThickness = baseThickness + thicknessVariation

        // 随机速度调整
        val speedMultiplier = 0.8f + (i % 3) * 0.4f
        val adjustedProgress = (progressAlongPath * speedMultiplier) % 1f

        // 绘制弧形路径上的线条段
        drawFlowingArcLine(
            center = center,
            radius = flowRadius,
            startAngle = currentAngle,
            progress = adjustedProgress,
            color = lineColor,
            thickness = lineThickness,
            shimmer = shimmer,
        )
    }
}

/**
 * 绘制横穿黑洞的流动线条
 * 从右到左直线穿过黑洞
 */
private fun DrawScope.drawCrossingLines(
    center: Offset,
    blackHoleRadius: Float,
    canvasWidth: Float,
    grayscaleColors: List<Color>,
    accentPink: Color,
    accentOrange: Color,
    rotation: Float,
    shimmer: Float,
) {
    val lineCount = 12 // 横穿线条数量
    val lineSpacing = blackHoleRadius * 0.3f // 线条间距

    for (i in 0 until lineCount) {
        val yOffset = (i - lineCount / 2f) * lineSpacing
        val lineY = center.y + yOffset

        // 确保线条在黑洞范围内
        if (abs(yOffset) > blackHoleRadius) continue

        // 计算线条进度（从右到左）
        val baseProgress = (rotation + i * 30f + shimmer * 15f) % 360f
        val progressAlongLine = (baseProgress / 360f) % 1f

        // 线条颜色选择 - Fine-tuned to match GymBro design system
        val colorChoice = i % 20
        val lineColor = when {
            colorChoice < 18 -> grayscaleColors[i % grayscaleColors.size]
            colorChoice == 18 -> Color(0xFF3F6CF3) // GymBro primary blue
            else -> Color(0xFF00BC80) // GymBro accent green
        }

        // 线条粗细
        val baseThickness = 1.5f + (i % 4) * 1f
        val thicknessVariation = sin(shimmer * 1.5f + i * 0.6f) * 0.8f
        val lineThickness = baseThickness + thicknessVariation

        // 随机速度
        val speedMultiplier = 0.7f + (i % 4) * 0.3f
        val adjustedProgress = (progressAlongLine * speedMultiplier) % 1f

        // 绘制横穿线条
        drawFlowingHorizontalLine(
            startX = canvasWidth,
            endX = 0f,
            y = lineY,
            progress = adjustedProgress,
            color = lineColor,
            thickness = lineThickness,
            shimmer = shimmer,
        )
    }
}

/**
 * 绘制弧形流动线条 with enhanced visual effects
 */
private fun DrawScope.drawFlowingArcLine(
    center: Offset,
    radius: Float,
    startAngle: Float,
    progress: Float,
    color: Color,
    thickness: Float,
    shimmer: Float,
) {
    val segmentCount = 30 // Increased for smoother curves
    val angleRange = 180f // 半圆弧

    for (i in 0 until segmentCount) {
        val segmentProgress = i.toFloat() / segmentCount
        val distanceFromProgress = abs(segmentProgress - progress)

        // 只绘制靠近当前进度的段
        if (distanceFromProgress > 0.2f) continue

        val segmentAngle = startAngle + segmentProgress * angleRange
        val angleRad = segmentAngle * PI.toFloat() / 180f

        val x = center.x + cos(angleRad) * radius
        val y = center.y + sin(angleRad) * radius

        // Enhanced alpha gradient for trail effect
        val normalizedDistance = distanceFromProgress / 0.2f
        val alpha = (1f - normalizedDistance * normalizedDistance) * 0.9f
        val segmentThickness = thickness * (1f - normalizedDistance * 0.7f)

        // Draw with gradient effect
        drawCircle(
            color = color.copy(alpha = alpha),
            radius = segmentThickness,
            center = Offset(x, y),
        )

        // Add subtle glow for enhanced visibility
        if (alpha > 0.5f) {
            drawCircle(
                color = color.copy(alpha = alpha * 0.3f),
                radius = segmentThickness * 2f,
                center = Offset(x, y),
            )
        }
    }
}

/**
 * 绘制水平流动线条 with enhanced visual effects
 */
private fun DrawScope.drawFlowingHorizontalLine(
    startX: Float,
    endX: Float,
    y: Float,
    progress: Float,
    color: Color,
    thickness: Float,
    shimmer: Float,
) {
    val segmentCount = 35 // Increased for smoother flow
    val lineLength = abs(startX - endX)

    for (i in 0 until segmentCount) {
        val segmentProgress = i.toFloat() / segmentCount
        val distanceFromProgress = abs(segmentProgress - progress)

        // 只绘制靠近当前进度的段
        if (distanceFromProgress > 0.15f) continue

        val x = startX + (endX - startX) * segmentProgress

        // Enhanced alpha gradient for smooth trail
        val normalizedDistance = distanceFromProgress / 0.15f
        val alpha = (1f - normalizedDistance * normalizedDistance) * 0.85f
        val segmentThickness = thickness * (1f - normalizedDistance * 0.6f)

        // Draw main segment with gradient
        drawCircle(
            color = color.copy(alpha = alpha),
            radius = segmentThickness,
            center = Offset(x, y),
        )

        // Add subtle glow effect
        if (alpha > 0.4f) {
            drawCircle(
                color = color.copy(alpha = alpha * 0.25f),
                radius = segmentThickness * 2.5f,
                center = Offset(x, y),
            )
        }
    }
}

// === Preview组件 - 标准化预览 ===

@GymBroPreview
@Composable
private fun StarRingCanvasPreview() {
    GymBroTheme {
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            enableAnimation = true,
        )
    }
}

@GymBroPreview
@Composable
private fun StarRingCanvasStaticPreview() {
    GymBroTheme {
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            enableAnimation = false,
        )
    }
}

@GymBroPreview
@Composable
private fun StarRingCanvasPerformancePreview() {
    GymBroTheme {
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            enableAnimation = true,
            enablePerformanceMonitoring = true,
        )
    }
}

/**
 * 深色主题预览
 * 展示StarRingCanvas在深色主题下的视觉效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasDarkThemePreview() {
    GymBroTheme(darkTheme = true) {
        starRingTheme {
            StarRingCanvas(
                modifier = Modifier.fillMaxSize(),
                enableAnimation = false,
            )
        }
    }
}

/**
 * 浅色主题预览
 * 展示StarRingCanvas在浅色主题下的视觉效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasLightThemePreview() {
    GymBroTheme(darkTheme = false) {
        starRingTheme {
            StarRingCanvas(
                modifier = Modifier.fillMaxSize(),
                enableAnimation = false,
            )
        }
    }
}

/**
 * 极简深色主题预览
 * 展示使用预设主题的效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasMinimalDarkPreview() {
    GymBroTheme(darkTheme = true) {
        starRingTheme(spec = StarRingThemePresets.MinimalDark) {
            StarRingCanvas(
                modifier = Modifier.fillMaxSize(),
                enableAnimation = false,
            )
        }
    }
}

/**
 * 极简浅色主题预览
 * 展示使用预设主题的效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasMinimalLightPreview() {
    GymBroTheme(darkTheme = false) {
        starRingTheme(spec = StarRingThemePresets.MinimalLight) {
            StarRingCanvas(
                modifier = Modifier.fillMaxSize(),
                enableAnimation = false,
            )
        }
    }
}

/**
 * Auth专用StarRingCanvas预设配置
 * 针对登录页面优化的动画效果
 */
@Composable
fun AuthStarRingCanvas(
    modifier: Modifier = Modifier,
    isDarkTheme: Boolean = isSystemInDarkTheme(),
    enableAnimation: Boolean = true,
    size: androidx.compose.ui.unit.Dp = 200.dp,
) {
    starRingTheme(
        spec = if (isDarkTheme) {
            StarRingThemePresets.MinimalDark
        } else {
            StarRingThemePresets.MinimalLight
        },
    ) {
        StarRingCanvas(
            modifier = modifier.size(size),
            enableAnimation = enableAnimation,
            blackHoleSizeRatio = 0.25f, // 更小的黑洞，突出流动效果
            numberOfLines = 6, // 减少线条数量，避免过于复杂
            animationSpeed = 0.8f, // 稍慢的动画速度，更优雅
            lineThickness = 1.8f, // 稍细的线条，更精致
            shadowIntensity = 0.6f, // 适中的阴影强度
            gradientStops = 12, // 适中的渐变层次
            enableEventHorizon = true, // 保持事件视界效果
        )
    }
}

/**
 * Auth专用StarRingCanvas预览 - 深色主题
 */
@GymBroPreview
@Composable
private fun AuthStarRingCanvasDarkPreview() {
    GymBroTheme(darkTheme = true) {
        Box(
            modifier = Modifier
                .size(240.dp)
                .background(Color.Black),
            contentAlignment = Alignment.Center,
        ) {
            AuthStarRingCanvas(
                isDarkTheme = true,
                enableAnimation = false,
                size = 200.dp,
            )
        }
    }
}

/**
 * Auth专用StarRingCanvas预览 - 浅色主题
 */
@GymBroPreview
@Composable
private fun AuthStarRingCanvasLightPreview() {
    GymBroTheme(darkTheme = false) {
        Box(
            modifier = Modifier
                .size(240.dp)
                .background(Color.White),
            contentAlignment = Alignment.Center,
        ) {
            AuthStarRingCanvas(
                isDarkTheme = false,
                enableAnimation = false,
                size = 200.dp,
            )
        }
    }
}

/**
 * 高对比度深色主题预览
 * 展示高对比度配置下的视觉效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasHighContrastDarkPreview() {
    GymBroTheme(darkTheme = true) {
        starRingTheme(spec = StarRingThemePresets.HighContrastDark) {
            StarRingCanvas(
                modifier = Modifier.fillMaxSize(),
                enableAnimation = false,
            )
        }
    }
}

/**
 * 高对比度浅色主题预览
 * 展示高对比度配置下的视觉效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasHighContrastLightPreview() {
    GymBroTheme(darkTheme = false) {
        starRingTheme(spec = StarRingThemePresets.HighContrastLight) {
            StarRingCanvas(
                modifier = Modifier.fillMaxSize(),
                enableAnimation = false,
            )
        }
    }
}

/**
 * 自定义黑洞大小预览
 * 展示不同黑洞大小的视觉效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasCustomSizePreview() {
    GymBroTheme {
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            enableAnimation = false,
            blackHoleSizeRatio = 0.45f,
            shadowIntensity = 0.9f,
        )
    }
}

/**
 * 紫色黑洞预览
 * 展示自定义颜色的黑洞效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasPurpleHolePreview() {
    GymBroTheme {
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            enableAnimation = false,
            coreColor = Color(0xFF6A0DAD),
            shadowIntensity = 0.8f,
            gradientStops = 15,
        )
    }
}

/**
 * 小型黑洞无事件视界预览
 * 展示小型黑洞且没有事件视界的效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasSmallHolePreview() {
    GymBroTheme {
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            enableAnimation = false,
            blackHoleSizeRatio = 0.2f,
            shadowIntensity = 0.5f,
            enableEventHorizon = false,
        )
    }
}

/**
 * 高细节黑洞预览
 * 展示更多渐变层次的黑洞效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasHighDetailPreview() {
    GymBroTheme {
        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            enableAnimation = false,
            gradientStops = 20,
            shadowIntensity = 1f,
            coreColor = Color(0xFF1A1A1A),
        )
    }
}

/**
 * 自定义轨道线条预览
 * 展示自定义配置的轨道线条效果
 */
@GymBroPreview
@Composable
private fun StarRingCanvasCustomOrbitingLinesPreview() {
    GymBroTheme {
        val customConfig = OrbitingLineConfig(
            lines = listOf(
                // Slow outer ring - blue gradient
                OrbitingLine(
                    positionAngle = 0f,
                    orbitalRadius = 200f,
                    length = 120f,
                    thickness = 4f,
                    color = Color(0xFF00B4D8),
                    opacity = 0.9f,
                    animationPhaseOffset = 0f,
                ),
                OrbitingLine(
                    positionAngle = 90f,
                    orbitalRadius = 200f,
                    length = 120f,
                    thickness = 4f,
                    color = Color(0xFF0077B6),
                    opacity = 0.9f,
                    animationPhaseOffset = 0f,
                ),
                OrbitingLine(
                    positionAngle = 180f,
                    orbitalRadius = 200f,
                    length = 120f,
                    thickness = 4f,
                    color = Color(0xFF023E8A),
                    opacity = 0.9f,
                    animationPhaseOffset = 0f,
                ),
                OrbitingLine(
                    positionAngle = 270f,
                    orbitalRadius = 200f,
                    length = 120f,
                    thickness = 4f,
                    color = Color(0xFF03045E),
                    opacity = 0.9f,
                    animationPhaseOffset = 0f,
                ),
                // Fast inner ring - orange/red
                OrbitingLine(
                    positionAngle = 45f,
                    orbitalRadius = 150f,
                    length = 80f,
                    thickness = 3f,
                    color = Color(0xFFFF6B35),
                    opacity = 0.7f,
                    animationPhaseOffset = 45f,
                ),
                OrbitingLine(
                    positionAngle = 225f,
                    orbitalRadius = 150f,
                    length = 80f,
                    thickness = 3f,
                    color = Color(0xFFF7931E),
                    opacity = 0.7f,
                    animationPhaseOffset = 45f,
                ),
            ),
            animationSpeed = 1.5f,
            enableEllipticalOrbits = true,
            ellipticalRatio = 0.8f,
        )

        StarRingCanvas(
            modifier = Modifier.fillMaxSize(),
            enableAnimation = true,
            blackHoleSizeRatio = 0.3f,
            orbitingLineConfig = customConfig,
        )
    }
}

package com.example.gymbro.domain.workout.model.stats

import androidx.compose.runtime.Immutable
import kotlinx.datetime.LocalDate
import kotlinx.serialization.Serializable

/**
 * 日级训练统计数据模型
 *
 * 用于存储和管理每日训练完成后的聚合统计信息。
 * 该模型遵循项目的Clean Architecture原则，属于Domain层的核心业务实体。
 *
 * 特性：
 * - 基于Session聚合生成的统计数据
 * - 支持Plan进度状态回写
 * - 提供格式化显示方法
 * - 数据持久化到daily_stats表
 *
 * @property userId 用户ID
 * @property date 统计日期
 * @property completedSessions 完成的训练次数
 * @property completedExercises 完成的练习数量
 * @property completedSets 完成的组数
 * @property totalReps 总次数
 * @property totalWeight 总重量(kg)
 * @property avgRpe 平均RPE(1-10, 可选)
 * @property sessionDurationSec 训练总时长(秒)
 * @property planId 关联的训练计划ID(可选)
 * @property dayOfWeek 星期几(1-7)
 * @property caloriesBurned 消耗卡路里(可选)
 * @property averageHeartRate 平均心率(可选)
 * @property createdAt 创建时间戳
 * @property updatedAt 更新时间戳
 */
@Immutable
@Serializable
data class DailyStats(
    val userId: String,
    val date: LocalDate,
    val completedSessions: Int = 0,
    val completedExercises: Int = 0,
    val completedSets: Int = 0,
    val totalReps: Int = 0,
    val totalWeight: Double = 0.0,
    val avgRpe: Float? = null,
    val sessionDurationSec: Int = 0,
    val planId: String? = null,
    val dayOfWeek: Int = date.dayOfWeek.ordinal + 1,
    val caloriesBurned: Int? = null,
    val averageHeartRate: Int? = null,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
) {
    /**
     * 训练时长格式化显示
     * @return 格式化的时长字符串，如 "1小时23分" 或 "45分钟"
     */
    fun getFormattedDuration(): String {
        val hours = sessionDurationSec / 3600
        val minutes = (sessionDurationSec % 3600) / 60

        return when {
            hours > 0 -> "${hours}小时${minutes}分"
            minutes > 0 -> "${minutes}分钟"
            else -> "少于1分钟"
        }
    }

    /**
     * 总重量格式化显示
     * @return 格式化的重量字符串，如 "1.2k" 或 "850kg"
     */
    fun getFormattedWeight(): String {
        return when {
            totalWeight >= 1000 -> "${(totalWeight / 1000).toInt()}k"
            totalWeight > 0 -> "${totalWeight.toInt()}kg"
            else -> "0kg"
        }
    }

    /**
     * RPE格式化显示
     * @return 格式化的RPE字符串，如 "RPE 7.5" 或 "未记录"
     */
    fun getFormattedRpe(): String {
        return avgRpe?.let { "RPE ${String.format("%.1f", it)}" } ?: "未记录"
    }

    /**
     * 训练强度评估
     * @return 训练强度等级
     */
    fun getIntensityLevel(): IntensityLevel {
        return when {
            avgRpe == null -> IntensityLevel.UNKNOWN
            avgRpe < 4.0f -> IntensityLevel.LIGHT
            avgRpe < 7.0f -> IntensityLevel.MODERATE
            avgRpe < 8.5f -> IntensityLevel.HARD
            else -> IntensityLevel.VERY_HARD
        }
    }

    /**
     * 检查是否为高质量训练
     * 基于完成的组数、时长和RPE综合评估
     */
    fun isHighQualityWorkout(): Boolean {
        return completedSets >= 8 &&
            sessionDurationSec >= 1800 && // 至少30分钟
            (avgRpe == null || avgRpe >= 6.0f) // RPE ≥ 6或未记录
    }

    /**
     * 获取训练效率分数 (0-100)
     * 基于完成组数/时长比值计算
     */
    fun getEfficiencyScore(): Int {
        if (sessionDurationSec <= 0) return 0

        val setsPerMinute = completedSets.toDouble() / (sessionDurationSec / 60.0)
        val normalizedScore = (setsPerMinute * 20).coerceIn(0.0, 100.0)
        return normalizedScore.toInt()
    }

    /**
     * 转换为UI显示模型
     */
    fun toUiModel(): DailyStatsUiModel {
        return DailyStatsUiModel(
            date = date,
            title = "${date.monthNumber}月${date.dayOfMonth}日训练总结",
            completedSets = completedSets,
            formattedWeight = getFormattedWeight(),
            formattedDuration = getFormattedDuration(),
            avgRpe = avgRpe,
            intensityLevel = getIntensityLevel(),
            efficiencyScore = getEfficiencyScore(),
            isHighQuality = isHighQualityWorkout(),
        )
    }

    companion object {
        /**
         * 创建空的统计数据
         */
        fun empty(userId: String, date: LocalDate): DailyStats {
            return DailyStats(
                userId = userId,
                date = date,
            )
        }

        /**
         * 从Session数据聚合创建统计
         */
        fun fromSessionData(
            userId: String,
            date: LocalDate,
            sessionDurationSec: Int,
            completedExercises: Int,
            completedSets: Int,
            totalReps: Int,
            totalWeight: Double,
            avgRpe: Float? = null,
            planId: String? = null,
        ): DailyStats {
            return DailyStats(
                userId = userId,
                date = date,
                completedSessions = 1,
                completedExercises = completedExercises,
                completedSets = completedSets,
                totalReps = totalReps,
                totalWeight = totalWeight,
                avgRpe = avgRpe,
                sessionDurationSec = sessionDurationSec,
                planId = planId,
            )
        }
    }
}

/**
 * 训练强度等级
 */
@Serializable
enum class IntensityLevel(val displayName: String, val colorHex: String) {
    LIGHT("轻松", "#4CAF50"),
    MODERATE("中等", "#FF9800"),
    HARD("困难", "#F44336"),
    VERY_HARD("极限", "#9C27B0"),
    UNKNOWN("未知", "#9E9E9E"),
}

/**
 * UI层显示模型
 * 专门用于Stats UI组件的数据绑定
 */
@Immutable
data class DailyStatsUiModel(
    val date: LocalDate,
    val title: String,
    val completedSets: Int,
    val formattedWeight: String,
    val formattedDuration: String,
    val avgRpe: Float?,
    val intensityLevel: IntensityLevel,
    val efficiencyScore: Int,
    val isHighQuality: Boolean,
)
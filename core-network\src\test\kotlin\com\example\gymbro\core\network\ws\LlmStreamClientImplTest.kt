package com.example.gymbro.core.network.ws

import com.example.gymbro.core.network.config.WsConfig
import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.shared.models.ai.ChatRequest
import com.example.gymbro.shared.models.network.NetworkResult
import io.mockk.*
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import kotlinx.serialization.json.Json
import okhttp3.*
import okhttp3.mockwebserver.MockResponse
import okhttp3.mockwebserver.MockWebServer
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * LlmStreamClientImpl WebSocket Service V2 单元测试
 *
 * 测试状态机、心跳机制、重连逻辑和断点续传功能
 */
class LlmStreamClientImplTest {

    private lateinit var mockWebServer: MockWebServer
    private lateinit var okHttpClient: OkHttpClient
    private lateinit var json: Json
    private lateinit var config: WsConfig
    private lateinit var tokenOffsetStore: TokenOffsetStore
    private lateinit var client: LlmStreamClientImpl

    @Before
    fun setup() {
        mockWebServer = MockWebServer()
        mockWebServer.start()

        okHttpClient = OkHttpClient.Builder()
            .connectTimeout(1, java.util.concurrent.TimeUnit.SECONDS)
            .readTimeout(1, java.util.concurrent.TimeUnit.SECONDS)
            .build()

        json = Json {
            ignoreUnknownKeys = true
            isLenient = true
        }

        config = WsConfig(
            wsUrl = mockWebServer.url("/").toString().removeSuffix("/"),
            apiKey = "test-api-key",
            basePingSec = 1, // 快速测试
            pongTimeoutSec = 1,
            maxRetries = 3,
            backoffStartMs = 100,
            backoffCapMs = 1000,
            connectTimeoutSec = 5,
            enableOffsetResume = true,
        )

        tokenOffsetStore = mockk<TokenOffsetStore>(relaxed = true)

        client = LlmStreamClientImpl(okHttpClient, json, config.apiKey, config.wsUrl)
    }

    @After
    fun teardown() {
        mockWebServer.shutdown()
    }

    @Test
    fun `test initial state is OPEN`() = runTest {
        assertEquals(WsState.OPEN, client.getCurrentState())
    }

    @Test
    fun `test state transitions`() {
        // 测试状态转换逻辑
        assertTrue(WsState.INIT.canReconnect())
        assertTrue(WsState.CONNECTING.canReconnect())
        assertTrue(WsState.RECONNECTING.canReconnect())

        assertTrue(WsState.OPEN.isActive())
        assertTrue(WsState.STREAMING.isActive())

        assertTrue(WsState.OPEN.isConnected())
        assertTrue(WsState.STREAMING.isConnected())

        assertTrue(WsState.DEAD.isTerminated())
    }

    @Test
    fun `test WsFrame creation and validation`() {
        val pingFrame = WsFrame.createPing("test-id")
        assertEquals(WsFrame.TYPE_PING, pingFrame.type)
        assertEquals("test-id", pingFrame.id)
        assertTrue(pingFrame.isPing())
        assertTrue(WsFrame.isHeartbeat(pingFrame))

        val pongFrame = WsFrame.createPong("test-id")
        assertEquals(WsFrame.TYPE_PONG, pongFrame.type)
        assertTrue(pongFrame.isPong())
        assertTrue(WsFrame.isHeartbeat(pongFrame))
    }

    @Test
    fun `test TokenOffsetStore operations`() = runTest {
        val store = InMemoryTokenOffsetStore()

        // 初始值应该是0
        assertEquals(0, store.getLastTokenIndex("conv1"))

        // 更新索引
        store.updateLastTokenIndex("conv1", 42)
        assertEquals(42, store.getLastTokenIndex("conv1"))

        // 清除特定会话
        store.clearTokenIndex("conv1")
        assertEquals(0, store.getLastTokenIndex("conv1"))

        // 测试多个会话
        store.updateLastTokenIndex("conv1", 10)
        store.updateLastTokenIndex("conv2", 20)
        assertEquals(10, store.getLastTokenIndex("conv1"))
        assertEquals(20, store.getLastTokenIndex("conv2"))

        // 清除所有
        store.clearAllTokenIndexes()
        assertEquals(0, store.getLastTokenIndex("conv1"))
        assertEquals(0, store.getLastTokenIndex("conv2"))
    }

    @Test
    fun `test SSE data parsing compatibility`() = runTest {
        // 模拟SSE响应
        mockWebServer.enqueue(
            MockResponse()
                .setBody(
                    "data: {\"choices\":[{\"delta\":{\"content\":\"Hello\"}}]}\n\n" +
                        "data: {\"choices\":[{\"delta\":{\"content\":\" World\"}}]}\n\n" +
                        "data: [DONE]\n\n",
                )
                .setHeader("Content-Type", "text/event-stream"),
        )

        val request = ChatRequest(
            model = "test-model",
            messages = listOf(ChatMessage(role = "user", content = "Test")),
            stream = true,
        )

        // 使用向后兼容的SSE模式
        val responses = client.streamChatLegacy(json.encodeToString(request)).toList()

        // 验证响应
        assertTrue(responses.isNotEmpty())
        assertTrue(responses.any { it.contains("Hello") })
    }

    @Test
    fun `test connection failure and retry`() = runTest {
        // 模拟连接失败
        mockWebServer.enqueue(MockResponse().setResponseCode(500))
        mockWebServer.enqueue(MockResponse().setResponseCode(500))
        mockWebServer.enqueue(
            MockResponse()
                .setBody("data: {\"choices\":[{\"delta\":{\"content\":\"Success\"}}]}\n\ndata: [DONE]\n\n")
                .setHeader("Content-Type", "text/event-stream"),
        )

        val request = ChatRequest(
            model = "test-model",
            messages = listOf(ChatMessage(role = "user", content = "Test")),
            stream = true,
        )

        val responses = client.streamChatLegacy(json.encodeToString(request)).toList()

        // 验证最终成功
        assertTrue(responses.any { it.contains("Success") })
    }

    @Test
    fun `test checkConnection method`() = runTest {
        // 模拟成功的连接检查
        mockWebServer.enqueue(MockResponse().setResponseCode(200))

        val result = client.checkConnection()
        assertTrue(result is NetworkResult.Success)
    }

    @Test
    fun `test getBaseUrl method`() {
        assertEquals(config.wsUrl, client.getBaseUrl())
    }

    @Test
    fun `test offset store integration`() = runTest {
        // 验证offset store被正确调用
        coEvery { tokenOffsetStore.getLastTokenIndex(any()) } returns 42

        val request = ChatRequest(
            model = "test-model",
            messages = listOf(ChatMessage(role = "user", content = "Test")),
            stream = true,
        )

        // 模拟WebSocket响应（这里简化为SSE测试）
        mockWebServer.enqueue(
            MockResponse()
                .setBody("data: [DONE]\n\n")
                .setHeader("Content-Type", "text/event-stream"),
        )

        // 🔥 【事件总线架构】使用streamChatWithMessageId
        val testMessageId = "test-offset-${System.currentTimeMillis()}"
        client.streamChatWithMessageId(request, testMessageId, 0)

        // 验证offset被查询
        coVerify { tokenOffsetStore.getLastTokenIndex(any()) }
    }
}

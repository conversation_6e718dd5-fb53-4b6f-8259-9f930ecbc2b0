package com.example.gymbro.domain.user.service

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.profile.model.user.UserProfile

/**
 * 用户数据服务接口
 *
 * 定义了用户数据管理的核心操作，遵循 Clean Architecture 原则。
 * 这个接口在 domain 层定义，具体实现在基础设施层（如 core-user-data-center）。
 *
 * 设计原则：
 * - 接口隔离：只暴露 Profile 模块需要的用户数据操作
 * - 依赖倒置：domain 层定义接口，基础设施层实现
 * - 单一职责：专注于用户数据的 CRUD 操作
 */
interface UserDataService {

    /**
     * 获取当前用户的资料信息
     *
     * @return 用户资料，如果用户未登录或资料不存在则返回 null
     */
    suspend fun getCurrentUserProfile(): ModernResult<UserProfile?>

    /**
     * 更新用户资料信息
     *
     * @param profile 要更新的用户资料
     * @return 更新结果
     */
    suspend fun updateUserProfile(profile: UserProfile): ModernResult<Unit>
}

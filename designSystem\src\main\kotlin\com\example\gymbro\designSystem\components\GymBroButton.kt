package com.example.gymbro.designSystem.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.semantics.contentDescription
import androidx.compose.ui.semantics.semantics
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.GymBroTokenValidator
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * GymBro通用按钮组件 v2.0
 *
 * 采用新的高级颜色系统，提供优雅的视觉体验和完整的无障碍支持
 *
 * 特性：
 * - 使用新的13级灰阶颜色系统
 * - Overlay式加载状态，避免布局跳变
 * - 完整的无障碍支持（contentDescription、semantics）
 * - 标准化API参数顺序
 * - 优化的视觉反馈和状态切换
 * - 智能高度适配（基于内容和重要性）
 *
 * @param onClick 点击回调函数
 * @param text 按钮文本内容（UiText）
 * @param modifier 修饰符，默认为Modifier（第一个可选参数）
 * @param enabled 是否启用按钮，默认为true
 * @param isLoading 是否显示加载状态，默认为false
 * @param importance 按钮重要性，影响高度和视觉层级
 * @param colors 按钮颜色配置，默认使用新的颜色系统
 * @param shape 按钮形状，默认圆角
 * @param contentDescription 无障碍描述，用于屏幕阅读器
 */
@Composable
fun GymBroButton(
    onClick: () -> Unit,
    text: UiText,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    isLoading: Boolean = false,
    importance: ButtonImportance = ButtonImportance.Primary,
    colors: ButtonColors = GymBroButtonDefaults.colors(),
    shape: Shape = GymBroButtonDefaults.shape,
    contentDescription: String? = null,
) {
    // Token 使用验证 (仅在 DEBUG 模式下)
    GymBroTokenValidator.validateTokenUsage("GymBroButton")
    val buttonHeight =
        when (importance) {
            ButtonImportance.Large -> Tokens.Button.HeightLarge // 64.dp - 使用 Token
            ButtonImportance.Primary -> Tokens.Button.HeightPrimary // 56.dp - 使用 Token
            ButtonImportance.Secondary -> Tokens.Button.HeightSecondary // 48.dp - 使用 Token
            ButtonImportance.Small -> Tokens.Button.HeightSmall // 40.dp - 使用 Token
        }

    val actualEnabled = enabled && !isLoading
    val textContent = text.asString()
    val accessibilityDescription = contentDescription ?: textContent

    Button(
        onClick = onClick,
        modifier =
        modifier
            .fillMaxWidth()
            .height(buttonHeight)
            .semantics {
                this.contentDescription =
                    if (isLoading) {
                        "$accessibilityDescription - 加载中"
                    } else {
                        accessibilityDescription
                    }
            },
        enabled = actualEnabled,
        colors = colors,
        shape = shape,
        elevation =
        ButtonDefaults.buttonElevation(
            defaultElevation = Tokens.Elevation.Button,
            pressedElevation = Tokens.Elevation.ButtonPressed,
            disabledElevation = Tokens.Elevation.None,
        ),
    ) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier.fillMaxWidth(),
        ) {
            // 主要内容 - 文本
            Text(
                text = textContent,
                style =
                when (importance) {
                    ButtonImportance.Large -> MaterialTheme.typography.labelLarge
                    ButtonImportance.Primary -> MaterialTheme.typography.labelMedium
                    ButtonImportance.Secondary -> MaterialTheme.typography.labelMedium
                    ButtonImportance.Small -> MaterialTheme.typography.labelSmall
                },
                color =
                if (actualEnabled) {
                    colors.contentColor
                } else {
                    colors.disabledContentColor
                },
            )

            // 覆盖式加载指示器 - 避免布局跳变
            if (isLoading) {
                CircularProgressIndicator(
                    modifier =
                    Modifier
                        .size(Tokens.Button.LoadingIndicatorSize) // 20.dp - 使用 Token
                        .semantics {
                            this.contentDescription = "正在加载"
                        },
                    color = colors.contentColor,
                    strokeWidth = Tokens.Button.LoadingIndicatorStroke, // 2.dp - 使用 Token
                )
            }
        }
    }
}

/**
 * 按钮重要性枚举
 * 用于确定按钮的视觉层级和尺寸
 * 现在使用 Tokens.Button.Height* 系统，确保设计一致性
 */
enum class ButtonImportance {
    Large, // Tokens.Button.HeightLarge (64dp) - 主要CTA按钮
    Primary, // Tokens.Button.HeightPrimary (56dp) - 标准主要按钮
    Secondary, // Tokens.Button.HeightSecondary (48dp) - 次要按钮
    Small, // Tokens.Button.HeightSmall (40dp) - 紧凑布局按钮
}

/**
 * GymBro按钮默认配置 v2.0
 *
 * 基于新的高级颜色系统，提供精致的视觉效果
 */
object GymBroButtonDefaults {
    /**
     * 主要按钮颜色配置 - 采用优雅橙色
     */
    @Composable
    fun colors(
        containerColor: Color = Tokens.Color.CTAPrimary,
        contentColor: Color = Tokens.Color.Gray950,
        disabledContainerColor: Color = Tokens.Color.Gray400,
        disabledContentColor: Color = Tokens.Color.Gray600,
    ): ButtonColors =
        ButtonDefaults.buttonColors(
            containerColor = containerColor,
            contentColor = contentColor,
            disabledContainerColor = disabledContainerColor,
            disabledContentColor = disabledContentColor,
        )

    /**
     * 次要按钮颜色配置 - 采用灰阶系统
     */
    @Composable
    fun secondaryColors(
        containerColor: Color = Tokens.Color.Gray200,
        contentColor: Color = Tokens.Color.Gray800,
        disabledContainerColor: Color = Tokens.Color.Gray300,
        disabledContentColor: Color = Tokens.Color.Gray500,
    ): ButtonColors =
        ButtonDefaults.buttonColors(
            containerColor = containerColor,
            contentColor = contentColor,
            disabledContainerColor = disabledContainerColor,
            disabledContentColor = disabledContentColor,
        )

    /**
     * 品牌按钮颜色配置 - 采用铂金银
     */
    @Composable
    fun brandColors(
        containerColor: Color = Tokens.Color.BrandPrimary,
        contentColor: Color = Tokens.Color.Gray000,
        disabledContainerColor: Color = Tokens.Color.Gray400,
        disabledContentColor: Color = Tokens.Color.Gray600,
    ): ButtonColors =
        ButtonDefaults.buttonColors(
            containerColor = containerColor,
            contentColor = contentColor,
            disabledContainerColor = disabledContainerColor,
            disabledContentColor = disabledContentColor,
        )

    /**
     * 轮廓按钮颜色配置 - 透明背景
     */
    @Composable
    fun outlinedColors(
        containerColor: Color = Color.Transparent,
        contentColor: Color = Tokens.Color.BrandPrimary,
        disabledContainerColor: Color = Color.Transparent,
        disabledContentColor: Color = Tokens.Color.Gray500,
    ): ButtonColors =
        ButtonDefaults.buttonColors(
            containerColor = containerColor,
            contentColor = contentColor,
            disabledContainerColor = disabledContainerColor,
            disabledContentColor = disabledContentColor,
        )

    /**
     * 默认按钮形状 - 使用token系统
     */
    val shape: Shape @Composable get() = RoundedCornerShape(Tokens.Radius.Button)

    /**
     * 大按钮形状 - 更大的圆角
     */
    val largeShape: Shape @Composable get() = RoundedCornerShape(Tokens.Radius.ButtonLarge)
}

// === Preview组件 - 展示新的颜色系统 ===

@GymBroPreview
@Composable
private fun GymBroButtonPreview() {
    GymBroTheme {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 主要按钮组
            Text(
                text = "主要按钮（CTA橙色）",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
            GymBroButton(
                onClick = { },
                text = UiText.DynamicString("立即开始训练"),
                importance = ButtonImportance.Large,
                contentDescription = "开始训练按钮",
            )

            // 品牌按钮组
            Text(
                text = "品牌按钮（铂金银）",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
            GymBroButton(
                onClick = { },
                text = UiText.DynamicString("升级专业版"),
                colors = GymBroButtonDefaults.brandColors(),
                contentDescription = "升级专业版按钮",
            )

            // 次要按钮组
            Text(
                text = "次要按钮（灰阶）",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
            GymBroButton(
                onClick = { },
                text = UiText.DynamicString("查看详情"),
                colors = GymBroButtonDefaults.secondaryColors(),
                importance = ButtonImportance.Secondary,
            )

            // 加载状态
            Text(
                text = "加载状态（覆盖式）",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
            GymBroButton(
                onClick = { },
                text = UiText.DynamicString("正在同步数据"),
                isLoading = true,
                contentDescription = "同步数据按钮",
            )

            // 禁用状态
            Text(
                text = "禁用状态",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
            GymBroButton(
                onClick = { },
                text = UiText.DynamicString("暂不可用"),
                enabled = false,
                contentDescription = "不可用按钮",
            )

            // 小按钮
            Text(
                text = "紧凑按钮",
                style = MaterialTheme.typography.labelSmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
            GymBroButton(
                onClick = { },
                text = UiText.DynamicString("确认"),
                importance = ButtonImportance.Small,
                colors = GymBroButtonDefaults.outlinedColors(),
            )
        }
    }
}

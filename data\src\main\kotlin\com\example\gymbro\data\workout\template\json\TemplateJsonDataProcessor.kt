package com.example.gymbro.data.workout.template.json

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.port.JsonProcessorPort
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Data层专用的Template JSON处理器（兼容性包装器）
 *
 * 重构说明：
 * - 现在基于JsonProcessorPort接口，确保架构一致性
 * - 保持原有的静态方法接口，提供向后兼容性
 * - 内部委托给JsonProcessorImpl执行实际操作
 *
 * 核心功能：
 * - WorkoutTemplate (Domain) ↔ JSON String
 * - 使用DTO作为中间转换层
 * - 兼容原有调用方式，同时符合Clean Architecture原则
 *
 * <AUTHOR> AI Assistant
 */
@Singleton
class TemplateJsonDataProcessor @Inject constructor(
    private val jsonProcessor: JsonProcessorPort,
) {

    companion object {
        // 保持向后兼容的静态实例（延迟初始化）
        @Volatile
        private var staticInstance: TemplateJsonDataProcessor? = null

        /**
         * 获取静态实例（兼容性方法）
         * 注意：这个方法主要用于向后兼容，新代码应该通过DI注入JsonProcessorPort
         */
        private fun getInstance(): TemplateJsonDataProcessor {
            return staticInstance ?: synchronized(this) {
                // 创建临时实例，避免直接依赖具体实现
                staticInstance ?: TemplateJsonDataProcessor(
                    object : JsonProcessorPort {
                        private val impl = JsonProcessorImpl()
                        override suspend fun serializeTemplate(
                            template: WorkoutTemplate,
                        ) = impl.serializeTemplate(template)
                        override suspend fun deserializeTemplate(
                            jsonString: String,
                        ) = impl.deserializeTemplate(jsonString)
                        override suspend fun validateJson(jsonString: String) = impl.validateJson(jsonString)
                        override suspend fun formatJson(jsonString: String) = impl.formatJson(jsonString)
                    },
                ).also { staticInstance = it }
            }
        }

        /**
         * 将Domain模型序列化为JSON字符串（兼容性方法）
         * @param template Domain模型
         * @return JSON字符串，失败返回null
         */
        fun safeToJson(template: WorkoutTemplate): String? {
            return runBlocking {
                when (val result = getInstance().jsonProcessor.serializeTemplate(template)) {
                    is ModernResult.Success -> result.data
                    is ModernResult.Error -> {
                        Timber.e(result.error.cause, "Template序列化失败: ${template.id}")
                        null
                    }
                    is ModernResult.Loading -> null
                }
            }
        }

        /**
         * 从JSON字符串反序列化为Domain模型（兼容性方法）
         * @param jsonString JSON字符串
         * @return Domain模型，失败返回null
         */
        fun fromJson(jsonString: String): WorkoutTemplate? {
            return runBlocking {
                when (val result = getInstance().jsonProcessor.deserializeTemplate(jsonString)) {
                    is ModernResult.Success -> result.data
                    is ModernResult.Error -> {
                        Timber.e(result.error.cause, "Template反序列化失败")
                        null
                    }
                    is ModernResult.Loading -> null
                }
            }
        }
    }

    /**
     * 实例方法：序列化Template
     */
    suspend fun serializeTemplate(template: WorkoutTemplate): ModernResult<String> {
        return jsonProcessor.serializeTemplate(template)
    }

    /**
     * 实例方法：反序列化Template
     */
    suspend fun deserializeTemplate(jsonString: String): ModernResult<WorkoutTemplate> {
        return jsonProcessor.deserializeTemplate(jsonString)
    }

    /**
     * 实例方法：验证JSON格式
     */
    suspend fun validateJson(jsonString: String): ModernResult<Boolean> {
        return jsonProcessor.validateJson(jsonString)
    }

    /**
     * 实例方法：格式化JSON
     */
    suspend fun formatJson(jsonString: String): ModernResult<String> {
        return jsonProcessor.formatJson(jsonString)
    }
}
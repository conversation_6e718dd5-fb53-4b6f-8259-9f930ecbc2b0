package com.example.gymbro.data.repository

import com.example.gymbro.core.error.types.*
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.userdata.api.UserDataCenterApi
import com.example.gymbro.domain.profile.model.user.WorkoutDay
import com.example.gymbro.domain.profile.repository.ProfileRepository
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * ProfileRepository的实现类
 *
 * 重构后使用 UserDataCenter 作为统一的用户数据管理中心，
 * 替代之前的 DataStore 存储方式，确保数据的一致性和统一管理。
 *
 * 核心变更：
 * - 训练日设置通过 UserDataCenter 统一管理
 * - 用户资料摘要从 UnifiedUserData 生成
 * - 保持接口兼容性，内部实现透明切换
 */
@Singleton
class ProfileRepositoryImpl
@Inject
constructor(
    private val userDataCenterApi: UserDataCenterApi,
) : ProfileRepository {
    override suspend fun getWorkoutDays(): List<Int> =
        try {
            Timber.d("从 UserDataCenter 获取训练日设置")

            // 从 UserDataCenter 获取当前用户数据
            when (val result = userDataCenterApi.getCurrentUserData()) {
                is ModernResult.Success -> {
                    val userData = result.data
                    if (userData != null) {
                        // 将 WorkoutDay 枚举转换为 Int 列表
                        val workoutDays = userData.workoutDays.map { workoutDay ->
                            when (workoutDay) {
                                WorkoutDay.MONDAY -> 1
                                WorkoutDay.TUESDAY -> 2
                                WorkoutDay.WEDNESDAY -> 3
                                WorkoutDay.THURSDAY -> 4
                                WorkoutDay.FRIDAY -> 5
                                WorkoutDay.SATURDAY -> 6
                                WorkoutDay.SUNDAY -> 7
                            }
                        }
                        Timber.d("获取训练日成功: $workoutDays")
                        workoutDays
                    } else {
                        Timber.d("用户未登录，返回默认训练日")
                        listOf(1, 3, 5) // 默认：周一、周三、周五
                    }
                }
                is ModernResult.Error -> {
                    Timber.e("获取用户数据失败: ${result.error}")
                    listOf(1, 3, 5) // 默认：周一、周三、周五
                }
                is ModernResult.Loading -> {
                    Timber.d("用户数据加载中，返回默认训练日")
                    listOf(1, 3, 5) // 默认：周一、周三、周五
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "获取训练日设置时发生异常")
            listOf(1, 3, 5) // 默认：周一、周三、周五
        }

    override suspend fun saveWorkoutDays(workoutDays: List<Int>) {
        try {
            Timber.d("保存训练日设置到 UserDataCenter: $workoutDays")

            // 首先获取当前用户数据
            when (val currentResult = userDataCenterApi.getCurrentUserData()) {
                is ModernResult.Success -> {
                    val currentData = currentResult.data
                    if (currentData != null) {
                        // 将 Int 列表转换为 WorkoutDay 枚举列表
                        val workoutDayEnums = workoutDays.mapNotNull { day ->
                            when (day) {
                                1 -> WorkoutDay.MONDAY
                                2 -> WorkoutDay.TUESDAY
                                3 -> WorkoutDay.WEDNESDAY
                                4 -> WorkoutDay.THURSDAY
                                5 -> WorkoutDay.FRIDAY
                                6 -> WorkoutDay.SATURDAY
                                7 -> WorkoutDay.SUNDAY
                                else -> {
                                    Timber.w("无效的训练日: $day")
                                    null
                                }
                            }
                        }

                        // 创建更新的用户资料，只更新训练日字段
                        val updatedProfile = com.example.gymbro.domain.profile.model.user.UserProfile(
                            userId = currentData.userId,
                            username = currentData.username,
                            displayName = currentData.displayName,
                            email = currentData.email,
                            phoneNumber = currentData.phoneNumber,
                            bio = currentData.bio,
                            gender = currentData.gender,
                            height = currentData.height,
                            weight = currentData.weight,
                            fitnessLevel = currentData.fitnessLevel,
                            fitnessGoals = currentData.fitnessGoals,
                            workoutDays = workoutDayEnums, // 更新训练日
                            allowPartnerMatching = currentData.allowPartnerMatching,
                            totalActivityCount = currentData.totalActivityCount,
                            weeklyActiveMinutes = currentData.weeklyActiveMinutes,
                            // 注意：UserProfile 模型没有 lastUpdated 字段，时间戳由 UserDataCenter 管理
                        )

                        // 通过 UserDataCenter 更新用户资料
                        when (val updateResult = userDataCenterApi.updateProfile(updatedProfile)) {
                            is ModernResult.Success -> {
                                Timber.d("训练日设置保存成功")
                            }
                            is ModernResult.Error -> {
                                Timber.e("保存训练日设置失败: ${updateResult.error}")
                                throw Exception("保存训练日设置失败: ${updateResult.error.uiMessage}")
                            }
                            is ModernResult.Loading -> {
                                Timber.w("保存训练日设置仍在处理中")
                            }
                        }
                    } else {
                        throw Exception("用户未登录，无法保存训练日设置")
                    }
                }
                is ModernResult.Error -> {
                    Timber.e("获取当前用户数据失败: ${currentResult.error}")
                    throw Exception("获取当前用户数据失败，无法保存训练日设置")
                }
                is ModernResult.Loading -> {
                    throw Exception("用户数据加载中，请稍后重试")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "保存训练日设置时发生异常")
            throw e
        }
    }

    // ===== Task2-CoachContext数据中心集成：RAG相关方法实现 =====

    override suspend fun getProfileSummary(): ModernResult<String?> =
        try {
            Timber.d("从 UserDataCenter 生成用户资料摘要")

            // 从 UserDataCenter 获取当前用户数据
            when (val result = userDataCenterApi.getCurrentUserData()) {
                is ModernResult.Success -> {
                    val userData = result.data
                    if (userData != null) {
                        // 生成用户资料摘要，用于 AI Coach 上下文
                        val summary = buildString {
                            // 基本信息
                            append("用户: ${userData.getPrimaryIdentifier()}")
                            if (!userData.isAnonymous) {
                                append("（已认证用户）")
                            } else {
                                append("（匿名用户）")
                            }

                            // 身体数据
                            if (userData.hasBodyData) {
                                append("\n身体数据: ")
                                userData.height?.let { append("身高${it}cm ") }
                                userData.weight?.let { append("体重${it}kg") }
                                userData.getBmi()?.let { bmi ->
                                    append(" (BMI: ${"%.1f".format(bmi)})")
                                }
                                if (userData.gender != com.example.gymbro.domain.profile.model.user.enums.Gender.OTHER) {
                                    val genderDisplay = when (userData.gender) {
                                        com.example.gymbro.domain.profile.model.user.enums.Gender.MALE -> "男性"
                                        com.example.gymbro.domain.profile.model.user.enums.Gender.FEMALE -> "女性"
                                        com.example.gymbro.domain.profile.model.user.enums.Gender.OTHER -> "其他"
                                        com.example.gymbro.domain.profile.model.user.enums.Gender.PREFER_NOT_TO_SAY -> "不愿透露"
                                        com.example.gymbro.domain.profile.model.user.enums.Gender.UNSPECIFIED -> "未设置"
                                    }
                                    append(" 性别: $genderDisplay")
                                }
                            }

                            // 健身信息
                            if (userData.hasFitnessInfo) {
                                append("\n健身水平: ${userData.fitnessLevel.displayName}")
                                if (userData.fitnessGoals.isNotEmpty()) {
                                    append("\n健身目标: ${userData.getFormattedGoals()}")
                                }
                            }

                            // 训练计划
                            if (userData.workoutDays.isNotEmpty()) {
                                append("\n训练日: ${userData.getFormattedWorkoutDays()}")
                            }

                            // 活动统计
                            if (userData.totalActivityCount > 0 || userData.weeklyActiveMinutes > 0) {
                                append("\n活动统计: ")
                                if (userData.totalActivityCount > 0) {
                                    append("总训练次数${userData.totalActivityCount}次 ")
                                }
                                if (userData.weeklyActiveMinutes > 0) {
                                    append("周活跃时间${userData.weeklyActiveMinutes}分钟")
                                }
                            }

                            // 个人简介
                            if (!userData.bio.isNullOrBlank()) {
                                append("\n个人简介: ${userData.bio}")
                            }
                        }

                        val finalSummary = if (summary.isBlank()) null else summary
                        Timber.d("用户资料摘要生成成功: ${finalSummary?.length ?: 0} 字符")
                        ModernResult.Success(finalSummary)
                    } else {
                        Timber.d("用户未登录，无法生成资料摘要")
                        ModernResult.Success(null)
                    }
                }
                is ModernResult.Error -> {
                    Timber.e("获取用户数据失败: ${result.error}")
                    ModernResult.Error(
                        ModernDataError(
                            operationName = "ProfileRepositoryImpl.getProfileSummary",
                            errorType = GlobalErrorType.Database.QueryFailed,
                            category = ErrorCategory.DATA,
                            uiMessage = UiText.DynamicString("获取用户数据失败，无法生成资料摘要"),
                            severity = ErrorSeverity.ERROR,
                            cause = Exception(result.error.toString()),
                        ),
                    )
                }
                is ModernResult.Loading -> {
                    Timber.d("用户数据加载中")
                    ModernResult.Success(null)
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "生成用户资料摘要时发生异常")
            ModernResult.Error(
                ModernDataError(
                    operationName = "ProfileRepositoryImpl.getProfileSummary",
                    errorType = GlobalErrorType.Database.QueryFailed,
                    category = ErrorCategory.DATA,
                    uiMessage = UiText.DynamicString("生成用户资料摘要失败"),
                    severity = ErrorSeverity.ERROR,
                    cause = e,
                ),
            )
        }
}

package com.example.gymbro.domain.coach.executor.modules

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.executor.FunctionCall
import com.example.gymbro.domain.coach.executor.FunctionResult
import com.example.gymbro.domain.exercise.repository.ExerciseRepository
import com.example.gymbro.domain.exercise.repository.HybridSearchRepository
import com.example.gymbro.domain.workout.model.WorkoutAction
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 动作库域模块
 *
 * 负责处理动作库相关的Function Call请求
 * 包括动作搜索、详情获取、用户自定义动作创建等功能
 *
 * 核心功能：
 * 1. 动作搜索：支持关键词搜索和多维度筛选
 * 2. 动作详情：根据ID获取完整动作信息
 * 3. 动作创建：用户自定义动作的创建和更新
 *
 * 设计原则：
 * - 只提供读写基础功能，删除操作由用户执行
 * - 返回AI专用的轻量级数据格式
 * - 支持增量搜索和缓存优化
 * - 严格的参数验证和错误处理
 *
 * @property exerciseRepository 动作库Repository
 * @property aiExerciseSearchRepository AI专用搜索Repository
 * @property jsonCodec JSON编解码器
 * @property ioDispatcher IO调度器
 * @property logger 日志记录器
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (P6阶段动作库Function Call链路)
 */
@Singleton
class ExerciseFunctionModule @Inject constructor(
    private val exerciseRepository: ExerciseRepository,
    private val aiExerciseSearchRepository: HybridSearchRepository,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
    private val logger: Logger,
) {

    /**
     * 处理动作库域的Function Call
     *
     * @param functionCall Function Call请求
     * @param onActionTrigger UI动作触发回调
     * @return 函数执行结果
     */
    suspend fun handle(
        functionCall: FunctionCall,
        onActionTrigger: ((WorkoutAction) -> Unit)? = null,
    ): ModernResult<FunctionResult> = safeCatch {
        logger.d("🏋️ 处理动作库函数: ${functionCall.name}")

        val result = when (functionCall.name) {
            "gymbro.exercise.search" -> handleSearch(functionCall.arguments)
            "gymbro.exercise.get_detail" -> handleGetDetail(functionCall.arguments)
            "gymbro.exercise.upsert" -> handleUpsert(functionCall.arguments, onActionTrigger)
            else -> ModernResult.Success(
                FunctionResult(
                    success = false,
                    error = "未知的动作库函数: ${functionCall.name}",
                ),
            )
        }

        when (result) {
            is ModernResult.Success -> result.data
            is ModernResult.Error -> FunctionResult(
                success = false,
                error = "执行失败: ${result.error}",
            )
            is ModernResult.Loading -> FunctionResult(
                success = false,
                error = "执行超时，请稍后重试",
            )
        }
    }

    /**
     * 处理动作搜索
     * 支持关键词搜索和多维度筛选
     */
    private suspend fun handleSearch(
        arguments: Map<String, String>,
    ): ModernResult<FunctionResult> = safeCatch {
        withContext(ioDispatcher) {
            // 参数验证
            val query = arguments["query"]
            if (query.isNullOrBlank()) {
                return@withContext FunctionResult(
                    success = false,
                    error = "搜索关键词不能为空",
                )
            }

            val topK = arguments["top_k"]?.toIntOrNull() ?: 8
            val filters = parseSearchFilters(arguments["filters"])

            logger.d("动作搜索: query='$query', topK=$topK, filters=$filters")

            // 使用混合搜索Repository
            try {
                val exercises = aiExerciseSearchRepository.hybridSearch(
                    query = query,
                    limit = topK,
                )

                // 转换为Function Call返回格式
                val responseData = mapOf(
                    "exercises" to exercises.map { exercise ->
                        mapOf(
                            "id" to exercise.id,
                            "name" to exercise.name,
                            "muscleGroup" to exercise.muscleGroup.toString(),
                            "equipment" to exercise.equipment.toString(),
                            "difficulty" to exercise.difficultyLevel.toString(),
                        )
                    },
                    "source" to "function_call",
                    "query" to query,
                    "resultCount" to exercises.size,
                )

                val responseJson = Json.encodeToString(responseData)

                return@withContext FunctionResult(
                    success = true,
                    data = responseJson,
                    metadata = mapOf(
                        "result_count" to exercises.size,
                        "query" to query,
                        "search_type" to "hybrid_search",
                        "filters_applied" to (filters != null),
                    ),
                )
            } catch (e: Exception) {
                logger.e("动作搜索失败", e)
                return@withContext FunctionResult(
                    success = false,
                    error = "搜索失败: ${e.message}",
                )
            }
        }
    }

    /**
     * 处理动作详情获取
     * 根据动作ID获取完整信息
     */
    private suspend fun handleGetDetail(
        arguments: Map<String, String>,
    ): ModernResult<FunctionResult> = safeCatch {
        withContext(ioDispatcher) {
            // 参数验证
            val exerciseId = arguments["exercise_id"]
            if (exerciseId.isNullOrBlank()) {
                return@withContext FunctionResult(
                    success = false,
                    error = "动作ID不能为空",
                )
            }

            val includeMedia = arguments["include_media"]?.toBooleanStrictOrNull() ?: false
            val includeInstructions = arguments["include_instructions"]?.toBooleanStrictOrNull() ?: true

            logger.d(
                "获取动作详情: id='$exerciseId', includeMedia=$includeMedia, includeInstructions=$includeInstructions",
            )

            try {
                // 从Repository获取动作详情
                val exerciseResult = exerciseRepository.getExerciseById(exerciseId)

                when (exerciseResult) {
                    is ModernResult.Success -> {
                        val exercise = exerciseResult.data

                        // 构建详情数据
                        val detailData = mutableMapOf<String, Any>(
                            "id" to exercise.id,
                            "name" to exercise.name,
                            "muscleGroup" to exercise.muscleGroup.toString(),
                            "equipment" to exercise.equipment.toString(),
                            "difficulty" to exercise.difficultyLevel.toString(),
                            "description" to (exercise.description ?: ""),
                        )

                        // 可选字段
                        if (includeInstructions && exercise.instructions.isNotEmpty()) {
                            detailData["instructions"] = exercise.instructions
                        }

                        if (includeMedia) {
                            exercise.imageUrl?.let { detailData["imageUrl"] = it }
                            exercise.videoUrl?.let { detailData["videoUrl"] = it }
                        }

                        val responseJson = Json.encodeToString(detailData)

                        return@withContext FunctionResult(
                            success = true,
                            data = responseJson,
                            metadata = mapOf(
                                "exercise_id" to exerciseId,
                                "include_media" to includeMedia,
                                "include_instructions" to includeInstructions,
                            ),
                        )
                    }
                    is ModernResult.Error -> {
                        logger.e("获取动作详情失败: ${exerciseResult.error}")
                        return@withContext FunctionResult(
                            success = false,
                            error = "获取动作详情失败: ${exerciseResult.error}",
                        )
                    }
                    is ModernResult.Loading -> {
                        return@withContext FunctionResult(
                            success = false,
                            error = "获取动作详情超时，请稍后重试",
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("获取动作详情时发生异常", e)
                return@withContext FunctionResult(
                    success = false,
                    error = "获取动作详情失败: ${e.message}",
                )
            }
        }
    }

    /**
     * 处理用户自定义动作创建/更新
     * 🔥 修复：实际调用Repository进行数据库写入
     */
    private suspend fun handleUpsert(
        arguments: Map<String, String>,
        onActionTrigger: ((WorkoutAction) -> Unit)?,
    ): ModernResult<FunctionResult> = safeCatch {
        withContext(ioDispatcher) {
            // 参数验证
            val exerciseDataJson = arguments["exercise_data"]
            if (exerciseDataJson.isNullOrBlank()) {
                return@withContext FunctionResult(
                    success = false,
                    error = "动作数据不能为空",
                )
            }

            try {
                // 解析动作数据
                val exerciseData = Json.parseToJsonElement(exerciseDataJson).jsonObject

                // 基础验证
                val name = exerciseData["name"]?.jsonPrimitive?.content
                val muscleGroupStr = exerciseData["muscle_group"]?.jsonPrimitive?.content
                val equipmentStr = exerciseData["equipment"]?.jsonPrimitive?.content
                val difficultyStr = exerciseData["difficulty"]?.jsonPrimitive?.content
                val description = exerciseData["description"]?.jsonPrimitive?.content
                val instructions = exerciseData["instructions"]?.jsonPrimitive?.content

                if (name.isNullOrBlank() || muscleGroupStr.isNullOrBlank()) {
                    return@withContext FunctionResult(
                        success = false,
                        error = "动作名称和肌群是必填字段",
                    )
                }

                logger.d("🔥 开始创建用户自定义动作: name='$name', muscleGroup='$muscleGroupStr'")

                // 🔥 构建Exercise对象 - 使用正确的domain.exercise.model.Exercise
                val exercise = com.example.gymbro.domain.exercise.model.Exercise(
                    id = generateExerciseId(name), // 生成唯一ID
                    name = UiText.DynamicString(name),
                    muscleGroup = parseMuscleGroup(muscleGroupStr),
                    equipment = listOf(parseEquipment(equipmentStr)),
                    description = UiText.DynamicString(description ?: ""),
                    instructions = listOf(UiText.DynamicString(instructions ?: "")),
                    isCustom = true,
                    createdByUserId = getCurrentUserId(), // 🔥 修复：获取真实用户ID
                    difficultyLevel = parseDifficultyLevel(difficultyStr),
                    createdAt = System.currentTimeMillis(),
                    updatedAt = System.currentTimeMillis(),
                )

                // 🔥 实际调用Repository进行数据库写入
                val createResult = exerciseRepository.createCustomExercise(exercise)

                when (createResult) {
                    is ModernResult.Success -> {
                        logger.d("✅ 用户自定义动作创建成功: ${createResult.data.name}")

                        // 触发UI动作（如果需要）
                        onActionTrigger?.invoke(WorkoutAction.CreateCustomExercise)

                        FunctionResult(
                            success = true,
                            data = "用户自定义动作创建成功: ${createResult.data.name}",
                            actionTriggered = "CreateCustomExercise",
                            metadata = mapOf(
                                "exercise_id" to createResult.data.id,
                                "exercise_name" to createResult.data.name,
                                "muscle_group" to createResult.data.muscleGroup.toString(),
                                "operation" to "create",
                                "created_at" to createResult.data.createdAt.toString(),
                            ),
                            executionPath = "exercise_upsert_success",
                            resourceId = createResult.data.id, // 🔥 新增：返回创建的动作ID
                            resourceType = "exercise", // 🔥 新增：资源类型
                        )
                    }
                    is ModernResult.Error -> {
                        logger.e("❌ 用户自定义动作创建失败: ${createResult.error}")
                        FunctionResult(
                            success = false,
                            error = "动作创建失败: ${createResult.error.message}",
                        )
                    }
                    is ModernResult.Loading -> {
                        FunctionResult(
                            success = false,
                            error = "动作创建超时，请稍后重试",
                        )
                    }
                }
            } catch (e: Exception) {
                logger.e("解析动作数据失败", e)
                FunctionResult(
                    success = false,
                    error = "动作数据格式错误: ${e.message}",
                )
            }
        }
    }

    // ========== 🔥 新增：数据解析和辅助方法 ==========

    /**
     * 生成动作ID
     */
    private fun generateExerciseId(name: String): String {
        val timestamp = System.currentTimeMillis()
        val nameHash = name.hashCode().toString().takeLast(4)
        return "custom_${timestamp}_$nameHash"
    }

    /**
     * 获取当前用户ID
     * 🔥 修复：使用真实的用户认证系统获取用户ID
     */
    private suspend fun getCurrentUserId(): String {
        return try {
            val userIdResult = getCurrentUserIdUseCase().firstOrNull()
            when (userIdResult) {
                is ModernResult.Success -> {
                    val userId = userIdResult.data
                    if (!userId.isNullOrBlank()) {
                        logger.d("✅ 获取到真实用户ID: $userId")
                        userId
                    } else {
                        logger.w("⚠️ 用户ID为空，使用默认值")
                        "user_default"
                    }
                }
                is ModernResult.Error -> {
                    logger.e("❌ 获取用户ID失败: ${userIdResult.error.message}")
                    "user_default"
                }
                is ModernResult.Loading -> {
                    logger.w("⏳ 获取用户ID超时，使用默认值")
                    "user_default"
                }
                null -> {
                    logger.w("⚠️ 用户ID结果为null，使用默认值")
                    "user_default"
                }
            }
        } catch (e: Exception) {
            logger.e("💥 获取用户ID异常: ${e.message}")
            "user_default"
        }
    }

    /**
     * 解析肌群枚举 - 使用 SharedMuscleGroup
     */
    private fun parseMuscleGroup(
        muscleGroupStr: String,
    ): com.example.gymbro.shared.models.exercise.MuscleGroup {
        return try {
            when (muscleGroupStr.uppercase()) {
                "CHEST", "胸部" -> com.example.gymbro.shared.models.exercise.MuscleGroup.CHEST
                "BACK", "背部" -> com.example.gymbro.shared.models.exercise.MuscleGroup.BACK
                "SHOULDERS", "肩部" -> com.example.gymbro.shared.models.exercise.MuscleGroup.SHOULDERS
                "ARMS", "手臂" -> com.example.gymbro.shared.models.exercise.MuscleGroup.ARMS
                "LEGS", "腿部" -> com.example.gymbro.shared.models.exercise.MuscleGroup.LEGS
                "CORE", "核心" -> com.example.gymbro.shared.models.exercise.MuscleGroup.CORE
                "CARDIO", "有氧" -> com.example.gymbro.shared.models.exercise.MuscleGroup.CARDIO
                "FULL_BODY", "全身" -> com.example.gymbro.shared.models.exercise.MuscleGroup.FULL_BODY
                else -> com.example.gymbro.shared.models.exercise.MuscleGroup.OTHER // 默认值
            }
        } catch (e: Exception) {
            logger.w("解析肌群失败，使用默认值: $muscleGroupStr")
            com.example.gymbro.shared.models.exercise.MuscleGroup.OTHER
        }
    }

    /**
     * 解析器械枚举 - 使用 SharedEquipment
     */
    private fun parseEquipment(equipmentStr: String?): com.example.gymbro.shared.models.exercise.Equipment {
        if (equipmentStr.isNullOrBlank()) return com.example.gymbro.shared.models.exercise.Equipment.NONE

        return try {
            when (equipmentStr.uppercase()) {
                "BARBELL", "杠铃" -> com.example.gymbro.shared.models.exercise.Equipment.BARBELL
                "DUMBBELL", "哑铃" -> com.example.gymbro.shared.models.exercise.Equipment.DUMBBELL
                "CABLE", "拉索" -> com.example.gymbro.shared.models.exercise.Equipment.CABLE
                "MACHINE", "器械" -> com.example.gymbro.shared.models.exercise.Equipment.MACHINE
                "BODYWEIGHT", "自重" -> com.example.gymbro.shared.models.exercise.Equipment.NONE
                "KETTLEBELL", "壶铃" -> com.example.gymbro.shared.models.exercise.Equipment.KETTLEBELL
                "RESISTANCE_BAND", "弹力带" -> com.example.gymbro.shared.models.exercise.Equipment.RESISTANCE_BAND
                else -> com.example.gymbro.shared.models.exercise.Equipment.NONE // 默认值
            }
        } catch (e: Exception) {
            logger.w("解析器械失败，使用默认值: $equipmentStr")
            com.example.gymbro.shared.models.exercise.Equipment.NONE
        }
    }

    /**
     * 解析难度等级 - 返回 Int 类型
     */
    private fun parseDifficultyLevel(difficultyStr: String?): Int {
        if (difficultyStr.isNullOrBlank()) return 2 // 中级

        return try {
            when (difficultyStr.uppercase()) {
                "BEGINNER", "初级" -> 1
                "INTERMEDIATE", "中级" -> 2
                "ADVANCED", "高级" -> 3
                "EXPERT", "专家" -> 4
                else -> 2 // 默认中级
            }
        } catch (e: Exception) {
            logger.w("解析难度失败，使用默认值: $difficultyStr")
            2
        }
    }

    /**
     * 解析搜索筛选条件
     */
    private fun parseSearchFilters(filtersJson: String?): SearchFilters? {
        if (filtersJson.isNullOrBlank()) return null

        return try {
            val filtersObject = Json.parseToJsonElement(filtersJson).jsonObject

            SearchFilters(
                muscleGroup = filtersObject["muscle_group"]?.jsonPrimitive?.content,
                equipment = filtersObject["equipment"]?.jsonPrimitive?.content,
                difficulty = filtersObject["difficulty"]?.jsonPrimitive?.content,
                source = filtersObject["source"]?.jsonPrimitive?.content,
            )
        } catch (e: Exception) {
            logger.w("解析搜索筛选条件失败", e)
            null
        }
    }
}

/**
 * 搜索筛选条件数据类
 */
data class SearchFilters(
    val muscleGroup: String? = null,
    val equipment: String? = null,
    val difficulty: String? = null,
    val source: String? = null,
)

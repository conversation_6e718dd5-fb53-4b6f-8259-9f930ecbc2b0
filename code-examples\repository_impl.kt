package com.example.gymbro.examples

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository层实现标准示例
 * 
 * 本示例展示了GymBro项目中Data层Repository的标准实现模式：
 * 1. 使用Timber进行日志记录（Data层标准）
 * 2. 返回ModernResult包装的结果
 * 3. 完整的错误处理机制
 * 4. Room数据库集成
 * 5. 网络API集成
 * 6. ≥80%测试覆盖率要求
 * 
 * 参考标准：
 * - Data层使用Timber，不使用Logger接口
 * - 所有操作返回ModernResult<T>
 * - 支持响应式数据流
 * - 统一错误处理
 */

// ================================
// 1. Repository实现
// ================================

/**
 * 用户Repository实现
 * 
 * 标准Repository实现，整合本地数据库和网络API
 */
@Singleton
class UserRepositoryImpl @Inject constructor(
    private val userDao: UserDao,
    private val userApi: UserApi,
    private val errorHandler: CoreErrorHandlingUtils
) : UserRepository {
    
    override suspend fun getUserData(userId: String, includeProfile: Boolean): ModernResult<UserData> {
        Timber.d("获取用户数据: $userId, includeProfile: $includeProfile")
        
        return try {
            // 1. 先从本地数据库获取
            val localResult = getUserFromLocal(userId)
            
            // 2. 如果本地没有数据或需要刷新，从网络获取
            if (localResult is ModernResult.Error || shouldRefreshData(localResult)) {
                Timber.d("从网络获取用户数据")
                val networkResult = getUserFromNetwork(userId, includeProfile)
                
                // 3. 网络数据成功时保存到本地
                if (networkResult is ModernResult.Success) {
                    saveUserToLocal(networkResult.data)
                }
                
                networkResult
            } else {
                Timber.d("使用本地缓存数据")
                localResult
            }
        } catch (e: Exception) {
            Timber.e(e, "获取用户数据异常")
            ModernResult.Error(e.toModernDataError("getUserData"))
        }
    }
    
    override suspend fun updateUserProfile(userId: String, profile: UserProfile): ModernResult<UserProfile> {
        Timber.d("更新用户资料: $userId")
        
        return try {
            // 1. 先更新网络
            val networkResult = updateProfileOnNetwork(userId, profile)
            
            when (networkResult) {
                is ModernResult.Success -> {
                    // 2. 网络更新成功后更新本地
                    updateProfileInLocal(userId, networkResult.data)
                    Timber.d("用户资料更新成功")
                    networkResult
                }
                is ModernResult.Error -> {
                    Timber.e("网络更新用户资料失败: ${networkResult.error.operationName}")
                    networkResult
                }
                is ModernResult.Loading -> networkResult
            }
        } catch (e: Exception) {
            Timber.e(e, "更新用户资料异常")
            ModernResult.Error(e.toModernDataError("updateUserProfile"))
        }
    }
    
    override fun observeUserData(userId: String): Flow<ModernResult<UserData>> {
        Timber.d("开始观察用户数据: $userId")
        
        return userDao.observeUser(userId)
            .map { userEntity ->
                try {
                    if (userEntity != null) {
                        val userData = userEntity.toDomainModel()
                        ModernResult.Success(userData)
                    } else {
                        ModernResult.Error(createNotFoundError("observeUserData"))
                    }
                } catch (e: Exception) {
                    Timber.e(e, "用户数据转换异常")
                    ModernResult.Error(e.toModernDataError("observeUserData"))
                }
            }
    }
    
    // ================================
    // 私有辅助方法
    // ================================
    
    private suspend fun getUserFromLocal(userId: String): ModernResult<UserData> {
        return try {
            val userEntity = userDao.getUserById(userId)
            if (userEntity != null) {
                ModernResult.Success(userEntity.toDomainModel())
            } else {
                ModernResult.Error(createNotFoundError("getUserFromLocal"))
            }
        } catch (e: Exception) {
            Timber.e(e, "本地获取用户数据异常")
            ModernResult.Error(e.toModernDataError("getUserFromLocal"))
        }
    }
    
    private suspend fun getUserFromNetwork(userId: String, includeProfile: Boolean): ModernResult<UserData> {
        return try {
            val response = userApi.getUser(userId, includeProfile)
            if (response.isSuccessful && response.body() != null) {
                val userData = response.body()!!.toDomainModel()
                ModernResult.Success(userData)
            } else {
                ModernResult.Error(createNetworkError("getUserFromNetwork", response.code()))
            }
        } catch (e: Exception) {
            Timber.e(e, "网络获取用户数据异常")
            ModernResult.Error(e.toModernDataError("getUserFromNetwork"))
        }
    }
    
    private suspend fun updateProfileOnNetwork(userId: String, profile: UserProfile): ModernResult<UserProfile> {
        return try {
            val request = profile.toNetworkModel()
            val response = userApi.updateProfile(userId, request)
            
            if (response.isSuccessful && response.body() != null) {
                val updatedProfile = response.body()!!.toDomainModel()
                ModernResult.Success(updatedProfile)
            } else {
                ModernResult.Error(createNetworkError("updateProfileOnNetwork", response.code()))
            }
        } catch (e: Exception) {
            Timber.e(e, "网络更新用户资料异常")
            ModernResult.Error(e.toModernDataError("updateProfileOnNetwork"))
        }
    }
    
    private suspend fun saveUserToLocal(userData: UserData) {
        try {
            val userEntity = userData.toEntity()
            userDao.insertOrUpdate(userEntity)
            Timber.d("用户数据已保存到本地")
        } catch (e: Exception) {
            Timber.e(e, "保存用户数据到本地异常")
        }
    }
    
    private suspend fun updateProfileInLocal(userId: String, profile: UserProfile) {
        try {
            userDao.updateProfile(userId, profile.toEntity())
            Timber.d("本地用户资料已更新")
        } catch (e: Exception) {
            Timber.e(e, "更新本地用户资料异常")
        }
    }
    
    private fun shouldRefreshData(result: ModernResult<UserData>): Boolean {
        if (result !is ModernResult.Success) return true
        
        val userData = result.data
        val cacheAge = System.currentTimeMillis() - userData.lastUpdated
        val maxCacheAge = 5 * 60 * 1000L // 5分钟
        
        return cacheAge > maxCacheAge
    }
    
    // ================================
    // 错误处理辅助方法
    // ================================
    
    private fun createNotFoundError(operationName: String): com.example.gymbro.core.error.types.ModernDataError {
        return com.example.gymbro.core.error.types.ModernDataError(
            operationName = operationName,
            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Data.NotFound,
            category = com.example.gymbro.core.error.types.ErrorCategory.DATA,
            uiMessage = UiText.DynamicString("用户数据不存在")
        )
    }
    
    private fun createNetworkError(operationName: String, code: Int): com.example.gymbro.core.error.types.ModernDataError {
        return com.example.gymbro.core.error.types.ModernDataError(
            operationName = operationName,
            errorType = com.example.gymbro.core.error.types.GlobalErrorType.Network.ServerError,
            category = com.example.gymbro.core.error.types.ErrorCategory.NETWORK,
            uiMessage = UiText.DynamicString("网络请求失败 (错误码: $code)")
        )
    }
}

// ================================
// 2. 数据访问接口
// ================================

/**
 * 用户DAO接口
 */
interface UserDao {
    suspend fun getUserById(userId: String): UserEntity?
    suspend fun insertOrUpdate(user: UserEntity)
    suspend fun updateProfile(userId: String, profile: UserProfileEntity)
    fun observeUser(userId: String): Flow<UserEntity?>
}

/**
 * 用户API接口
 */
interface UserApi {
    suspend fun getUser(userId: String, includeProfile: Boolean): retrofit2.Response<UserResponse>
    suspend fun updateProfile(userId: String, request: UpdateProfileRequest): retrofit2.Response<UserProfileResponse>
}

/**
 * 错误处理工具接口
 */
interface CoreErrorHandlingUtils {
    fun handleApiError(throwable: Throwable): com.example.gymbro.core.error.types.ModernDataError
    fun handleDatabaseError(throwable: Throwable): com.example.gymbro.core.error.types.ModernDataError
}

// ================================
// 3. 数据模型
// ================================

/**
 * 用户实体（Room）
 */
data class UserEntity(
    val userId: String,
    val displayName: String,
    val email: String,
    val bio: String,
    val avatarUrl: String?,
    val theme: String,
    val notifications: Boolean,
    val privacy: String,
    val lastUpdated: Long
)

/**
 * 用户资料实体（Room）
 */
data class UserProfileEntity(
    val displayName: String,
    val email: String,
    val bio: String,
    val avatarUrl: String?
)

/**
 * 网络响应模型
 */
data class UserResponse(
    val userId: String,
    val profile: UserProfileResponse,
    val settings: UserSettingsResponse
)

data class UserProfileResponse(
    val displayName: String,
    val email: String,
    val bio: String,
    val avatarUrl: String?
)

data class UserSettingsResponse(
    val theme: String,
    val notifications: Boolean,
    val privacy: String
)

/**
 * 网络请求模型
 */
data class UpdateProfileRequest(
    val displayName: String,
    val email: String,
    val bio: String,
    val avatarUrl: String?
)

// ================================
// 4. 扩展函数 - 数据转换
// ================================

/**
 * 实体转换为领域模型
 */
private fun UserEntity.toDomainModel(): UserData {
    return UserData(
        userId = this.userId,
        profile = UserProfile(
            displayName = this.displayName,
            email = this.email,
            bio = this.bio,
            avatarUrl = this.avatarUrl
        ),
        settings = UserSettings(
            theme = this.theme,
            notifications = this.notifications,
            privacy = this.privacy
        ),
        lastUpdated = this.lastUpdated
    )
}

/**
 * 领域模型转换为实体
 */
private fun UserData.toEntity(): UserEntity {
    return UserEntity(
        userId = this.userId,
        displayName = this.profile.displayName,
        email = this.profile.email,
        bio = this.profile.bio,
        avatarUrl = this.profile.avatarUrl,
        theme = this.settings.theme,
        notifications = this.settings.notifications,
        privacy = this.settings.privacy,
        lastUpdated = this.lastUpdated
    )
}

/**
 * 网络响应转换为领域模型
 */
private fun UserResponse.toDomainModel(): UserData {
    return UserData(
        userId = this.userId,
        profile = this.profile.toDomainModel(),
        settings = this.settings.toDomainModel(),
        lastUpdated = System.currentTimeMillis()
    )
}

private fun UserProfileResponse.toDomainModel(): UserProfile {
    return UserProfile(
        displayName = this.displayName,
        email = this.email,
        bio = this.bio,
        avatarUrl = this.avatarUrl
    )
}

private fun UserSettingsResponse.toDomainModel(): UserSettings {
    return UserSettings(
        theme = this.theme,
        notifications = this.notifications,
        privacy = this.privacy
    )
}

/**
 * 领域模型转换为网络请求
 */
private fun UserProfile.toNetworkModel(): UpdateProfileRequest {
    return UpdateProfileRequest(
        displayName = this.displayName,
        email = this.email,
        bio = this.bio,
        avatarUrl = this.avatarUrl
    )
}

private fun UserProfile.toEntity(): UserProfileEntity {
    return UserProfileEntity(
        displayName = this.displayName,
        email = this.email,
        bio = this.bio,
        avatarUrl = this.avatarUrl
    )
}

/**
 * 异常转换为ModernDataError
 */
private fun Exception.toModernDataError(operationName: String): com.example.gymbro.core.error.types.ModernDataError {
    return com.example.gymbro.core.error.types.ModernDataError(
        operationName = operationName,
        errorType = com.example.gymbro.core.error.types.GlobalErrorType.System.General,
        category = com.example.gymbro.core.error.types.ErrorCategory.SYSTEM,
        uiMessage = UiText.DynamicString(this.message ?: "操作失败"),
        cause = this
    )
}

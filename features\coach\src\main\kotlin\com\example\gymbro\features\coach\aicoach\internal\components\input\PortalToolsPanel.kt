package com.example.gymbro.features.coach.aicoach.internal.components.input

import androidx.compose.animation.*
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Help
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.zIndex
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.shared.models.coach.QuickSuggestion

// 转换函数：将QuickSuggestion转换为GymBroTool
private fun QuickSuggestion.toGymBroTool(): GymBroTool {
    return GymBroTool(
        id = this.title.replace(" ", "_").lowercase(),
        icon = Icons.AutoMirrored.Filled.Help, // 默认图标
        title = this.title,
        subtitle = this.prompt.take(20) + if (this.prompt.length > 20) "..." else "",
        functionCallName = this.title.replace(" ", "_").lowercase(),
    )
}

/**
 * 🚀 Portal层级工具面板 - ChatGPT风格完美覆盖
 *
 * 🎯 设计哲学：
 * - Portal架构：在最高层级渲染，确保覆盖所有UI元素
 * - 简洁设计：无遮罩，纯净的底部弹出效果
 * - 底部弹出：从底部slide up的自然动画
 * - 完美覆盖：覆盖包括input在内的所有区域
 * - 性能优先：优化动画避免掉帧
 *
 * 🔧 技术特性：
 * - 最高Z-index确保层级优先级
 * - 纯净的底部弹出动画
 * - 300ms标准动画时长
 * - remember缓存优化性能
 * - 简洁的ChatGPT风格
 *
 * 🚨 架构优势：
 * - 完全独立的Portal组件
 * - 与input container解耦
 * - 支持任意位置调用
 * - MVI架构状态驱动
 */

/**
 * 🚀 Portal工具面板主组件
 *
 * 此组件应该放置在Screen级别的最外层Container中，
 * 确保能够覆盖包括input在内的所有UI元素
 */
@Composable
internal fun PortalToolsPanel(
    isVisible: Boolean,
    tools: List<QuickSuggestion>,
    onToolClick: (String) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    // 🔥 性能优化：记忆化回调
    val stableOnToolClick = remember(onToolClick) { onToolClick }
    val stableOnDismiss = remember(onDismiss) { onDismiss }

    // 🎯 Portal容器 - 最高层级渲染
    AnimatedVisibility(
        visible = isVisible,
        enter = fadeIn(
            animationSpec = tween(
                durationMillis = 300,
                easing = FastOutSlowInEasing,
            ),
        ),
        exit = fadeOut(
            animationSpec = tween(
                durationMillis = 250,
                easing = FastOutSlowInEasing,
            ),
        ),
        modifier = modifier
            .fillMaxSize()
            .zIndex(1000f), // 🔥 确保最高层级
    ) {
        PortalContainer(
            tools = tools,
            onToolClick = stableOnToolClick,
            onDismiss = stableOnDismiss,
        )
    }
}

/**
 * 🎨 Portal容器实现
 */
@Composable
private fun PortalContainer(
    tools: List<QuickSuggestion>,
    onToolClick: (String) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier.fillMaxSize(),
    ) {
        // 🎯 透明背景遮罩 - 支持点击外部退出
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null, // 无点击波纹效果
                ) {
                    onDismiss() // 点击外部区域关闭面板
                },
        )

        // 🎯 底部工具面板 - 简洁的ChatGPT风格
        PortalBottomPanel(
            tools = tools,
            onToolClick = onToolClick,
            onDismiss = onDismiss,
            modifier = Modifier.align(Alignment.BottomCenter),
        )
    }
}

// 🚀 Portal背景遮罩已移除 - 用户要求简洁设计，无遮罩效果

/**
 * 🎨 Portal底部面板容器
 */
@Composable
private fun PortalBottomPanel(
    tools: List<QuickSuggestion>,
    onToolClick: (String) -> Unit,
    onDismiss: () -> Unit = {},
    modifier: Modifier = Modifier,
) {
    // 🔥 底部弹出动画 - 精确的slide up效果
    AnimatedVisibility(
        visible = true, // 由父级AnimatedVisibility控制
        enter = slideInVertically(
            initialOffsetY = { fullHeight -> fullHeight }, // 从底部完全滑入
            animationSpec = tween(
                durationMillis = 300,
                delayMillis = 50, // 🔥 轻微延迟，让遮罩先显示
                easing = FastOutSlowInEasing,
            ),
        ),
        exit = slideOutVertically(
            targetOffsetY = { fullHeight -> fullHeight }, // 向底部完全滑出
            animationSpec = tween(
                durationMillis = 250,
                easing = FastOutSlowInEasing,
            ),
        ),
        modifier = modifier.fillMaxWidth(),
    ) {
        // 🎯 使用现有的ChatGPT风格面板组件
        ChatGPTStyleToolsPanel(
            isVisible = true,
            tools = tools.map { it.toGymBroTool() }, // 转换为GymBroTool类型
            onToolClick = onToolClick,
            modifier = Modifier.fillMaxWidth(),
        )
    }
}

/**
 * 🎨 扩展函数：简化Portal调用
 */
@Composable
internal fun AiCoachContract.State.RenderPortalToolsIfNeeded(
    onToolClick: (String) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (inputState.isToolbarExpanded) {
        PortalToolsPanel(
            isVisible = true,
            tools = suggestionConfig?.quickSuggestions ?: emptyList(), // 使用状态中的快速建议
            onToolClick = onToolClick,
            onDismiss = onDismiss,
            modifier = modifier,
        )
    }
}

// ==================== 预览组件 ====================

@GymBroPreview
@Composable
private fun PortalToolsPanelPreview() {
    GymBroTheme {
        Box(
            modifier = Modifier.fillMaxSize(),
        ) {
            // Portal工具面板覆盖层
            PortalToolsPanel(
                isVisible = true,
                tools = listOf(
                    QuickSuggestion("示例建议", "这是一个示例建议"),
                ),
                onToolClick = { /* Preview */ },
                onDismiss = { /* Preview */ },
            )
        }
    }
}

@GymBroPreview
@Composable
private fun PortalContainerPreview() {
    GymBroTheme {
        PortalContainer(
            tools = listOf(
                QuickSuggestion("示例建议", "这是一个示例建议"),
            ),
            onToolClick = { /* Preview */ },
            onDismiss = { /* Preview */ },
        )
    }
}

@GymBroPreview
@Composable
private fun PortalToolsPanelDarkThemePreview() {
    GymBroTheme(darkTheme = true) {
        Box(
            modifier = Modifier.fillMaxSize(),
        ) {
            PortalToolsPanel(
                isVisible = true,
                tools = listOf(
                    QuickSuggestion("示例建议", "这是一个示例建议"),
                ),
                onToolClick = { /* Preview */ },
                onDismiss = { /* Preview */ },
            )
        }
    }
}

package com.example.gymbro.designSystem.components

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.SmartToy
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.designSystem.components.extensions.asString
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.GymBroTokenValidator
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.designSystem.theme.tokens.workoutColors

/**
 * Workout卡片类型枚举
 * 定义不同功能卡片的类型
 */
enum class WorkoutCardType {
    PRIMARY, // 主要动作卡片（开始训练）
    STANDARD, // 标准功能卡片
    AI_COACH, // AI Coach集成卡片
    STATUS, // 状态显示卡片（进行中训练）
    STATS, // 统计信息卡片
}

/**
 * Workout卡片状态枚举
 */
enum class WorkoutCardStatus {
    Default,
    Active,
    Completed,
    Disabled,
}

/**
 * Workout卡片数据模型
 */
@Stable
data class WorkoutCardData(
    val title: UiText,
    val subtitle: UiText? = null,
    val icon: ImageVector? = null,
    val badge: UiText? = null,
    val progress: Float? = null, // 0.0-1.0 for progress cards
    val status: WorkoutCardStatus = WorkoutCardStatus.Default,
    val actionText: UiText? = null,
)

/**
 * WorkoutUnifiedCard统一卡片组件
 * 根据白皮书v2.0设计的统一组件系统
 *
 * @param data 卡片数据
 * @param type 卡片类型
 * @param onClick 主要点击事件
 * @param modifier 修饰符
 * @param onSecondaryAction 次要操作点击事件（可选）
 */
@Composable
fun WorkoutUnifiedCard(
    data: WorkoutCardData,
    type: WorkoutCardType,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    onSecondaryAction: (() -> Unit)? = null,
) {
    // Token 使用验证 (仅在 DEBUG 模式下)
    GymBroTokenValidator.validateTokenUsage("WorkoutUnifiedCard")
    val colors =
        when (type) {
            WorkoutCardType.PRIMARY ->
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.workoutColors.accentPrimary,
                    contentColor = Color.White, // 使用白色确保在强调色背景上的可读性
                )
            WorkoutCardType.AI_COACH ->
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.workoutColors.aiCoachBackground,
                    contentColor = MaterialTheme.workoutColors.aiCoachText,
                )
            WorkoutCardType.STATUS ->
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.workoutColors.activeState.copy(alpha = 0.1f),
                    contentColor = MaterialTheme.workoutColors.activeState,
                )
            else ->
                CardDefaults.cardColors(
                    containerColor = MaterialTheme.workoutColors.cardBackground,
                    contentColor = MaterialTheme.workoutColors.accentSecondary, // 使用 workout 主题的次要文本颜色
                )
        }

    Card(
        onClick = onClick,
        modifier =
        modifier
            .fillMaxWidth()
            .animateContentSize(),
        // 自动内容大小动画
        colors = colors,
        border =
        BorderStroke(
            width = if (type == WorkoutCardType.AI_COACH) Tokens.Input.BorderWidthFocused else Tokens.Input.BorderWidth, // 2.dp/1.dp - 使用 Token
            color =
            if (type == WorkoutCardType.AI_COACH) {
                MaterialTheme.workoutColors.aiCoachPrimary
            } else {
                MaterialTheme.workoutColors.cardBorder
            },
        ),
        elevation =
        CardDefaults.cardElevation(
            defaultElevation =
            when (type) {
                WorkoutCardType.PRIMARY -> Tokens.Elevation.Large // 8.dp - 使用 Token
                WorkoutCardType.AI_COACH -> Tokens.Elevation.Medium // 6.dp - 使用 Token (接近值)
                else -> Tokens.Elevation.Small // 2.dp - 使用 Token
            },
        ),
    ) {
        WorkoutCardContent(
            data = data,
            type = type,
            onSecondaryAction = onSecondaryAction,
        )
    }
}

/**
 * WorkoutUnifiedCard内容组件
 */
@Composable
private fun WorkoutCardContent(
    data: WorkoutCardData,
    type: WorkoutCardType,
    onSecondaryAction: (() -> Unit)?,
) {
    Row(
        modifier =
        Modifier
            .fillMaxWidth()
            .padding(Tokens.Spacing.Medium),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
    ) {
        // 图标区域
        data.icon?.let { icon ->
            Icon(
                imageVector = icon,
                contentDescription = null,
                modifier =
                Modifier.size(
                    when (type) {
                        WorkoutCardType.PRIMARY -> Tokens.Icon.Large // 32.dp - 使用 Token
                        WorkoutCardType.AI_COACH -> Tokens.Icon.Medium // 28.dp - 使用接近的 Token (20.dp)
                        else -> Tokens.Icon.Standard // 24.dp - 使用 Token
                    },
                ),
                tint =
                when (type) {
                    WorkoutCardType.AI_COACH -> MaterialTheme.workoutColors.aiCoachPrimary
                    else -> LocalContentColor.current
                },
            )
        }

        // 主要内容区域
        Column(
            modifier = Modifier.weight(1f),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                Text(
                    text = data.title.asString(),
                    style =
                    when (type) {
                        WorkoutCardType.PRIMARY -> MaterialTheme.typography.titleLarge
                        else -> MaterialTheme.typography.titleMedium
                    },
                    fontWeight = if (type == WorkoutCardType.PRIMARY) FontWeight.Bold else FontWeight.Medium,
                )

                // 徽章
                data.badge?.let { badge ->
                    Badge(
                        containerColor = MaterialTheme.workoutColors.accentSecondary,
                    ) {
                        Text(
                            text = badge.asString(),
                            style = MaterialTheme.typography.labelSmall,
                        )
                    }
                }
            }

            // 副标题
            data.subtitle?.let { subtitle ->
                Text(
                    text = subtitle.asString(),
                    style = MaterialTheme.typography.bodyMedium,
                    color = LocalContentColor.current.copy(alpha = 0.7f),
                )
            }

            // 进度条（如果有）
            data.progress?.let { progress ->
                Spacer(modifier = Modifier.height(Tokens.Spacing.Small))
                LinearProgressIndicator(
                    progress = progress,
                    modifier = Modifier.fillMaxWidth(),
                    color =
                    when (type) {
                        WorkoutCardType.AI_COACH -> MaterialTheme.workoutColors.aiCoachPrimary
                        else -> MaterialTheme.workoutColors.accentPrimary
                    },
                )
            }
        }

        // 动作按钮区域
        if (onSecondaryAction != null && data.actionText != null) {
            OutlinedButton(
                onClick = onSecondaryAction,
                modifier = Modifier.wrapContentWidth(),
            ) {
                Text(data.actionText.asString())
            }
        }
    }
}

// Preview组件
@GymBroPreview
@Composable
private fun WorkoutUnifiedCardPreview() {
    GymBroTheme {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium), // 16.dp - 使用 Token
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium), // 16.dp - 使用 Token
        ) {
            // Primary卡片
            WorkoutUnifiedCard(
                data =
                WorkoutCardData(
                    title = UiText.DynamicString("开始训练"),
                    subtitle = UiText.DynamicString("开始新的训练会话"),
                    icon = Icons.Default.PlayArrow,
                ),
                type = WorkoutCardType.PRIMARY,
                onClick = {},
            )

            // AI Coach卡片
            WorkoutUnifiedCard(
                data =
                WorkoutCardData(
                    title = UiText.DynamicString("AI教练建议"),
                    subtitle = UiText.DynamicString("获取个性化训练方案"),
                    icon = Icons.Default.SmartToy,
                    badge = UiText.DynamicString("智能"),
                    actionText = UiText.DynamicString("快速生成"),
                ),
                type = WorkoutCardType.AI_COACH,
                onClick = {},
                onSecondaryAction = {},
            )

            // Status卡片（进行中训练）
            WorkoutUnifiedCard(
                data =
                WorkoutCardData(
                    title = UiText.DynamicString("继续训练"),
                    subtitle = UiText.DynamicString("会话进行中"),
                    icon = Icons.Default.PlayArrow,
                    progress = 0.65f,
                ),
                type = WorkoutCardType.STATUS,
                onClick = {},
            )

            // Standard卡片
            WorkoutUnifiedCard(
                data =
                WorkoutCardData(
                    title = UiText.DynamicString("训练模板"),
                    subtitle = UiText.DynamicString("管理训练模板"),
                    icon = Icons.Default.PlayArrow,
                ),
                type = WorkoutCardType.STANDARD,
                onClick = {},
            )
        }
    }
}

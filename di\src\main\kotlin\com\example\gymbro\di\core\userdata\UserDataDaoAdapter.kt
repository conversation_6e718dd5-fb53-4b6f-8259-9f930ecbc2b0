package com.example.gymbro.di.core.userdata

import com.example.gymbro.core.userdata.internal.dao.UserAuthData
import com.example.gymbro.core.userdata.internal.dao.UserDataDao
import com.example.gymbro.core.userdata.internal.dao.UserProfileData
import com.example.gymbro.data.local.dao.user.UserDao
import com.example.gymbro.data.local.entity.user.UserCacheEntity
import com.example.gymbro.data.local.entity.user.UserProfileEntity
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import javax.inject.Inject
import javax.inject.Singleton
import com.example.gymbro.core.userdata.internal.dao.UserProfileDao as CoreUserProfileDao
import com.example.gymbro.data.local.dao.user.UserProfileDao as DataUserProfileDao

/**
 * UserDataDao 适配器实现
 *
 * 将 data 模块的 UserDao 适配到 core-user-data-center 的抽象接口，
 * 解决循环依赖问题的同时保持真正的 Room 数据库持久化。
 */
@Singleton
class UserDataDaoAdapter @Inject constructor(
    private val userDao: UserDao,
) : UserDataDao {

    override fun observeCurrentUserAuth(): Flow<UserAuthData?> {
        return userDao.getCurrentUser().map { entity ->
            entity?.toUserAuthData()
        }
    }

    override fun observeUserAuth(userId: String): Flow<UserAuthData?> {
        return userDao.getUser(userId).map { entity ->
            entity?.toUserAuthData()
        }
    }

    override suspend fun getCurrentUserAuth(): UserAuthData? {
        return userDao.getCurrentUser().first()?.toUserAuthData()
    }

    override suspend fun getUserAuth(userId: String): UserAuthData? {
        return userDao.getUser(userId).first()?.toUserAuthData()
    }

    override suspend fun saveUserAuth(userAuth: UserAuthData) {
        val entity = userAuth.toUserCacheEntity()
        userDao.insertUser(entity)
    }

    override suspend fun updateUserAuth(userAuth: UserAuthData) {
        val entity = userAuth.toUserCacheEntity()
        userDao.updateUser(entity)
    }

    override suspend fun deleteAllUserAuth() {
        userDao.deleteUser()
    }

    override suspend fun deleteUserAuth(userId: String) {
        userDao.deleteUserById(userId)
    }

    override suspend fun userExists(userId: String): Boolean {
        return userDao.getUser(userId).first() != null
    }

    override suspend fun hasAnyUser(): Boolean {
        return userDao.hasUser()
    }

    // === 数据转换扩展方法 ===

    private fun UserCacheEntity.toUserAuthData(): UserAuthData {
        return UserAuthData(
            userId = this.userId,
            email = this.email,
            displayName = this.displayName,
            phoneNumber = this.phoneNumber,
            isAnonymous = this.anonymousId != null,
            isEmailVerified = this.isEmailVerified,
            isPhoneVerified = false, // UserCacheEntity 中没有此字段
            photoUrl = this.photoUrl,
            isActive = this.isActive,
            lastUpdated = this.lastModified, // 使用 lastModified 字段
            createdAt = this.createdAt ?: System.currentTimeMillis(), // 处理可空类型
        )
    }

    private fun UserAuthData.toUserCacheEntity(): UserCacheEntity {
        return UserCacheEntity(
            userId = this.userId,
            email = this.email,
            username = this.displayName ?: "用户${this.userId.take(8)}",
            displayName = this.displayName,
            photoUrl = this.photoUrl,
            phoneNumber = this.phoneNumber,
            isActive = this.isActive,
            isEmailVerified = this.isEmailVerified,
            // 其他字段使用默认值
            wechatId = null,
            anonymousId = if (this.isAnonymous) this.userId else null,
            gender = null,
            weight = null,
            weightUnit = null,
            fitnessLevel = null,
            preferredGym = null,
            avatar = this.photoUrl,
            bio = null,
            lastModified = this.lastUpdated, // 使用 lastModified 字段
            createdAt = this.createdAt,
        )
    }
}

/**
 * UserProfileDao 适配器实现
 *
 * 将 data 模块的 UserProfileDao 适配到 core-user-data-center 的抽象接口。
 */
@Singleton
class UserProfileDaoAdapter @Inject constructor(
    private val userProfileDao: DataUserProfileDao,
) : CoreUserProfileDao {

    override fun observeAllUserProfiles(): Flow<List<UserProfileData>> {
        return userProfileDao.getAllUserProfiles().map { entities ->
            entities.map { it.toUserProfileData() }
        }
    }

    override fun observeUserProfile(userId: String): Flow<UserProfileData?> {
        return userProfileDao.getUserProfile(userId).map { entity ->
            entity?.toUserProfileData()
        }
    }

    override suspend fun getUserProfile(userId: String): UserProfileData? {
        return userProfileDao.getUserProfileSync(userId)?.toUserProfileData()
    }

    override suspend fun saveUserProfile(userProfile: UserProfileData) {
        val entity = userProfile.toUserProfileEntity()
        userProfileDao.insertOrUpdateUserProfile(entity)
    }

    override suspend fun updateUserProfile(userProfile: UserProfileData) {
        val entity = userProfile.toUserProfileEntity()
        userProfileDao.updateUserProfile(entity)
    }

    override suspend fun deleteAllUserProfiles() {
        userProfileDao.clearAllUserProfiles()
    }

    override suspend fun deleteUserProfile(userId: String) {
        userProfileDao.deleteUserProfile(userId)
    }

    override suspend fun userProfileExists(userId: String): Boolean {
        return userProfileDao.userProfileExists(userId)
    }

    override suspend fun getUserProfileCount(): Int {
        return userProfileDao.getAllUserProfiles().first().size
    }

    // === 数据转换扩展方法 ===

    private fun UserProfileEntity.toUserProfileData(): UserProfileData {
        return UserProfileData(
            userId = this.userId,
            username = this.username,
            displayName = this.displayName,
            email = this.email,
            phoneNumber = this.phoneNumber,
            bio = this.bio,
            gender = this.gender,
            height = this.height,
            weight = this.weight,
            fitnessLevel = this.fitnessLevel,
            fitnessGoals = this.fitnessGoals,
            workoutDays = this.workoutDays,
            allowPartnerMatching = this.allowPartnerMatching,
            totalActivityCount = this.totalWorkoutCount,
            weeklyActiveMinutes = this.weeklyActiveMinutes,
            lastUpdated = this.lastUpdated,
            createdAt = this.createdAt,
        )
    }

    private fun UserProfileData.toUserProfileEntity(): UserProfileEntity {
        return UserProfileEntity(
            userId = this.userId,
            username = this.username,
            displayName = this.displayName,
            email = this.email,
            phoneNumber = this.phoneNumber,
            profileImageUrl = null, // 暂时不支持
            bio = this.bio,
            gender = this.gender,
            height = this.height,
            heightUnit = "CM", // 默认单位
            weight = this.weight,
            weightUnit = "KG", // 默认单位
            fitnessLevel = this.fitnessLevel,
            fitnessGoals = this.fitnessGoals,
            workoutDays = this.workoutDays,
            allowPartnerMatching = this.allowPartnerMatching,
            totalWorkoutCount = this.totalActivityCount,
            weeklyActiveMinutes = this.weeklyActiveMinutes,
            likesReceived = 0, // 默认值
            isAnonymous = false, // 默认值
            hasValidSubscription = false, // 默认值
            lastUpdated = this.lastUpdated,
            createdAt = this.createdAt,
            profileSummary = null, // 暂时不支持
            vector = null, // 暂时不支持
            vectorCreatedAt = null, // 暂时不支持
        )
    }
}

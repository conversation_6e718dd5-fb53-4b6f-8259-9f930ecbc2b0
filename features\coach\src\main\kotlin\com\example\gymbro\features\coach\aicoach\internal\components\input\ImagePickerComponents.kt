package com.example.gymbro.features.coach.aicoach.internal.components.input

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 🎨 ChatGPT风格图片选择器按钮 - 纯按钮组件
 *
 * 只负责按钮本身，不包含面板逻辑
 *
 * @param onImagePickerClick 点击图片按钮的回调
 * @param iconTint 图标颜色
 * @param modifier 修饰符
 */
@Composable
internal fun ChatGPTImagePickerButton(
    onImagePickerClick: () -> Unit,
    iconTint: Color,
    modifier: Modifier = Modifier,
) {
    ChatGPTActionButton(
        icon = Icons.Default.AddPhotoAlternate,
        contentDescription = "添加图片",
        tint = iconTint,
        onClick = onImagePickerClick,
        modifier = modifier,
    )
}

/**
 * 🎨 图片选择器组件 - 包含完整选择逻辑，用于相对定位
 *
 * 用于在ActionToolbar中相对于按钮定位显示
 *
 * @param isVisible 是否显示面板
 * @param onDismiss 关闭面板回调
 * @param onImagesSelected 图片选择完成回调
 * @param modifier 修饰符
 */
@Composable
internal fun ChatGPTImagePickerWithLogic(
    isVisible: Boolean,
    onDismiss: () -> Unit,
    onImagesSelected: (List<Uri>) -> Unit,
    modifier: Modifier = Modifier,
) {
    if (!isVisible) return

    // 🎯 图片选择器 - 相册
    val photoPickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.PickVisualMedia(),
    ) { uri ->
        uri?.let {
            onImagesSelected(listOf(it))
        }
    }

    // 🎯 文件选择器
    val filePickerLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.GetContent(),
    ) { uri ->
        uri?.let {
            onImagesSelected(listOf(it))
        }
    }

    // 🎯 直接显示面板，不需要全屏覆盖层
    ChatGPTImagePickerPanel(
        onDismiss = onDismiss,
        onPhotoClick = {
            photoPickerLauncher.launch(
                androidx.activity.result.PickVisualMediaRequest(
                    ActivityResultContracts.PickVisualMedia.ImageOnly,
                ),
            )
        },
        onCameraClick = {
            // 暂时使用相册选择器代替相机
            photoPickerLauncher.launch(
                androidx.activity.result.PickVisualMediaRequest(
                    ActivityResultContracts.PickVisualMedia.ImageOnly,
                ),
            )
        },
        onFileClick = {
            filePickerLauncher.launch("image/*")
        },
        modifier = modifier,
    )
}

// � Portal 组件已移除，使用简化的 offset + zIndex 方案

/**
 * 🎨 ChatGPT风格图片选择面板 - 重新设计版
 */
@Composable
internal fun ChatGPTImagePickerPanel(
    onDismiss: () -> Unit,
    onPhotoClick: () -> Unit,
    onCameraClick: () -> Unit,
    onFileClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier.wrapContentSize(),
        shape = RoundedCornerShape(Tokens.Radius.Large),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = Tokens.Elevation.Card,
        tonalElevation = 0.dp,
    ) {
        Column(
            modifier = Modifier
                .padding(Tokens.Spacing.XSmall) // 🔥 减少内边距，更紧凑
                .widthIn(min = 120.dp, max = 140.dp), // 🔥 缩小宽度，更紧凑
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Tiny), // 🔥 减少选项间距 (2dp)
        ) {
            ChatGPTImagePickerOption(
                icon = Icons.Default.PhotoLibrary,
                title = "照片",
                onClick = onPhotoClick,
            )

            ChatGPTImagePickerOption(
                icon = Icons.Default.CameraAlt,
                title = "相机",
                onClick = onCameraClick,
            )

            ChatGPTImagePickerOption(
                icon = Icons.Default.AttachFile,
                title = "文件",
                onClick = onFileClick,
            )
        }
    }
}

/**
 * 🎨 ChatGPT风格图片选择选项 - 优化点击响应版
 */
@Composable
internal fun ChatGPTImagePickerOption(
    icon: ImageVector,
    title: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null, // 使用默认的 ripple 效果
                onClick = onClick,
            )
            .padding(
                horizontal = Tokens.Spacing.Small, // 🔥 减少水平间距，更紧凑
                vertical = Tokens.Spacing.XSmall, // 🔥 减少垂直间距，更紧凑
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
    ) {
        Icon(
            imageVector = icon,
            contentDescription = title,
            tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.8f),
            modifier = Modifier.size(Tokens.Icon.Small), // 🔥 使用小尺寸图标，更紧凑
        )

        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
        )
    }
}

// === 预览组件 ===

@GymBroPreview
@Composable
private fun ChatGPTImagePickerButtonPreview() {
    GymBroTheme {
        ChatGPTImagePickerButton(
            onImagePickerClick = { /* no-op */ },
            iconTint = Color(0xFF6B7280),
        )
    }
}

@GymBroPreview
@Composable
private fun ChatGPTImagePickerPanelPreview() {
    GymBroTheme {
        ChatGPTImagePickerPanel(
            onDismiss = { /* no-op */ },
            onPhotoClick = { /* no-op */ },
            onCameraClick = { /* no-op */ },
            onFileClick = { /* no-op */ },
        )
    }
}

@GymBroPreview
@Composable
private fun ChatGPTImagePickerWithLogicPreview() {
    GymBroTheme {
        ChatGPTImagePickerWithLogic(
            isVisible = true,
            onDismiss = { /* no-op */ },
            onImagesSelected = { /* no-op */ },
        )
    }
}

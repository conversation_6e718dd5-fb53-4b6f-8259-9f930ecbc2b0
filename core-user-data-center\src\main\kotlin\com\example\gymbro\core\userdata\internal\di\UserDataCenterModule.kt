package com.example.gymbro.core.userdata.internal.di

import com.example.gymbro.core.userdata.api.UserDataCenterApi
import com.example.gymbro.core.userdata.internal.adapter.UserDataProviderImpl
import com.example.gymbro.core.userdata.internal.api.UserDataCenterApiImpl
import com.example.gymbro.core.userdata.internal.datasource.UserLocalDataSource
import com.example.gymbro.core.userdata.internal.datasource.UserLocalDataSourceSimpleImpl
import com.example.gymbro.core.userdata.internal.repository.UserDataRepository
import com.example.gymbro.core.userdata.internal.repository.UserDataRepositoryImpl
import com.example.gymbro.core.userdata.internal.service.UserDataServiceImpl
import com.example.gymbro.domain.user.repository.UserDataProvider
import com.example.gymbro.domain.user.service.UserDataService
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * UserDataCenter 模块的依赖注入配置
 *
 * 配置 UserDataCenter 模块的所有依赖绑定，确保各组件能够正确注入和使用。
 * 遵循 Hilt 的最佳实践，使用 @Binds 进行接口绑定。
 *
 * 绑定关系：
 * - UserDataCenterApi → UserDataCenterApiImpl
 * - UserDataRepository → UserDataRepositoryImpl
 *
 * 作用域：
 * - 所有绑定都使用 @Singleton 作用域，确保全局唯一实例
 * - 安装在 SingletonComponent 中，应用生命周期内有效
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class UserDataCenterModule {

    /**
     * 绑定 UserDataCenterApi 接口到其实现类
     *
     * 这是模块的主要对外接口，其他模块通过此接口访问 UserDataCenter 功能。
     *
     * @param impl UserDataCenterApiImpl 实现类
     * @return UserDataCenterApi 接口
     */
    @Binds
    @Singleton
    abstract fun bindUserDataCenterApi(
        impl: UserDataCenterApiImpl,
    ): UserDataCenterApi

    /**
     * 绑定 UserDataRepository 接口到其实现类
     *
     * 内部使用的数据仓库接口，负责数据的存储和访问。
     *
     * @param impl UserDataRepositoryImpl 实现类
     * @return UserDataRepository 接口
     */
    @Binds
    @Singleton
    internal abstract fun bindUserDataRepository(
        impl: UserDataRepositoryImpl,
    ): UserDataRepository

    /**
     * 绑定 UserLocalDataSource 接口到简化实现
     *
     * 使用 @SimpleUserDataSource 限定符避免与 Room 实现冲突。
     * 这个实现主要用于测试和开发场景。
     *
     * @param impl UserLocalDataSourceSimpleImpl 简化实现类
     * @return UserLocalDataSource 接口
     */
    @Binds
    @Singleton
    @SimpleUserDataSource
    internal abstract fun bindSimpleUserLocalDataSource(
        impl: UserLocalDataSourceSimpleImpl,
    ): UserLocalDataSource

    /**
     * 绑定 UserDataProvider 接口到其实现类
     *
     * 这是为domain层提供的用户数据访问接口，遵循依赖倒置原则。
     * 通过适配器模式将UserDataCenterApi适配为domain层的接口。
     *
     * @param impl UserDataProviderImpl 实现类
     * @return UserDataProvider 接口
     */
    @Binds
    @Singleton
    abstract fun bindUserDataProvider(
        impl: UserDataProviderImpl,
    ): UserDataProvider

    /**
     * 绑定 UserDataService 接口到其实现类
     *
     * 为 domain 层提供 UserDataService 接口的实现，遵循依赖倒置原则。
     * 这是 Profile 模块用于用户数据操作的主要接口。
     *
     * @param impl UserDataServiceImpl 实现类
     * @return UserDataService 接口
     */
    @Binds
    @Singleton
    abstract fun bindUserDataService(
        impl: UserDataServiceImpl,
    ): UserDataService
}

/**
 * UserDataCenter 初始化模块
 *
 * 提供一些需要特殊初始化的组件，如单例对象或需要特定配置的实例。
 * 当前版本使用 Room 实现通过 di 模块提供，此模块保留用于未来扩展。
 */
@Module
@InstallIn(SingletonComponent::class)
object UserDataCenterProviderModule {
    // 当前版本暂无需要特殊提供的依赖
    // Room 数据库实现已通过 di 模块的 UserDataCenterDiModule 提供
}

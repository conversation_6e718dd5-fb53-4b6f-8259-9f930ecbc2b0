package com.example.gymbro.core.userdata.internal.dao

import kotlinx.coroutines.flow.Flow

/**
 * 用户数据 DAO 抽象接口
 *
 * 根据解决循环依赖.md的方案，这个接口定义了 UserDataCenter 需要的数据库操作，
 * 具体实现将在 di 模块中通过 Hilt 绑定到 data 模块的具体 DAO 实现。
 *
 * 设计原则：
 * - 避免循环依赖：core-user-data-center 不直接依赖 data 模块
 * - 依赖倒置：定义抽象接口，具体实现在外部注入
 * - 单一职责：只包含 UserDataCenter 需要的操作
 */
interface UserDataDao {

    /**
     * 观察当前用户的认证数据
     * @return Flow<UserAuthData?> 当前用户认证数据流
     */
    fun observeCurrentUserAuth(): Flow<UserAuthData?>

    /**
     * 观察指定用户的认证数据
     * @param userId 用户ID
     * @return Flow<UserAuthData?> 指定用户认证数据流
     */
    fun observeUserAuth(userId: String): Flow<UserAuthData?>

    /**
     * 获取当前用户认证数据（同步）
     * @return UserAuthData? 当前用户认证数据
     */
    suspend fun getCurrentUserAuth(): UserAuthData?

    /**
     * 获取指定用户认证数据（同步）
     * @param userId 用户ID
     * @return UserAuthData? 指定用户认证数据
     */
    suspend fun getUserAuth(userId: String): UserAuthData?

    /**
     * 保存用户认证数据
     * @param userAuth 用户认证数据
     */
    suspend fun saveUserAuth(userAuth: UserAuthData)

    /**
     * 更新用户认证数据
     * @param userAuth 用户认证数据
     */
    suspend fun updateUserAuth(userAuth: UserAuthData)

    /**
     * 删除所有用户认证数据
     */
    suspend fun deleteAllUserAuth()

    /**
     * 删除指定用户认证数据
     * @param userId 用户ID
     */
    suspend fun deleteUserAuth(userId: String)

    /**
     * 检查用户是否存在
     * @param userId 用户ID
     * @return Boolean 用户是否存在
     */
    suspend fun userExists(userId: String): Boolean

    /**
     * 检查是否有任何用户数据
     * @return Boolean 是否有用户数据
     */
    suspend fun hasAnyUser(): Boolean
}

/**
 * 用户资料 DAO 抽象接口
 */
interface UserProfileDao {

    /**
     * 观察所有用户资料
     * @return Flow<List<UserProfileData>> 所有用户资料数据流
     */
    fun observeAllUserProfiles(): Flow<List<UserProfileData>>

    /**
     * 观察指定用户资料
     * @param userId 用户ID
     * @return Flow<UserProfileData?> 指定用户资料数据流
     */
    fun observeUserProfile(userId: String): Flow<UserProfileData?>

    /**
     * 获取指定用户资料（同步）
     * @param userId 用户ID
     * @return UserProfileData? 指定用户资料数据
     */
    suspend fun getUserProfile(userId: String): UserProfileData?

    /**
     * 保存用户资料数据
     * @param userProfile 用户资料数据
     */
    suspend fun saveUserProfile(userProfile: UserProfileData)

    /**
     * 更新用户资料数据
     * @param userProfile 用户资料数据
     */
    suspend fun updateUserProfile(userProfile: UserProfileData)

    /**
     * 删除所有用户资料数据
     */
    suspend fun deleteAllUserProfiles()

    /**
     * 删除指定用户资料数据
     * @param userId 用户ID
     */
    suspend fun deleteUserProfile(userId: String)

    /**
     * 检查用户资料是否存在
     * @param userId 用户ID
     * @return Boolean 用户资料是否存在
     */
    suspend fun userProfileExists(userId: String): Boolean

    /**
     * 获取用户资料数量
     * @return Int 用户资料数量
     */
    suspend fun getUserProfileCount(): Int
}

/**
 * 用户认证数据模型
 *
 * 这是一个简化的数据模型，只包含 UserDataCenter 需要的字段
 */
data class UserAuthData(
    val userId: String,
    val email: String?,
    val displayName: String?,
    val phoneNumber: String?,
    val isAnonymous: Boolean = false,
    val isEmailVerified: Boolean = false,
    val isPhoneVerified: Boolean = false,
    val photoUrl: String? = null,
    val isActive: Boolean = true,
    val lastUpdated: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis(),
)

/**
 * 用户资料数据模型
 *
 * 这是一个简化的数据模型，只包含 UserDataCenter 需要的字段
 */
data class UserProfileData(
    val userId: String,
    val username: String?,
    val displayName: String?,
    val email: String?,
    val phoneNumber: String?,
    val bio: String?,
    val gender: String?,
    val height: Float?,
    val weight: Float?,
    val fitnessLevel: Int?,
    val fitnessGoals: List<String> = emptyList(),
    val workoutDays: List<String> = emptyList(),
    val allowPartnerMatching: Boolean = false,
    val totalActivityCount: Int = 0,
    val weeklyActiveMinutes: Int = 0,
    val lastUpdated: Long = System.currentTimeMillis(),
    val createdAt: Long = System.currentTimeMillis(),
)

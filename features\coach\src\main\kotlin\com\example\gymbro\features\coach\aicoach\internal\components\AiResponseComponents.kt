package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.thinkingbox.ThinkingBoxStaticRenderer

/**
 * 🔥 【架构迁移完成】Final 内容渲染职责迁移到 ThinkingBox 模块
 *
 * 根据 ThinkingBox README.md 的最新架构设计，完成了以下职责分离：
 * - **ThinkingBox 模块**：负责完整的 AI 思考流程和 Final 内容渲染
 * - **Coach 模块**：专注于对话管理、历史记录保存和数据持久化
 *
 * 迁移内容：
 * - 移除了 Coach 模块中的 FinalRichTextRenderer 直接使用
 * - 统一使用 ThinkingBoxStaticRenderer 进行历史消息的富文本渲染
 * - 保持了完整的 Markdown + Mermaid 渲染能力
 * - 确保了与实时 ThinkingBox 渲染的视觉一致性
 */

/**
 * Historical AI Response Renderer - Unified ThinkingBox Integration
 *
 * Uses the unified ThinkingBox component for consistent AI response rendering.
 * For historical messages, we create a minimal UI state to display the final content.
 */
@Composable
internal fun HistoricalAiResponseRenderer(
    message: AiCoachContract.MessageUi,
    onShowSummaryCard: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    // For historical messages, we only need to display the final content
    // The thinking process is not shown for historical messages to optimize performance
    StaticAiResponseRenderer(
        message = message,
        modifier = modifier,
    )
}

/**
 * Static AI Response Renderer - 富文本历史显示
 *
 * 🔥 【架构迁移】使用 ThinkingBox 模块的统一静态渲染器
 * - 完全委托给 ThinkingBox 模块处理 Final 内容渲染
 * - 支持 Markdown 格式：标题、表格、代码高亮、任务列表等
 * - 支持 Mermaid 图表渲染
 * - 禁用打字机效果，直接显示最终内容
 * - 保持与实时 ThinkingBox 一致的渲染质量
 */
@Composable
internal fun StaticAiResponseRenderer(
    message: AiCoachContract.MessageUi,
    modifier: Modifier = Modifier,
) {
    // 🔥 【架构迁移】使用 ThinkingBox 的统一静态渲染器
    val finalContent = message.finalMarkdown?.takeIf { it.isNotBlank() } ?: message.content

    if (finalContent.isNotBlank()) {
        ThinkingBoxStaticRenderer(
            finalMarkdown = finalContent,
            modifier = modifier, // ThinkingBoxStaticRenderer 内部已处理 fillMaxWidth
        )
    }
}

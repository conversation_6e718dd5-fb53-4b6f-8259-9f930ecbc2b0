package com.example.gymbro.core.userdata.api.model

import com.example.gymbro.domain.profile.model.user.FitnessGoal
import com.example.gymbro.domain.profile.model.user.WorkoutDay
import com.example.gymbro.domain.profile.model.user.enums.FitnessLevel
import com.example.gymbro.domain.profile.model.user.enums.Gender

/**
 * 统一用户数据模型
 *
 * 作为 UserDataCenter 的核心数据模型，整合了认证信息和个人资料信息，
 * 为所有消费模块提供一致的用户数据视图。
 *
 * 设计原则：
 * - 单一数据源：所有用户相关数据的统一表示
 * - 不可变性：使用 data class 确保数据不可变
 * - 完整性：包含认证、个人资料、统计等所有用户数据
 * - 版本化：支持数据模型演进和兼容性
 *
 * @property userId 用户唯一标识符，来自认证系统
 * @property email 用户邮箱地址，可选
 * @property displayName 用户显示名称，可选
 * @property isAnonymous 是否为匿名用户
 * @property isEmailVerified 邮箱是否已验证
 * @property phoneNumber 用户电话号码，可选
 * @property isPhoneVerified 电话号码是否已验证
 * @property username 用户名，可选
 * @property height 身高（厘米），可选
 * @property weight 体重（公斤），可选
 * @property gender 性别
 * @property fitnessLevel 健身水平
 * @property fitnessGoals 健身目标列表
 * @property workoutDays 训练日列表
 * @property bio 个人简介，可选
 * @property totalActivityCount 总活动次数
 * @property weeklyActiveMinutes 每周活跃分钟数
 * @property allowPartnerMatching 是否允许伙伴匹配
 * @property lastUpdated 最后更新时间戳
 * @property syncStatus 同步状态
 * @property dataVersion 数据版本
 */
data class UnifiedUserData(
    // === 认证数据 ===
    val userId: String,
    val email: String? = null,
    val displayName: String? = null,
    val isAnonymous: Boolean = false,
    val isEmailVerified: Boolean = false,
    val phoneNumber: String? = null,
    val isPhoneVerified: Boolean = false,

    // === 个人资料数据 ===
    val username: String? = null,
    val height: Float? = null,
    val weight: Float? = null,
    val gender: Gender = Gender.OTHER,
    val fitnessLevel: FitnessLevel = FitnessLevel.BEGINNER,
    val fitnessGoals: List<FitnessGoal> = emptyList(),
    val workoutDays: List<WorkoutDay> = emptyList(),
    val bio: String? = null,

    // === 统计数据 ===
    val totalActivityCount: Int = 0,
    val weeklyActiveMinutes: Int = 0,
    val allowPartnerMatching: Boolean = false,

    // === 元数据 ===
    val lastUpdated: Long = System.currentTimeMillis(),
    val syncStatus: SyncStatus = SyncStatus.SYNCED,
    val dataVersion: String = "1.0",
) {
    /**
     * 检查用户数据是否完整
     * 完整的用户数据应该包含基本的身体数据和健身信息
     */
    val isComplete: Boolean
        get() = userId.isNotEmpty() &&
            !displayName.isNullOrBlank() &&
            height != null &&
            weight != null &&
            fitnessLevel != FitnessLevel.UNSPECIFIED &&
            fitnessGoals.isNotEmpty()

    /**
     * 检查是否有基本的个人信息
     */
    val hasBasicInfo: Boolean
        get() = userId.isNotEmpty() &&
            !displayName.isNullOrBlank()

    /**
     * 检查是否有健身相关信息
     */
    val hasFitnessInfo: Boolean
        get() = fitnessLevel != FitnessLevel.UNSPECIFIED &&
            fitnessGoals.isNotEmpty()

    /**
     * 检查是否有身体数据
     */
    val hasBodyData: Boolean
        get() = height != null && weight != null

    /**
     * 计算 BMI（身体质量指数）
     * @return BMI 值，如果身高或体重为空则返回 null
     */
    fun getBmi(): Float? {
        return if (height != null && weight != null && height > 0) {
            val heightInMeters = height / 100f
            weight / (heightInMeters * heightInMeters)
        } else {
            null
        }
    }

    /**
     * 获取格式化的健身目标字符串
     * @return 格式化的健身目标，用逗号分隔
     */
    fun getFormattedGoals(): String {
        return fitnessGoals.joinToString(", ") { it.getDisplayName() }
    }

    /**
     * 获取格式化的训练日字符串
     * @return 格式化的训练日，用逗号分隔
     */
    fun getFormattedWorkoutDays(): String {
        return workoutDays.joinToString(", ") { workoutDay ->
            when (workoutDay) {
                WorkoutDay.MONDAY -> "周一"
                WorkoutDay.TUESDAY -> "周二"
                WorkoutDay.WEDNESDAY -> "周三"
                WorkoutDay.THURSDAY -> "周四"
                WorkoutDay.FRIDAY -> "周五"
                WorkoutDay.SATURDAY -> "周六"
                WorkoutDay.SUNDAY -> "周日"
            }
        }
    }

    /**
     * 获取用户的主要标识符
     * 优先级：显示名称 > 用户名 > 邮箱 > 用户ID
     */
    fun getPrimaryIdentifier(): String {
        return when {
            !displayName.isNullOrBlank() -> displayName
            !username.isNullOrBlank() -> username
            !email.isNullOrBlank() -> email
            else -> userId
        }
    }

    /**
     * 检查数据是否需要同步
     */
    val needsSync: Boolean
        get() = syncStatus == SyncStatus.PENDING_SYNC ||
            syncStatus == SyncStatus.SYNC_FAILED

    companion object {
        /**
         * 创建空的用户数据
         * @param userId 用户ID
         * @return 空的 UnifiedUserData 实例
         */
        fun empty(userId: String): UnifiedUserData {
            return UnifiedUserData(
                userId = userId,
                syncStatus = SyncStatus.PENDING_SYNC,
            )
        }

        /**
         * 创建匿名用户数据
         * @param userId 匿名用户ID
         * @return 匿名用户的 UnifiedUserData 实例
         */
        fun anonymous(userId: String): UnifiedUserData {
            return UnifiedUserData(
                userId = userId,
                isAnonymous = true,
                displayName = "匿名用户",
                syncStatus = SyncStatus.SYNCED,
            )
        }
    }
}

package com.example.gymbro.core.network.protocol

import android.os.SystemClock
import com.example.gymbro.core.network.config.NetworkConfigManager
import com.example.gymbro.core.network.eventbus.TokenBus
import com.example.gymbro.core.network.eventbus.TokenEvent
import com.example.gymbro.core.network.ws.LlmStreamClient
import com.example.gymbro.core.network.ws.WsState
import com.example.gymbro.shared.models.ai.ChatMessage
import com.example.gymbro.shared.models.ai.ChatRequest
import com.example.gymbro.shared.models.network.NetworkResult
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.sse.EventSource
import okhttp3.sse.EventSourceListener
import okhttp3.sse.EventSources
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 🔥 修复：删除重复的CoreChatMessage定义，使用shared-models中的ChatMessage
 */
@Serializable
data class ChatCompletionRequest(
    val model: String,
    val messages: List<ChatMessage>,
    val stream: Boolean = true,
)

/**
 * 自适应流式客户端 - 智能协议选择实现
 *
 * 🎯 协议策略：
 * 1. 默认HTTP+SSE - 主要协议，稳定可靠
 * 2. 降级HTTP基础 - SSE不支持时的备选方案
 * 3. WebSocket专用 - 仅用于热更新配置的指定模型
 * 4. 动态配置 - 从NetworkConfigManager获取API配置
 */
@Singleton
class AdaptiveStreamClient @Inject constructor(
    private val httpClient: OkHttpClient,
    private val json: Json,
    private val tokenBus: TokenBus,
    private val networkConfigManager: NetworkConfigManager,
    private val protocolDetector: ProtocolDetector,
) : LlmStreamClient {

    // 🔥 【协议检测】枚举
    private enum class DetectedProtocol {
        UNKNOWN, JSON, TEXT
    }

    // 🔥 【协议检测】状态映射
    private val protocolMap = mutableMapOf<String, DetectedProtocol>()

    // 🔥 【上下文注入】解析状态管理
    private data class ParseState(
        var hasSentThinkTag: Boolean = false,
        var hasSentCloseThinkTag: Boolean = false,
    )

    private val parseStates = mutableMapOf<String, ParseState>()

    /**
     * 🔥 【上下文注入】解析JSON内容并注入XML上下文标签
     */
    private fun parseJsonContentWithContext(sseData: String, messageId: String): String? {
        if (sseData.isBlank() || sseData == "[DONE]") {
            return null
        }

        return try {
            // 移除data:前缀
            val jsonStr = sseData.removePrefix("data:").trim()
            if (jsonStr.isEmpty() || jsonStr == "[DONE]") {
                return null
            }

            // 尝试解析JSON
            val jsonElement = json.parseToJsonElement(jsonStr)
            val jsonObject = jsonElement.jsonObject

            // 获取choices数组
            val choices = jsonObject["choices"]?.jsonArray
            val delta = choices?.firstOrNull()?.jsonObject?.get("delta")?.jsonObject

            if (delta == null) {
                return null
            }

            // 提取reasoning_content和content字段
            val reasoningContent = delta["reasoning_content"]?.jsonPrimitive?.contentOrNull
            val content = delta["content"]?.jsonPrimitive?.contentOrNull

            // 获取或创建解析状态
            val state = parseStates.getOrPut(messageId) { ParseState() }

            // 🔥 【上下文注入逻辑】动态构建XML上下文
            return when {
                !reasoningContent.isNullOrEmpty() -> {
                    // reasoning_content: 注入<think>标签
                    if (!state.hasSentThinkTag) {
                        state.hasSentThinkTag = true
                        "<think>$reasoningContent"
                    } else {
                        reasoningContent
                    }
                }
                !content.isNullOrEmpty() -> {
                    // content: 注入</think>标签
                    if (state.hasSentThinkTag && !state.hasSentCloseThinkTag) {
                        state.hasSentCloseThinkTag = true
                        "</think>$content"
                    } else {
                        content
                    }
                }
                else -> null
            }
        } catch (e: Exception) {
            // 解析失败，说明不是JSON格式
            null
        }
    }

    /**
     * 🔥 【状态清理】清理指定messageId的解析状态
     */
    private fun clearParseState(messageId: String) {
        parseStates.remove(messageId)
    }

    /* 剥掉 assistant 消息中的 reasoning_content，避免 DeepSeek 400 */
    private fun stripReasoning(msgs: List<ChatMessage>): List<ChatMessage> =
        msgs.map {
            // ChatMessage 只有 role 和 content 字段，无需特殊处理
            it
        }

    private fun isDeepSeek(model: String): Boolean =
        model.contains("deepseek", ignoreCase = true)

    private fun isTopTierModel(model: String): Boolean =
        model.contains("gpt-4", ignoreCase = true) ||
            model.contains("claude", ignoreCase = true) ||
            model.contains("gemini", ignoreCase = true)

    override suspend fun streamChatWithMessageId(request: ChatRequest, messageId: String, offset: Int) {
        val config = networkConfigManager.getCurrentConfig()
        val baseUrl = config.restBase
        val apiKey = config.apiKey

        if (baseUrl.isBlank() || apiKey.isBlank()) {
            Timber.e("❌ 网络配置无效: baseUrl='$baseUrl', apiKey='${apiKey.take(10)}...'")
            return
        }

        try {
            streamChatInternal(request, messageId, baseUrl, apiKey, offset)
                .catch { e ->
                    Timber.e(e, "❌ 流式传输失败")
                    MainScope().launch {
                        tokenBus.publish(TokenEvent(messageId, "", isComplete = true))
                    }
                }
                .collect { token ->
                    if (token.isNotBlank()) {
                        MainScope().launch {
                            tokenBus.publish(TokenEvent(messageId, token, isComplete = false))
                        }
                    }
                }

            // 发送完成信号
            MainScope().launch {
                tokenBus.publish(TokenEvent(messageId, "", isComplete = true))
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ 流式传输异常")
            MainScope().launch {
                tokenBus.publish(TokenEvent(messageId, "", isComplete = true))
            }
        }
    }

    override suspend fun checkConnection(): NetworkResult<Boolean> {
        val config = networkConfigManager.getCurrentConfig()
        return try {
            // 使用HTTP HEAD请求检查连接
            val request = Request.Builder()
                .url("${config.restBase}/v1/models")
                .head()
                .header("Authorization", "Bearer ${config.apiKey}")
                .build()

            val response = httpClient.newCall(request).execute()
            response.use {
                if (response.isSuccessful) {
                    NetworkResult.Success(true)
                } else {
                    NetworkResult.Error(
                        com.example.gymbro.shared.models.network.ApiError.Http(
                            response.code,
                            response.message,
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            NetworkResult.Error(
                com.example.gymbro.shared.models.network.ApiError.Unknown(
                    e.message ?: "Unknown error",
                ),
            )
        }
    }

    override fun getBaseUrl(): String {
        return networkConfigManager.getCurrentConfig().restBase
    }

    override fun pause() {
        // HTTP+SSE doesn't need pause/resume functionality
        Timber.d("📡 AdaptiveStreamClient pause() - no-op for HTTP+SSE")
    }

    override fun resume() {
        // HTTP+SSE doesn't need pause/resume functionality
        Timber.d("📡 AdaptiveStreamClient resume() - no-op for HTTP+SSE")
    }

    override fun getCurrentState(): WsState {
        // Always return Open for HTTP+SSE
        return WsState.Open
    }

    private suspend fun streamChatInternal(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
        offset: Int = 0,
    ): Flow<String> {
        if (isTopTierModel(request.model)) {
            Timber.d("🔗 WebSocket path for ${request.model}")
            return tryWebSocketWithFallback(request, messageId, baseUrl, apiKey)
        }
        Timber.d("📡 HTTP+SSE path for ${request.model}")
        return tryHttpSSEWithFallback(request, messageId, baseUrl, apiKey)
    }

    /* ─────────────────────────────────────────────────────── */
    /* 3. SSE branch                                           */
    /* ─────────────────────────────────────────────────────── */
    private fun streamChatWithSSE(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> = callbackFlow {
        try {
            /* 3-1 build body (DeepSeek strip) */
            val msgs = if (isDeepSeek(request.model)) {
                stripReasoning(request.messages)
            } else {
                request.messages
            }
            val bodyJson = json.encodeToString(
                ChatCompletionRequest(request.model, msgs, true),
            )
            val reqBody = bodyJson.toRequestBody("application/json".toMediaTypeOrNull())

            val httpRequest = Request.Builder()
                .url("$baseUrl/v1/chat/completions")
                .post(reqBody)
                .header("Accept", "text/event-stream")
                .header("Cache-Control", "no-cache")
                .header("Connection", "keep-alive")
                .header("Authorization", "Bearer $apiKey")
                .header("User-Agent", "GymBro/1.0")
                .build()

            Timber.d("📡 SSE请求配置:")
            Timber.d("  - URL: $baseUrl/v1/chat/completions")
            Timber.d("  - Model: ${request.model}")
            Timber.d("  - Stream: true")

            /* 3-2 SSE listener */
            val listener = object : EventSourceListener() {
                @Volatile private var finished = false
                private var lifeKeeper: EventSource? = null
                private var lastBeat = SystemClock.elapsedRealtime()

                override fun onOpen(es: EventSource, resp: okhttp3.Response) {
                    Timber.d("📡 SSE连接已建立: ${resp.code}")
                    lifeKeeper = es
                    lastBeat = SystemClock.elapsedRealtime()
                }

                override fun onEvent(es: EventSource, id: String?, type: String?, data: String) {
                    lastBeat = SystemClock.elapsedRealtime()

                    if (data == "[DONE]") {
                        finished = true
                        clearParseState(messageId)
                        Timber.d("📡 SSE流结束")
                        close()
                        return
                    }

                    // 🔥 【协议检测与上下文注入】
                    var protocol = protocolMap[messageId] ?: DetectedProtocol.UNKNOWN

                    if (protocol == DetectedProtocol.UNKNOWN) {
                        // 首次数据事件，检测协议类型
                        protocol = try {
                            // A simple check: if it can be parsed as JSON with delta content, it's JSON.
                            if (parseJsonContentWithContext(data, messageId) != null) {
                                DetectedProtocol.JSON
                            } else {
                                DetectedProtocol.TEXT
                            }
                        } catch (e: Exception) {
                            DetectedProtocol.TEXT
                        }
                        protocolMap[messageId] = protocol
                        Timber.d("✅ Protocol detected for messageId '$messageId': $protocol")
                    }

                    val cleanToken: String? = when (protocol) {
                        DetectedProtocol.JSON -> parseJsonContentWithContext(data, messageId)
                        DetectedProtocol.TEXT -> data // Pass through directly
                        DetectedProtocol.UNKNOWN -> null // Wait for more data to determine protocol
                    }

                    if (!cleanToken.isNullOrBlank()) {
                        MainScope().launch {
                            tokenBus.publish(TokenEvent(messageId, cleanToken, isComplete = false))
                        }
                    }
                }

                override fun onClosed(es: EventSource) {
                    // 🔥 【状态清理】清理解析状态
                    clearParseState(messageId)
                    Timber.d("📡 SSE连接已关闭")
                    close()
                }

                override fun onFailure(es: EventSource, t: Throwable?, r: okhttp3.Response?) {
                    // 🔥 【状态清理】清理解析状态
                    clearParseState(messageId)
                    Timber.e(t, "📡 SSE连接失败")
                    close(t)
                }
            }

            val es = EventSources.createFactory(httpClient)
                .newEventSource(httpRequest, listener)

            awaitClose {
                clearParseState(messageId)
                es.cancel()
            }
        } catch (e: Exception) {
            clearParseState(messageId)
            Timber.e(e, "SSE init failed")
            close(e)
        }
    }

    /* ───────── WebSocket with fallback ───────── */
    private suspend fun tryWebSocketWithFallback(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> {
        return try {
            // 简单检查WebSocket连通性
            if (isWebSocketAvailable(baseUrl)) {
                streamChatWithWebSocket(request, messageId, baseUrl, apiKey)
            } else {
                Timber.w("⚠️ WebSocket不可用，降级到HTTP+SSE")
                tryHttpSSEWithFallback(request, messageId, baseUrl, apiKey)
            }
        } catch (e: Exception) {
            Timber.w("⚠️ WebSocket连接失败，降级到HTTP+SSE: ${e.message}")
            tryHttpSSEWithFallback(request, messageId, baseUrl, apiKey)
        }
    }

    private suspend fun isWebSocketAvailable(baseUrl: String): Boolean {
        return try {
            // 使用ProtocolDetector的简化检查
            protocolDetector.isWebSocketAvailable(baseUrl, "dummy-key")
        } catch (e: Exception) {
            Timber.w("WebSocket连通性检查失败: ${e.message}")
            false
        }
    }

    /* ───────── HTTP+SSE with fallback ───────── */
    private suspend fun tryHttpSSEWithFallback(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> {
        return try {
            streamChatWithSSE(request, messageId, baseUrl, apiKey)
        } catch (e: Exception) {
            Timber.e(e, "📡 HTTP+SSE失败，尝试基础HTTP")
            streamChatWithBasicHttp(request, messageId, baseUrl, apiKey)
        }
    }

    /* ───────── WebSocket implementation ───────── */
    private suspend fun streamChatWithWebSocket(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> = flow {
        // WebSocket实现（简化版）
        emit("WebSocket streaming not implemented yet")
    }

    /* ───────── Basic HTTP fallback ───────── */
    private suspend fun streamChatWithBasicHttp(
        request: ChatRequest,
        messageId: String,
        baseUrl: String,
        apiKey: String,
    ): Flow<String> = flow {
        // 基础HTTP实现（简化版）
        emit("Basic HTTP fallback not implemented yet")
    }
}

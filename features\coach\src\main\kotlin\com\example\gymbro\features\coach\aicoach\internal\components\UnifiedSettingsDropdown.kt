package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.coachTheme // 🔥 【主题修复】添加Coach主题导入
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.coach.aicoach.AiCoachContract

/**
 * 菜单状态枚举
 */
enum class DropdownMenuState {
    MAIN, // 主设置菜单
    MODEL_SELECTION, // 模型选择子菜单
    PROMPT_SELECTION, // Prompt选择子菜单
    DEBUG, // 🔥 新增：调试选项子菜单
}

/**
 * 统一的设置下拉菜单 - 支持层级导航
 *
 * 🎯 设计目标：
 * - 参考图片样式的层级化菜单
 * - 主菜单 → 子菜单的流畅导航
 * - 统一的视觉风格和交互体验
 * - 遵循 designSystem 主题令牌
 */
@Composable
internal fun UnifiedSettingsDropdown(
    currentMenuState: DropdownMenuState,
    // 模型相关参数
    availableProviders: List<AiCoachContract.ApiProvider>,
    currentProvider: AiCoachContract.ApiProvider,
    getProviderDisplayName: (AiCoachContract.ApiProvider) -> String,
    getProviderModel: (AiCoachContract.ApiProvider) -> String,
    onProviderSelected: (AiCoachContract.ApiProvider) -> Unit,
    // Prompt相关参数
    availablePrompts: List<String>,
    currentPrompt: String,
    getPromptDisplayInfo: (String) -> Pair<String, String>,
    onPromptSelected: (String) -> Unit,
    // 历史记录相关参数
    sessionCount: Int = 0,
    onHistoryClick: () -> Unit,
    // 🔥 新增：调试面板相关参数
    showDebugPanel: Boolean = false,
    onDebugToggle: (Boolean) -> Unit = {},
    onDebugPageClick: () -> Unit = {},
    // 🔥 激活Debug功能：支持模拟数据和真实执行两种调试模式
    debugScenarios: List<com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.DebugScenario> =
        emptyList(),
    onDebugScenarioSelected: (
        com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.DebugScenario,
    ) -> Unit = {},
    // 🔥 新增：真实执行调试场景
    realExecutionScenarios:
    List<com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.RealExecutionScenario> = emptyList(),
    onRealExecutionSelected: (
        com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.RealExecutionScenario,
    ) -> Unit = {},
    // 导航控制
    onMenuStateChanged: (DropdownMenuState) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    DropdownMenu(
        expanded = true,
        onDismissRequest = onDismiss,
        modifier = modifier
            .width(240.dp) // 减少宽度，优化视觉体验
            .clip(RoundedCornerShape(Tokens.Radius.Large))
            .background(MaterialTheme.coachTheme.backgroundElevated), // 🔥 【主题修复】使用Coach主题背景色
    ) {
        when (currentMenuState) {
            DropdownMenuState.MAIN -> {
                MainSettingsMenu(
                    currentProvider = currentProvider,
                    currentPrompt = currentPrompt,
                    getProviderDisplayName = getProviderDisplayName,
                    getPromptDisplayInfo = getPromptDisplayInfo,
                    sessionCount = sessionCount,
                    onHistoryClick = onHistoryClick,
                    onNavigateToModelSelection = { onMenuStateChanged(DropdownMenuState.MODEL_SELECTION) },
                    onNavigateToPromptSelection = { onMenuStateChanged(DropdownMenuState.PROMPT_SELECTION) },
                    // 🔥 新增：调试相关参数
                    showDebugPanel = showDebugPanel,
                    onDebugToggle = onDebugToggle,
                    onDebugPageClick = onDebugPageClick,
                    onNavigateToDebugMenu = { onMenuStateChanged(DropdownMenuState.DEBUG) },
                    onDismiss = onDismiss,
                )
            }
            DropdownMenuState.MODEL_SELECTION -> {
                ModelSelectionSubmenu(
                    availableProviders = availableProviders,
                    currentProvider = currentProvider,
                    getProviderDisplayName = getProviderDisplayName,
                    getProviderModel = getProviderModel,
                    onProviderSelected = { provider ->
                        onProviderSelected(provider)
                        // 选择后返回主菜单，而不是关闭整个菜单
                        onMenuStateChanged(DropdownMenuState.MAIN)
                    },
                    onBackToMain = { onMenuStateChanged(DropdownMenuState.MAIN) },
                    onDismiss = onDismiss,
                )
            }
            DropdownMenuState.PROMPT_SELECTION -> {
                PromptSelectionSubmenu(
                    availablePrompts = availablePrompts,
                    currentPrompt = currentPrompt,
                    getPromptDisplayInfo = getPromptDisplayInfo,
                    onPromptSelected = { prompt ->
                        onPromptSelected(prompt)
                        // 选择后返回主菜单，而不是关闭整个菜单
                        onMenuStateChanged(DropdownMenuState.MAIN)
                    },
                    onBackToMain = { onMenuStateChanged(DropdownMenuState.MAIN) },
                    onDismiss = onDismiss,
                )
            }
            DropdownMenuState.DEBUG -> {
                DebugSubmenu(
                    debugScenarios = debugScenarios,
                    realExecutionScenarios = realExecutionScenarios,
                    onDebugScenarioSelected = { scenario ->
                        onDebugScenarioSelected(scenario)
                        onDismiss()
                    },
                    onRealExecutionSelected = { scenario ->
                        onRealExecutionSelected(scenario)
                        onDismiss()
                    },
                    onBackToMain = { onMenuStateChanged(DropdownMenuState.MAIN) },
                    onDismiss = onDismiss,
                )
            }
        }
    }
}

/**
 * 主设置菜单
 */
@Composable
private fun MainSettingsMenu(
    currentProvider: AiCoachContract.ApiProvider,
    currentPrompt: String,
    getProviderDisplayName: (AiCoachContract.ApiProvider) -> String,
    getPromptDisplayInfo: (String) -> Pair<String, String>,
    sessionCount: Int,
    onHistoryClick: () -> Unit,
    onNavigateToModelSelection: () -> Unit,
    onNavigateToPromptSelection: () -> Unit,
    // 🔥 新增：调试相关参数
    showDebugPanel: Boolean,
    onDebugToggle: (Boolean) -> Unit,
    onDebugPageClick: () -> Unit,
    onNavigateToDebugMenu: () -> Unit,
    onDismiss: () -> Unit,
) {
    // 历史记录项
    HistoryMenuItem(
        sessionCount = sessionCount,
        onClick = {
            onHistoryClick()
            onDismiss() // 点击后关闭菜单
        },
    )

    HorizontalDivider(
        modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium),
        color = MaterialTheme.coachTheme.dividerPrimary, // 🔥 【主题修复】使用Coach主题分割线色
    )

    // 模型设置项
    SettingsMenuItem(
        icon = Icons.Filled.Psychology,
        title = "模型设置",
        subtitle = getProviderDisplayName(currentProvider),
        onClick = onNavigateToModelSelection,
        showArrow = true,
    )

    HorizontalDivider(
        modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium),
        color = MaterialTheme.coachTheme.dividerPrimary, // 🔥 【主题修复】使用Coach主题分割线色
    )

    // Prompt设置项
    val (promptName, _) = getPromptDisplayInfo(currentPrompt)
    SettingsMenuItem(
        icon = Icons.Filled.EditNote,
        title = "Prompt设置",
        subtitle = promptName,
        onClick = onNavigateToPromptSelection,
        showArrow = true,
    )

    HorizontalDivider(
        modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium),
        color = MaterialTheme.coachTheme.dividerPrimary, // 🔥 【主题修复】使用Coach主题分割线色
    )

    // 🔥 新增：调试面板开关
    DebugToggleMenuItem(
        isEnabled = showDebugPanel,
        onToggle = onDebugToggle,
    )

    // 🔥 新增：调试页面入口（只在开启时显示）
    if (showDebugPanel) {
        SettingsMenuItem(
            icon = Icons.Filled.BugReport,
            title = "调试页面",
            subtitle = "Function Call测试",
            onClick = {
                onDebugPageClick()
                onDismiss()
            },
            showArrow = true,
        )

        // 🔥 新增：Function Call快速测试菜单（与现有调试页面区分）
        SettingsMenuItem(
            icon = Icons.Filled.PlayArrow,
            title = "Function Call快速测试",
            subtitle = "快速模拟结果弹窗",
            onClick = onNavigateToDebugMenu,
            showArrow = true,
        )

        HorizontalDivider(
            modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium),
            color = MaterialTheme.coachTheme.dividerPrimary, // 🔥 【主题修复】使用Coach主题分割线色
        )
    }
}

/**
 * 设置菜单项组件
 */
@Composable
private fun SettingsMenuItem(
    icon: ImageVector,
    title: String,
    subtitle: String,
    onClick: () -> Unit,
    showArrow: Boolean = false,
    modifier: Modifier = Modifier,
) {
    DropdownMenuItem(
        text = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f),
                ) {
                    Icon(
                        imageVector = icon,
                        contentDescription = null,
                        tint = MaterialTheme.coachTheme.iconSecondary, // 🔥 【主题修复】使用Coach主题次要图标色
                        modifier = Modifier.size(20.dp),
                    )

                    Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                    Column {
                        Text(
                            text = title,
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.coachTheme.textPrimary, // 🔥 【主题修复】使用Coach主题主文本色
                            fontWeight = FontWeight.Medium,
                        )
                        Text(
                            text = subtitle,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.coachTheme.textSecondary, // 🔥 【主题修复】使用Coach主题次要文本色
                        )
                    }
                }

                if (showArrow) {
                    Icon(
                        imageVector = Icons.Filled.ChevronRight,
                        contentDescription = null,
                        tint = MaterialTheme.coachTheme.iconSecondary, // 🔥 【主题修复】使用Coach主题次要图标色
                        modifier = Modifier.size(16.dp),
                    )
                }
            }
        },
        onClick = onClick,
        modifier = modifier.padding(horizontal = Tokens.Spacing.Small),
        contentPadding = PaddingValues(
            horizontal = Tokens.Spacing.Medium,
            vertical = Tokens.Spacing.Small,
        ),
    )
}

/**
 * 模型选择子菜单
 */
@Composable
private fun ModelSelectionSubmenu(
    availableProviders: List<AiCoachContract.ApiProvider>,
    currentProvider: AiCoachContract.ApiProvider,
    getProviderDisplayName: (AiCoachContract.ApiProvider) -> String,
    getProviderModel: (AiCoachContract.ApiProvider) -> String,
    onProviderSelected: (AiCoachContract.ApiProvider) -> Unit,
    onBackToMain: () -> Unit,
    onDismiss: () -> Unit,
) {
    // 子菜单标题栏
    SubmenuHeader(
        title = "选择模型",
        onBackClick = onBackToMain,
    )

    HorizontalDivider(
        color = MaterialTheme.coachTheme.dividerPrimary, // 🔥 【主题修复】使用Coach主题分割线色
    )

    // 模型选项列表
    availableProviders.forEach { provider ->
        val isSelected = provider == currentProvider
        val displayName = getProviderDisplayName(provider)
        val modelName = getProviderModel(provider)

        ModelSelectionItem(
            displayName = displayName,
            modelName = modelName,
            isSelected = isSelected,
            onClick = {
                onProviderSelected(provider)
                onDismiss() // 选择后关闭菜单
            },
        )
    }
}

/**
 * Prompt选择子菜单
 */
@Composable
private fun PromptSelectionSubmenu(
    availablePrompts: List<String>,
    currentPrompt: String,
    getPromptDisplayInfo: (String) -> Pair<String, String>,
    onPromptSelected: (String) -> Unit,
    onBackToMain: () -> Unit,
    onDismiss: () -> Unit,
) {
    // 子菜单标题栏
    SubmenuHeader(
        title = "选择Prompt",
        onBackClick = onBackToMain,
    )

    HorizontalDivider(
        color = MaterialTheme.coachTheme.dividerPrimary, // 🔥 【主题修复】使用Coach主题分割线色
    )

    // Prompt选项列表
    availablePrompts.forEach { prompt ->
        val isSelected = prompt == currentPrompt
        val (displayName, description) = getPromptDisplayInfo(prompt)

        PromptSelectionItem(
            displayName = displayName,
            description = description,
            isSelected = isSelected,
            onClick = {
                onPromptSelected(prompt)
                onDismiss() // 选择后关闭菜单
            },
        )
    }
}

/**
 * 子菜单标题栏组件
 */
@Composable
private fun SubmenuHeader(
    title: String,
    onBackClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    DropdownMenuItem(
        text = {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth(),
            ) {
                IconButton(
                    onClick = onBackClick,
                    modifier = Modifier.size(24.dp),
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                        contentDescription = "返回",
                        tint = MaterialTheme.coachTheme.iconSecondary, // 🔥 【主题修复】使用Coach主题次要图标色
                        modifier = Modifier.size(16.dp),
                    )
                }

                Spacer(modifier = Modifier.width(Tokens.Spacing.Small))

                Text(
                    text = title,
                    style = MaterialTheme.typography.titleSmall,
                    color = MaterialTheme.coachTheme.textPrimary, // 🔥 【主题修复】使用Coach主题主文本色
                    fontWeight = FontWeight.Bold,
                )
            }
        },
        onClick = { }, // 标题栏不可点击
        enabled = false,
        modifier = modifier,
        contentPadding = PaddingValues(
            horizontal = Tokens.Spacing.Medium,
            vertical = Tokens.Spacing.Small,
        ),
    )
}

/**
 * 🔥 调试面板开关菜单项
 */
@Composable
private fun DebugToggleMenuItem(
    isEnabled: Boolean,
    onToggle: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
) {
    DropdownMenuItem(
        text = {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.weight(1f),
                ) {
                    Icon(
                        imageVector = if (isEnabled) Icons.Filled.DeveloperMode else Icons.Filled.Code,
                        contentDescription = null,
                        tint = if (isEnabled) MaterialTheme.coachTheme.iconPrimary else MaterialTheme.coachTheme.iconSecondary, // 🔥 【主题修复】使用Coach主题图标色
                        modifier = Modifier.size(20.dp),
                    )

                    Spacer(modifier = Modifier.width(Tokens.Spacing.Medium))

                    Column {
                        Text(
                            text = "调试模式",
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.coachTheme.textPrimary, // 🔥 【主题修复】使用Coach主题主文本色
                            fontWeight = FontWeight.Medium,
                        )
                        Text(
                            text = if (isEnabled) "已开启" else "已关闭",
                            style = MaterialTheme.typography.bodySmall,
                            color = if (isEnabled) MaterialTheme.coachTheme.textPrimary else MaterialTheme.coachTheme.textSecondary, // 🔥 【主题修复】使用Coach主题文本色
                        )
                    }
                }

                Switch(
                    checked = isEnabled,
                    onCheckedChange = onToggle,
                    modifier = Modifier.size(24.dp),
                )
            }
        },
        onClick = { onToggle(!isEnabled) },
        modifier = modifier.padding(horizontal = Tokens.Spacing.Small),
        contentPadding = PaddingValues(
            horizontal = Tokens.Spacing.Medium,
            vertical = Tokens.Spacing.Small,
        ),
    )
}

/**
 * 调试子菜单
 */
@Composable
private fun DebugSubmenu(
    debugScenarios:
    List<com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.DebugScenario>,
    realExecutionScenarios:
    List<com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.RealExecutionScenario>,
    onDebugScenarioSelected: (
        com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.DebugScenario,
    ) -> Unit,
    onRealExecutionSelected: (
        com.example.gymbro.features.coach.internal.debug.FunctionCallDebugData.RealExecutionScenario,
    ) -> Unit,
    onBackToMain: () -> Unit,
    onDismiss: () -> Unit,
) {
    // 返回按钮
    SettingsMenuItem(
        icon = Icons.AutoMirrored.Filled.ArrowBack,
        title = "返回",
        subtitle = "回到主菜单",
        onClick = onBackToMain,
        showArrow = false,
    )

    HorizontalDivider(
        modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium),
        color = MaterialTheme.coachTheme.dividerPrimary,
    )

    // 模拟数据场景
    if (debugScenarios.isNotEmpty()) {
        Text(
            text = "模拟数据测试",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.coachTheme.textSecondary,
            modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
        )

        debugScenarios.forEach { scenario ->
            SettingsMenuItem(
                icon = Icons.Filled.PlayArrow,
                title = scenario.displayName,
                subtitle = "快速UI测试",
                onClick = { onDebugScenarioSelected(scenario) },
                showArrow = false,
            )
        }

        HorizontalDivider(
            modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium),
            color = MaterialTheme.coachTheme.dividerPrimary,
        )
    }

    // 真实执行场景
    if (realExecutionScenarios.isNotEmpty()) {
        Text(
            text = "真实数据库写入",
            style = MaterialTheme.typography.labelMedium,
            color = MaterialTheme.coachTheme.textSecondary,
            modifier = Modifier.padding(horizontal = Tokens.Spacing.Medium, vertical = Tokens.Spacing.Small),
        )

        realExecutionScenarios.forEach { scenario ->
            SettingsMenuItem(
                icon = Icons.Filled.Storage,
                title = scenario.displayName,
                subtitle = scenario.description,
                onClick = { onRealExecutionSelected(scenario) },
                showArrow = false,
            )
        }
    }
}

/**
 * 🔥 Debug功能已移至CoachDebugScreen
 * 这个函数保留用于兼容性，但不再使用
 */

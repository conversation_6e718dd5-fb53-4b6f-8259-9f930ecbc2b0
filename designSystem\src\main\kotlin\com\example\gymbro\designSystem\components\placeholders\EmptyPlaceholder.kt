package com.example.gymbro.designSystem.components.placeholders

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.ChatBubbleOutline
import androidx.compose.material.icons.outlined.SearchOff
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme

/**
 * 标准化的空状态占位符组件
 *
 * 为各种空状态场景提供一致的视觉体验，
 * 支持自定义图标、消息和操作按钮。
 */
@Composable
fun EmptyPlaceholder(
    message: String,
    modifier: Modifier = Modifier,
    subtitle: String? = null,
    icon: ImageVector = Icons.Outlined.ChatBubbleOutline,
    actionButton: (@Composable () -> Unit)? = null,
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.Center,
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp),
            modifier = Modifier.padding(32.dp),
        ) {
            Icon(
                imageVector = icon,
                contentDescription = message,
                modifier = Modifier.size(64.dp),
                tint = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.6f),
            )

            Text(
                text = message,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center,
            )

            subtitle?.let { subtitleText ->
                Text(
                    text = subtitleText,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                    textAlign = TextAlign.Center,
                )
            }

            actionButton?.let { button ->
                Spacer(modifier = Modifier.height(8.dp))
                button()
            }
        }
    }
}

/**
 * 搜索空结果占位符（专用于搜索场景）
 */
@Composable
fun SearchEmptyPlaceholder(
    query: String,
    modifier: Modifier = Modifier,
    onClearSearch: (() -> Unit)? = null,
) {
    EmptyPlaceholder(
        message = "没有找到相关结果",
        subtitle = "试试其他关键词或检查拼写",
        icon = Icons.Outlined.SearchOff,
        modifier = modifier,
        actionButton = onClearSearch?.let { clearAction ->
            {
                OutlinedButton(
                    onClick = clearAction,
                    colors = ButtonDefaults.outlinedButtonColors(
                        contentColor = MaterialTheme.colorScheme.primary,
                    ),
                ) {
                    Text("清除搜索")
                }
            }
        },
    )
}

/**
 * 历史记录空状态占位符（专用于历史记录场景）
 */
@Composable
fun HistoryEmptyPlaceholder(
    modifier: Modifier = Modifier,
    onStartChat: (() -> Unit)? = null,
) {
    EmptyPlaceholder(
        message = "暂无聊天记录",
        subtitle = "开始新的对话，探索AI助手的强大功能",
        icon = Icons.Outlined.ChatBubbleOutline,
        modifier = modifier,
        actionButton = onStartChat?.let { startAction ->
            {
                Button(
                    onClick = startAction,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.primary,
                    ),
                ) {
                    Text("开始对话")
                }
            }
        },
    )
}

@GymBroPreview
@Composable
private fun EmptyPlaceholderPreview() {
    GymBroTheme {
        Surface {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(32.dp),
            ) {
                // 通用空状态
                EmptyPlaceholder(
                    message = "暂无数据",
                    subtitle = "当前没有可显示的内容",
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                )

                // 搜索空结果
                SearchEmptyPlaceholder(
                    query = "test",
                    onClearSearch = { },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                )

                // 历史记录空状态
                HistoryEmptyPlaceholder(
                    onStartChat = { },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp),
                )
            }
        }
    }
}

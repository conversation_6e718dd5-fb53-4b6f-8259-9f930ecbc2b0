package com.example.gymbro.domain.workout.repository

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.shared.models.workout.PlanProgressStatus
import com.example.gymbro.shared.models.workout.WorkoutPlan
import kotlinx.coroutines.flow.Flow

/**
 * 计划仓库接口 - PlanDB
 *
 * 基于四数据库架构设计
 * 负责训练计划管理，与PlanRepositoryImpl完全匹配
 *
 * ✅ Plan层TemplateVersion适配 - Phase 2
 * - 新增TemplateVersion解析支持
 * - 复用已有的TemplateVersionUseCase
 * - 保持现有接口向后兼容
 */
interface PlanRepository {

    // ==================== Plan 基础操作 ====================

    /**
     * 根据ID获取计划
     */
    suspend fun getPlan(planId: String): ModernResult<WorkoutPlan>

    /**
     * 根据ID获取计划 (简化版，直接返回计划或null)
     */
    suspend fun getPlanById(planId: String): WorkoutPlan?

    /**
     * 获取用户的所有计划
     */
    suspend fun getUserPlans(userId: String): Flow<List<WorkoutPlan>>

    /**
     * 保存计划
     */
    suspend fun savePlan(plan: WorkoutPlan): ModernResult<Unit>

    /**
     * 删除计划
     */
    suspend fun deletePlan(planId: String): ModernResult<Unit>

    /**
     * 获取计划内的所有模板
     * 用于Session选择器中Plan→Template展开
     */
    suspend fun getPlanTemplates(planId: String): ModernResult<List<WorkoutTemplate>>

    /**
     * 搜索计划
     */
    suspend fun searchPlans(userId: String, query: String): Flow<List<WorkoutPlan>>

    /**
     * 获取活跃计划
     */
    suspend fun getActivePlans(userId: String): Flow<List<WorkoutPlan>>

    // ==================== Plan 分类操作 ====================

    /**
     * 获取收藏计划
     */
    suspend fun getFavoritePlans(userId: String): Flow<List<WorkoutPlan>>

    /**
     * 获取AI生成的计划
     */
    suspend fun getAIGeneratedPlans(userId: String): Flow<List<WorkoutPlan>>

    /**
     * 切换计划收藏状态
     */
    suspend fun togglePlanFavorite(planId: String): ModernResult<Unit>

    /**
     * 标记计划为AI生成
     */
    suspend fun markPlanAsAIGenerated(planId: String): ModernResult<Unit>

    // ==================== ✅ Phase 2新增：TemplateVersion适配 ====================

    /**
     * 创建Plan并自动解析TemplateVersion
     * 复用TemplateVersionUseCase的成功模式
     *
     * 工作流程：
     * 1. 收集Plan中的所有templateIds
     * 2. 调用TemplateVersionUseCase.batchGetLatestVersions解析为TemplateVersion
     * 3. 更新DayPlan中的templateVersionIds字段
     * 4. 保存解析后的Plan
     */
    suspend fun createPlanWithVersionResolution(plan: WorkoutPlan): ModernResult<WorkoutPlan>

    /**
     * 解析Plan中的Template ID为TemplateVersion ID
     * 内部使用TemplateVersionUseCase.batchGetLatestVersions
     *
     * 核心功能：
     * - 批量解析Plan.dailySchedule中的templateIds
     * - 使用autoCreateIfNotExist=true确保版本存在
     * - 保留原templateIds用于兼容性检查
     * - 返回更新后的Plan（不保存到数据库）
     */
    suspend fun resolvePlanTemplateVersions(plan: WorkoutPlan): ModernResult<WorkoutPlan>

    /**
     * 获取所有Plan列表（无过滤）
     * 用于数据迁移时获取现有Plan
     */
    suspend fun getAllPlans(): List<WorkoutPlan>

    // ==================== Plan Calendar JSON 输出 ====================

    /**
     * 生成计划的calendar.json数据
     *
     * 🎯 核心功能：实现Plan层的calendar.json输出要求
     * 遵循Function Call输出模式，支持日历视图展示
     *
     * @param planId 计划ID
     * @param startDate 开始日期（格式：YYYY-MM-DD）
     * @return PlanCalendarData 日历数据
     */
    suspend fun generatePlanCalendarJson(
        planId: String,
        startDate: String,
    ): ModernResult<com.example.gymbro.shared.models.workout.PlanCalendarData>

    // ==================== Progress Management ====================

    /**
     * 更新计划中特定天的进度状态
     *
     * @param planId 计划ID
     * @param dayNumber 天数（从1开始）
     * @param status 新的进度状态
     */
    suspend fun updateDayProgress(
        planId: String,
        dayNumber: Int,
        status: PlanProgressStatus,
    )

    /**
     * 观察计划变化，包括进度更新
     *
     * @param planId 计划ID
     * @return Flow发射更新的WorkoutPlan
     */
    fun observePlan(planId: String): Flow<WorkoutPlan>
}

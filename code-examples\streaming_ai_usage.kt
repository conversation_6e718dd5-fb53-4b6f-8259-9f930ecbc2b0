package com.example.gymbro.examples

import androidx.compose.runtime.Immutable
import androidx.lifecycle.viewModelScope
import com.example.gymbro.core.arch.mvi.BaseMviViewModel
import com.example.gymbro.core.arch.mvi.Reducer
import com.example.gymbro.core.arch.mvi.ReduceResult
import com.example.gymbro.core.arch.mvi.UiEffect
import com.example.gymbro.core.arch.mvi.UiState
import com.example.gymbro.core.arch.mvi.AppIntent
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.features.thinkingbox.domain.interfaces.StreamingState
import com.example.gymbro.features.thinkingbox.domain.interfaces.ThinkingState
import com.example.gymbro.shared.models.ai.StreamChunk
import com.example.gymbro.shared.models.ai.FunctionCall
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * AI流式响应处理示例
 *
 * 本示例展示了GymBro项目中AI流式响应的完整处理模式：
 * 1. WebSocket流式数据接收和解析
 * 2. ThinkingBox实时思考过程渲染
 * 3. 流式文本逐字显示和打字机效果
 * 4. Function Call实时检测和处理
 * 5. 错误恢复和重连机制
 *
 * 技术特点：
 * - 双时序架构：思考阶段 + 回答阶段
 * - 实时解析：XML标签流式解析
 * - 动画引擎：流畅的打字机和渐现效果
 * - 跨模块集成：Coach + ThinkingBox + Core
 */

// ========================================
// 1. Streaming AI Contract定义
// ========================================

/**
 * 流式AI响应的MVI契约
 *
 * 设计原则：
 * - 支持双时序架构（thinking + answer）
 * - 实时状态更新和UI同步
 * - Function Call流式检测
 * - 错误处理和重连机制
 */
object StreamingAiContract {

    /**
     * 流式AI相关意图
     */
    sealed interface Intent : AppIntent {
        // 流式会话控制
        data class StartStreaming(val prompt: String) : Intent
        object StopStreaming : Intent
        object PauseStreaming : Intent
        object ResumeStreaming : Intent
        
        // 流式数据处理
        data class ProcessStreamChunk(val chunk: StreamChunk) : Intent
        data class HandleStreamError(val error: Throwable) : Intent
        data class StreamCompleted(val finalResponse: String) : Intent
        
        // ThinkingBox集成
        data class UpdateThinkingState(val thinkingState: ThinkingState) : Intent
        data class ThinkingCompleted(val summary: String) : Intent
        
        // Function Call检测
        data class FunctionCallDetected(val functionCall: FunctionCall) : Intent
        data class FunctionCallCompleted(val result: String) : Intent
        
        // 动画控制
        object StartTypingAnimation : Intent
        object PauseTypingAnimation : Intent
        data class UpdateTypingSpeed(val speed: Long) : Intent
        
        // 内部状态Intent
        data class StreamConnectionResult(val isConnected: Boolean, val error: String? = null) : Intent
    }

    /**
     * 流式AI UI状态
     */
    @Immutable
    data class State(
        // 连接状态
        val isConnecting: Boolean = false,
        val isConnected: Boolean = false,
        val connectionError: String? = null,
        
        // 流式状态
        val isStreaming: Boolean = false,
        val isPaused: Boolean = false,
        val currentPrompt: String = "",
        
        // 内容状态
        val thinkingContent: String = "",
        val responseContent: String = "",
        val displayedContent: String = "",
        
        // ThinkingBox状态
        val thinkingState: ThinkingState = ThinkingState.IDLE,
        val streamingState: StreamingState = StreamingState.THINKING,
        val thinkingSummary: String = "",
        
        // Function Call状态
        val detectedFunctionCalls: List<FunctionCall> = emptyList(),
        val activeFunctionCall: FunctionCall? = null,
        val functionCallResults: Map<String, String> = emptyMap(),
        
        // 动画状态
        val isTypingAnimationActive: Boolean = false,
        val typingSpeed: Long = 50L, // 毫秒
        val lastTypedIndex: Int = 0,
        
        // 错误状态
        val error: UiText? = null,
        val retryCount: Int = 0
    ) : UiState

    /**
     * 流式AI副作用
     */
    sealed interface Effect : UiEffect {
        // 连接管理
        object InitializeWebSocket : Effect
        object CloseWebSocket : Effect
        data class SendMessage(val message: String) : Effect
        
        // 动画控制
        object StartTypingEffect : Effect
        object StopTypingEffect : Effect
        data class ScrollToBottom(val animated: Boolean = true) : Effect
        
        // ThinkingBox控制
        data class UpdateThinkingBox(val content: String, val state: ThinkingState) : Effect
        object ShowThinkingBox : Effect
        object HideThinkingBox : Effect
        
        // Function Call处理
        data class ExecuteFunctionCall(val functionCall: FunctionCall) : Effect
        data class ShowFunctionCallResult(val result: String) : Effect
        
        // 用户反馈
        data class ShowError(val message: UiText) : Effect
        data class ShowRetryOption(val retryCount: Int) : Effect
        object HapticFeedback : Effect
    }
}

// ========================================
// 2. 流式数据模型
// ========================================

/**
 * 流式数据块定义
 */
@Immutable
data class StreamChunk(
    val type: ChunkType,
    val content: String,
    val timestamp: Long = System.currentTimeMillis(),
    val metadata: Map<String, String> = emptyMap()
)

/**
 * 流式数据类型
 */
enum class ChunkType {
    THINKING_START,      // <thinking>
    THINKING_CONTENT,    // 思考内容
    THINKING_END,        // </thinking>
    ANSWER_START,        // 回答开始
    ANSWER_CONTENT,      // 回答内容
    FUNCTION_CALL,       // Function Call检测
    STREAM_END,          // 流结束
    ERROR                // 错误
}

/**
 * ThinkingBox状态枚举
 */
enum class ThinkingPhase {
    ANALYZING,      // 分析问题
    PLANNING,       // 制定计划
    THINKING,       // 深度思考
    CONCLUDING,     // 总结结论
    COMPLETED       // 思考完成
}

// ========================================
// 3. Streaming AI Reducer
// ========================================

/**
 * 流式AI Reducer实现
 *
 * 核心职责：
 * - 处理WebSocket连接状态
 * - 管理流式数据解析和状态更新
 * - 协调ThinkingBox和typing动画
 * - 处理Function Call检测
 */
class StreamingAiReducer @Inject constructor() :
    Reducer<StreamingAiContract.Intent, StreamingAiContract.State, StreamingAiContract.Effect> {

    override fun reduce(
        intent: StreamingAiContract.Intent,
        currentState: StreamingAiContract.State
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        
        Timber.d("StreamingAiReducer处理Intent: ${intent::class.simpleName}")
        
        return when (intent) {
            is StreamingAiContract.Intent.StartStreaming -> handleStartStreaming(currentState, intent.prompt)
            is StreamingAiContract.Intent.StopStreaming -> handleStopStreaming(currentState)
            is StreamingAiContract.Intent.PauseStreaming -> handlePauseStreaming(currentState)
            is StreamingAiContract.Intent.ResumeStreaming -> handleResumeStreaming(currentState)
            is StreamingAiContract.Intent.ProcessStreamChunk -> handleProcessStreamChunk(currentState, intent.chunk)
            is StreamingAiContract.Intent.HandleStreamError -> handleStreamError(currentState, intent.error)
            is StreamingAiContract.Intent.StreamCompleted -> handleStreamCompleted(currentState, intent.finalResponse)
            is StreamingAiContract.Intent.UpdateThinkingState -> handleUpdateThinkingState(currentState, intent.thinkingState)
            is StreamingAiContract.Intent.ThinkingCompleted -> handleThinkingCompleted(currentState, intent.summary)
            is StreamingAiContract.Intent.FunctionCallDetected -> handleFunctionCallDetected(currentState, intent.functionCall)
            is StreamingAiContract.Intent.FunctionCallCompleted -> handleFunctionCallCompleted(currentState, intent.result)
            is StreamingAiContract.Intent.StartTypingAnimation -> handleStartTypingAnimation(currentState)
            is StreamingAiContract.Intent.PauseTypingAnimation -> handlePauseTypingAnimation(currentState)
            is StreamingAiContract.Intent.UpdateTypingSpeed -> handleUpdateTypingSpeed(currentState, intent.speed)
            is StreamingAiContract.Intent.StreamConnectionResult -> handleConnectionResult(currentState, intent.isConnected, intent.error)
        }
    }

    private fun handleStartStreaming(
        state: StreamingAiContract.State,
        prompt: String
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(
            isConnecting = true,
            currentPrompt = prompt,
            thinkingContent = "",
            responseContent = "",
            displayedContent = "",
            detectedFunctionCalls = emptyList(),
            functionCallResults = emptyMap(),
            streamingState = StreamingState.THINKING,
            thinkingState = ThinkingState.ACTIVE,
            error = null,
            retryCount = 0
        )
        
        val effect = StreamingAiContract.Effect.InitializeWebSocket
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleStopStreaming(state: StreamingAiContract.State): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(
            isStreaming = false,
            isPaused = false,
            isConnected = false,
            streamingState = StreamingState.IDLE,
            thinkingState = ThinkingState.IDLE,
            isTypingAnimationActive = false
        )
        
        val effect = StreamingAiContract.Effect.CloseWebSocket
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handlePauseStreaming(state: StreamingAiContract.State): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(
            isPaused = true,
            isTypingAnimationActive = false
        )
        
        val effect = StreamingAiContract.Effect.StopTypingEffect
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleResumeStreaming(state: StreamingAiContract.State): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(
            isPaused = false,
            isTypingAnimationActive = true
        )
        
        val effect = StreamingAiContract.Effect.StartTypingEffect
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleProcessStreamChunk(
        state: StreamingAiContract.State,
        chunk: StreamChunk
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        return when (chunk.type) {
            ChunkType.THINKING_START -> {
                val newState = state.copy(
                    streamingState = StreamingState.THINKING,
                    thinkingState = ThinkingState.ACTIVE
                )
                val effect = StreamingAiContract.Effect.ShowThinkingBox
                ReduceResult.withEffect(newState, effect)
            }
            
            ChunkType.THINKING_CONTENT -> {
                val newState = state.copy(
                    thinkingContent = state.thinkingContent + chunk.content
                )
                val effect = StreamingAiContract.Effect.UpdateThinkingBox(
                    newState.thinkingContent, 
                    ThinkingState.ACTIVE
                )
                ReduceResult.withEffect(newState, effect)
            }
            
            ChunkType.THINKING_END -> {
                val newState = state.copy(
                    streamingState = StreamingState.ANSWERING,
                    thinkingState = ThinkingState.COMPLETED,
                    thinkingSummary = state.thinkingContent
                )
                val effect = StreamingAiContract.Effect.HideThinkingBox
                ReduceResult.withEffect(newState, effect)
            }
            
            ChunkType.ANSWER_START -> {
                val newState = state.copy(
                    streamingState = StreamingState.ANSWERING,
                    isTypingAnimationActive = true
                )
                val effect = StreamingAiContract.Effect.StartTypingEffect
                ReduceResult.withEffect(newState, effect)
            }
            
            ChunkType.ANSWER_CONTENT -> {
                val newState = state.copy(
                    responseContent = state.responseContent + chunk.content
                )
                ReduceResult.stateOnly(newState)
            }
            
            ChunkType.FUNCTION_CALL -> {
                // 解析Function Call
                val functionCall = parseFunctionCall(chunk.content)
                if (functionCall != null) {
                    val newState = state.copy(
                        detectedFunctionCalls = state.detectedFunctionCalls + functionCall,
                        activeFunctionCall = functionCall
                    )
                    val effect = StreamingAiContract.Effect.ExecuteFunctionCall(functionCall)
                    ReduceResult.withEffect(newState, effect)
                } else {
                    ReduceResult.stateOnly(state)
                }
            }
            
            ChunkType.STREAM_END -> {
                val newState = state.copy(
                    isStreaming = false,
                    streamingState = StreamingState.COMPLETED,
                    isTypingAnimationActive = false
                )
                val effect = StreamingAiContract.Effect.StopTypingEffect
                ReduceResult.withEffect(newState, effect)
            }
            
            ChunkType.ERROR -> {
                val newState = state.copy(
                    error = UiText.DynamicString(chunk.content),
                    isStreaming = false,
                    isTypingAnimationActive = false
                )
                val effect = StreamingAiContract.Effect.ShowError(UiText.DynamicString(chunk.content))
                ReduceResult.withEffect(newState, effect)
            }
        }
    }

    private fun handleStreamError(
        state: StreamingAiContract.State,
        error: Throwable
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(
            error = UiText.DynamicString(error.message ?: "流式连接错误"),
            isStreaming = false,
            isConnected = false,
            retryCount = state.retryCount + 1
        )
        
        val effect = if (state.retryCount < 3) {
            StreamingAiContract.Effect.ShowRetryOption(state.retryCount + 1)
        } else {
            StreamingAiContract.Effect.ShowError(UiText.DynamicString("连接失败，请检查网络"))
        }
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleStreamCompleted(
        state: StreamingAiContract.State,
        finalResponse: String
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(
            responseContent = finalResponse,
            displayedContent = finalResponse,
            isStreaming = false,
            streamingState = StreamingState.COMPLETED,
            isTypingAnimationActive = false
        )
        
        val effect = StreamingAiContract.Effect.HapticFeedback
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleUpdateThinkingState(
        state: StreamingAiContract.State,
        thinkingState: ThinkingState
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(thinkingState = thinkingState)
        
        val effect = StreamingAiContract.Effect.UpdateThinkingBox(
            state.thinkingContent,
            thinkingState
        )
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleThinkingCompleted(
        state: StreamingAiContract.State,
        summary: String
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(
            thinkingSummary = summary,
            thinkingState = ThinkingState.COMPLETED
        )
        
        val effect = StreamingAiContract.Effect.HideThinkingBox
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleFunctionCallDetected(
        state: StreamingAiContract.State,
        functionCall: FunctionCall
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(
            detectedFunctionCalls = state.detectedFunctionCalls + functionCall,
            activeFunctionCall = functionCall
        )
        
        val effect = StreamingAiContract.Effect.ExecuteFunctionCall(functionCall)
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleFunctionCallCompleted(
        state: StreamingAiContract.State,
        result: String
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val functionCallName = state.activeFunctionCall?.name ?: "unknown"
        
        val newState = state.copy(
            functionCallResults = state.functionCallResults + (functionCallName to result),
            activeFunctionCall = null
        )
        
        val effect = StreamingAiContract.Effect.ShowFunctionCallResult(result)
        
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleStartTypingAnimation(state: StreamingAiContract.State): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(isTypingAnimationActive = true)
        val effect = StreamingAiContract.Effect.StartTypingEffect
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handlePauseTypingAnimation(state: StreamingAiContract.State): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(isTypingAnimationActive = false)
        val effect = StreamingAiContract.Effect.StopTypingEffect
        return ReduceResult.withEffect(newState, effect)
    }

    private fun handleUpdateTypingSpeed(state: StreamingAiContract.State, speed: Long): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(typingSpeed = speed)
        return ReduceResult.stateOnly(newState)
    }

    private fun handleConnectionResult(
        state: StreamingAiContract.State,
        isConnected: Boolean,
        error: String?
    ): ReduceResult<StreamingAiContract.State, StreamingAiContract.Effect> {
        val newState = state.copy(
            isConnecting = false,
            isConnected = isConnected,
            connectionError = error,
            isStreaming = isConnected
        )
        
        val effect = if (isConnected) {
            StreamingAiContract.Effect.SendMessage(state.currentPrompt)
        } else {
            StreamingAiContract.Effect.ShowError(UiText.DynamicString(error ?: "连接失败"))
        }
        
        return ReduceResult.withEffect(newState, effect)
    }

    /**
     * Function Call解析工具
     */
    private fun parseFunctionCall(content: String): FunctionCall? {
        return try {
            // 简化的Function Call解析逻辑
            // 实际实现中会使用JSON解析
            if (content.contains("gymbro.")) {
                val functionName = content.substringAfter("\"name\":\"").substringBefore("\"")
                val arguments = content.substringAfter("\"arguments\":{").substringBefore("}")
                
                FunctionCall(
                    name = functionName,
                    arguments = mapOf("query" to arguments)
                )
            } else {
                null
            }
        } catch (e: Exception) {
            Timber.w(e, "Function Call解析失败")
            null
        }
    }
}

// ========================================
// 4. Streaming AI ViewModel
// ========================================

/**
 * 流式AI ViewModel实现
 *
 * 核心职责：
 * - 管理WebSocket连接和流式数据
 * - 协调ThinkingBox和打字机动画
 * - 处理Function Call检测和执行
 * - 提供错误恢复和重连机制
 */
@HiltViewModel
class StreamingAiViewModel @Inject constructor(
    private val reducer: StreamingAiReducer,
    private val webSocketService: WebSocketService,
    private val streamParser: StreamParser
) : BaseMviViewModel<StreamingAiContract.Intent, StreamingAiContract.State, StreamingAiContract.Effect>(
    initialState = StreamingAiContract.State()
) {

    override val reducer: Reducer<StreamingAiContract.Intent, StreamingAiContract.State, StreamingAiContract.Effect> = this.reducer

    init {
        Timber.d("StreamingAiViewModel初始化")
        initializeEffectHandler()
    }

    override fun initializeEffectHandler() {
        viewModelScope.launch {
            effects.collect { effect ->
                handleEffect(effect)
            }
        }
    }

    /**
     * Effect处理器
     */
    private suspend fun handleEffect(effect: StreamingAiContract.Effect) {
        Timber.d("处理StreamingAiEffect: ${effect::class.simpleName}")
        
        when (effect) {
            is StreamingAiContract.Effect.InitializeWebSocket -> {
                initializeWebSocketConnection()
            }
            is StreamingAiContract.Effect.CloseWebSocket -> {
                webSocketService.close()
            }
            is StreamingAiContract.Effect.SendMessage -> {
                webSocketService.sendMessage(effect.message)
            }
            is StreamingAiContract.Effect.StartTypingEffect -> {
                startTypingAnimation()
            }
            is StreamingAiContract.Effect.StopTypingEffect -> {
                // UI层处理打字机动画停止
            }
            is StreamingAiContract.Effect.ScrollToBottom -> {
                // UI层处理滚动
            }
            is StreamingAiContract.Effect.UpdateThinkingBox -> {
                // UI层更新ThinkingBox
            }
            is StreamingAiContract.Effect.ShowThinkingBox -> {
                // UI层显示ThinkingBox
            }
            is StreamingAiContract.Effect.HideThinkingBox -> {
                // UI层隐藏ThinkingBox
            }
            is StreamingAiContract.Effect.ExecuteFunctionCall -> {
                // 执行Function Call（可能需要调用其他ViewModel）
            }
            is StreamingAiContract.Effect.ShowFunctionCallResult -> {
                // UI层显示Function Call结果
            }
            is StreamingAiContract.Effect.ShowError -> {
                // UI层显示错误消息
            }
            is StreamingAiContract.Effect.ShowRetryOption -> {
                // UI层显示重试选项
            }
            is StreamingAiContract.Effect.HapticFeedback -> {
                // UI层触发触觉反馈
            }
        }
    }

    /**
     * WebSocket连接初始化
     */
    private suspend fun initializeWebSocketConnection() {
        try {
            val streamFlow = webSocketService.connect()
            
            streamFlow
                .onEach { message ->
                    val chunks = streamParser.parseMessage(message)
                    chunks.forEach { chunk ->
                        super.dispatch(StreamingAiContract.Intent.ProcessStreamChunk(chunk))
                    }
                }
                .catch { error ->
                    super.dispatch(StreamingAiContract.Intent.HandleStreamError(error))
                }
                .collect()
                
            super.dispatch(StreamingAiContract.Intent.StreamConnectionResult(true))
            
        } catch (e: Exception) {
            super.dispatch(StreamingAiContract.Intent.StreamConnectionResult(false, e.message))
        }
    }

    /**
     * 打字机动画启动
     */
    private suspend fun startTypingAnimation() {
        // 实现打字机效果的逐字显示逻辑
        val currentContent = currentState.responseContent
        val currentDisplayed = currentState.displayedContent
        
        if (currentDisplayed.length < currentContent.length) {
            // 继续显示下一个字符
            kotlinx.coroutines.delay(currentState.typingSpeed)
            
            val nextDisplayed = currentContent.substring(0, currentDisplayed.length + 1)
            
            super.dispatch(StreamingAiContract.Intent.ProcessStreamChunk(
                StreamChunk(
                    type = ChunkType.ANSWER_CONTENT,
                    content = nextDisplayed
                )
            ))
        }
    }

    // 便捷方法供UI层调用
    fun startStreaming(prompt: String) = dispatch(StreamingAiContract.Intent.StartStreaming(prompt))
    fun stopStreaming() = dispatch(StreamingAiContract.Intent.StopStreaming)
    fun pauseStreaming() = dispatch(StreamingAiContract.Intent.PauseStreaming)
    fun resumeStreaming() = dispatch(StreamingAiContract.Intent.ResumeStreaming)
    fun updateTypingSpeed(speed: Long) = dispatch(StreamingAiContract.Intent.UpdateTypingSpeed(speed))
}

// ========================================
// 5. 服务接口定义
// ========================================

/**
 * WebSocket服务接口
 */
interface WebSocketService {
    suspend fun connect(): Flow<String>
    suspend fun sendMessage(message: String)
    suspend fun close()
}

/**
 * 流式解析器接口
 */
interface StreamParser {
    fun parseMessage(message: String): List<StreamChunk>
}

// ========================================
// 6. 使用模式说明
// ========================================

/**
 * 流式AI使用模式示例
 * 
 * 在AI Chat界面中的典型用法：
 * 
 * ```kotlin
 * @Composable
 * fun StreamingAiChatScreen(
 *     streamingViewModel: StreamingAiViewModel = hiltViewModel()
 * ) {
 *     val state by streamingViewModel.state.collectAsStateWithLifecycle()
 *     
 *     // 监听流式Effects
 *     LaunchedEffect(streamingViewModel) {
 *         streamingViewModel.effects.collect { effect ->
 *             when (effect) {
 *                 is StreamingAiContract.Effect.StartTypingEffect -> {
 *                     // 启动打字机动画
 *                 }
 *                 is StreamingAiContract.Effect.UpdateThinkingBox -> {
 *                     // 更新ThinkingBox内容
 *                 }
 *                 is StreamingAiContract.Effect.ScrollToBottom -> {
 *                     // 滚动到底部
 *                 }
 *             }
 *         }
 *     }
 *     
 *     Column {
 *         // ThinkingBox
 *         if (state.thinkingState == ThinkingState.ACTIVE) {
 *             ThinkingBox(
 *                 content = state.thinkingContent,
 *                 state = state.thinkingState
 *             )
 *         }
 *         
 *         // 流式响应内容
 *         StreamingText(
 *             content = state.displayedContent,
 *             isTyping = state.isTypingAnimationActive,
 *             typingSpeed = state.typingSpeed
 *         )
 *         
 *         // Function Call结果
 *         state.detectedFunctionCalls.forEach { functionCall ->
 *             FunctionCallResult(
 *                 functionCall = functionCall,
 *                 result = state.functionCallResults[functionCall.name]
 *             )
 *         }
 *         
 *         // 输入区域
 *         MessageInput(
 *             enabled = !state.isStreaming,
 *             onSend = { message -> streamingViewModel.startStreaming(message) }
 *         )
 *     }
 * }
 * ```
 * 
 * ThinkingBox集成示例：
 * 
 * ```kotlin
 * @Composable
 * fun ThinkingBox(
 *     content: String,
 *     state: ThinkingState
 * ) {
 *     AnimatedVisibility(
 *         visible = state == ThinkingState.ACTIVE,
 *         enter = slideInVertically() + fadeIn(),
 *         exit = slideOutVertically() + fadeOut()
 *     ) {
 *         Card {
 *             Column {
 *                 Text("AI正在思考...")
 *                 
 *                 // 流式思考内容
 *                 TypewriterText(
 *                     text = content,
 *                     isActive = true
 *                 )
 *             }
 *         }
 *     }
 * }
 * ```
 */

/**
 * 流式AI架构总结
 * 
 * 1. **双时序架构**：
 *    - Thinking阶段：显示AI思考过程
 *    - Answer阶段：流式显示最终回答
 *    - 无缝切换：ThinkingBox → StreamingText
 * 
 * 2. **实时解析**：
 *    - XML标签流式解析
 *    - Function Call实时检测
 *    - 错误恢复和重连机制
 * 
 * 3. **动画集成**：
 *    - 打字机效果：逐字显示
 *    - 渐现动画：ThinkingBox显示/隐藏
 *    - 滚动跟随：自动滚动到最新内容
 * 
 * 4. **跨模块协作**：
 *    - Coach模块：流式响应管理
 *    - ThinkingBox模块：思考过程渲染
 *    - Core模块：WebSocket和解析服务
 *    - Function Call模块：工具调用处理
 */
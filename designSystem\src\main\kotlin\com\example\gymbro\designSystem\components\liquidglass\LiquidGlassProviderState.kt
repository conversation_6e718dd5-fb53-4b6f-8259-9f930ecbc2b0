package com.example.gymbro.designSystem.components.liquidglass

import androidx.compose.runtime.*
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.layer.GraphicsLayer
import androidx.compose.ui.graphics.rememberGraphicsLayer

@Composable
fun rememberLiquidGlassProviderState(
    backgroundColor: Color?,
): LiquidGlassProviderState {
    val graphicsLayer = rememberGraphicsLayer()
    return remember(backgroundColor, graphicsLayer) {
        LiquidGlassProviderState(
            backgroundColor = backgroundColor,
            graphicsLayer = graphicsLayer,
        )
    }
}

@Stable
class LiquidGlassProviderState internal constructor(
    val backgroundColor: Color?,
    internal val graphicsLayer: GraphicsLayer,
) {

    internal var rect: Rect? by mutableStateOf(null)
}

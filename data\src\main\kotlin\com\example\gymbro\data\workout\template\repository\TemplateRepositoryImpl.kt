package com.example.gymbro.data.workout.template.repository

import androidx.room.withTransaction
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.extensions.safeCatch
import com.example.gymbro.core.error.extensions.toModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.workout.template.dao.ExerciseInTemplateDao
import com.example.gymbro.data.workout.template.dao.TemplateDao
import com.example.gymbro.data.workout.template.dao.TemplateVersionDao
import com.example.gymbro.data.workout.template.database.TemplateDatabase
import com.example.gymbro.data.workout.template.json.TemplateJsonDataProcessor
import com.example.gymbro.data.workout.template.mapper.toDomain
import com.example.gymbro.data.workout.template.mapper.toDomainSafely
import com.example.gymbro.data.workout.template.mapper.toDomainWithExercisesSafely
import com.example.gymbro.data.workout.template.mapper.toEntity
import com.example.gymbro.data.workout.template.mapper.toExerciseInTemplateEntitySafely
import com.example.gymbro.domain.workout.mapper.toDomain
import com.example.gymbro.domain.workout.model.template.TemplateExercise
import com.example.gymbro.domain.workout.model.template.TemplateVersion
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.TemplateRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * TemplateRepository 实现 - Phase 2 架构重构
 *
 * 基于 05_Final_Database_Architecture.md 设计
 * 负责训练模板管理，统一使用 safeCatch 和 ModernResult 封装异常
 * Phase 2: 添加事务支持，确保数据一致性
 *
 * <AUTHOR> 4.0 sonnet
 */
@Singleton
class TemplateRepositoryImpl @Inject constructor(
    private val templateDatabase: TemplateDatabase,
    private val templateDao: TemplateDao,
    private val exerciseInTemplateDao: ExerciseInTemplateDao,
    private val versionDao: TemplateVersionDao,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) : TemplateRepository {

    // ==================== Template 基础操作 ====================

    override suspend fun getTemplateById(templateId: String): ModernResult<WorkoutTemplate?> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("获取模板: templateId=$templateId")
                val templateEntity = templateDao.getTemplateById(templateId)
                if (templateEntity != null) {
                    // 优先使用JSON格式
                    if (templateEntity.jsonData != null) {
                        Timber.tag("WK-TEMPLATE").d("使用 JSON 数据加载模板: templateId=$templateId")
                        Timber.tag("WK-TEMPLATE").d("JSON 数据长度: ${templateEntity.jsonData.length}")
                        Timber.tag("WK-TEMPLATE").d("JSON 数据预览: ${templateEntity.jsonData.take(200)}...")

                        val templateFromJson = TemplateJsonDataProcessor.fromJson(templateEntity.jsonData)
                        if (templateFromJson != null) {
                            Timber.tag("WK-TEMPLATE").d("JSON 解析成功，模板名称: ${templateFromJson.name}")
                            Timber.tag("WK-TEMPLATE").d("JSON 解析成功，动作数量: ${templateFromJson.exercises.size}")
                            templateFromJson.exercises.forEachIndexed { index, exercise ->
                                Timber.tag(
                                    "WK-TEMPLATE",
                                ).d(
                                    "JSON动作${index + 1}: ${exercise.name}, sets=${exercise.sets}, weight=${exercise.weight}",
                                )
                                Timber.tag(
                                    "WK-TEMPLATE",
                                ).d("JSON动作${index + 1} notes长度: ${exercise.notes?.length}")
                            }
                            // 直接返回Domain模型
                            templateFromJson
                        } else {
                            Timber.tag("WK-TEMPLATE").e("JSON解析失败，回退到关系型数据")
                            // 回退到关系型数据
                            val exercises = exerciseInTemplateDao.getExercisesInTemplateSync(templateId)
                            Timber.tag("WK-TEMPLATE").d("关系型数据加载，动作数量: ${exercises.size}")
                            when (
                                val legacyResult = templateEntity.toDomainWithExercisesSafely(
                                    exercises,
                                )
                            ) {
                                is ModernResult.Success -> legacyResult.data
                                is ModernResult.Error -> throw Exception(legacyResult.error.message)
                                is ModernResult.Loading -> throw Exception("Unexpected loading state")
                            }
                        }
                    } else {
                        // 使用关系型数据
                        val exercises = exerciseInTemplateDao.getExercisesInTemplateSync(templateId)
                        when (val result = templateEntity.toDomainWithExercisesSafely(exercises)) {
                            is ModernResult.Success -> result.data
                            is ModernResult.Error -> throw Exception(result.error.message)
                            is ModernResult.Loading -> throw Exception("Unexpected loading state")
                        }
                    }
                } else {
                    null
                }
            }
        }

    override fun getTemplatesByUser(userId: String): Flow<ModernResult<List<WorkoutTemplate>>> =
        templateDao.getTemplatesByUser(userId)
            .map { entities ->
                // 🔥 修复关键Bug：正确加载每个模板的exercises数据
                try {
                    val templatesWithExercises = entities.map { entity ->
                        // 优先使用JSON格式，与getTemplateById保持一致
                        if (entity.jsonData != null) {
                            Timber.d("🔍 [QUERY-FIX] 使用JSON数据加载模板: ${entity.name}")
                            val templateFromJson = TemplateJsonDataProcessor.fromJson(entity.jsonData)
                            if (templateFromJson != null) {
                                Timber.d(
                                    "🔍 [QUERY-FIX] JSON解析成功: ${templateFromJson.name}, exercises=${templateFromJson.exercises.size}",
                                )
                                // 直接返回Domain模型
                                templateFromJson
                            } else {
                                Timber.w("🔍 [QUERY-FIX] JSON解析失败，回退到关系型数据")
                                // 回退到关系型数据
                                val exercises = exerciseInTemplateDao.getExercisesInTemplateSync(entity.id)
                                when (val legacyResult = entity.toDomainWithExercisesSafely(exercises)) {
                                    is ModernResult.Success -> legacyResult.data
                                    is ModernResult.Error -> {
                                        Timber.e("🔍 [QUERY-FIX] 关系型数据加载也失败，返回空模板: ${legacyResult.error}")
                                        entity.toDomain() // 返回无exercises的基础模板
                                    }
                                    is ModernResult.Loading -> entity.toDomain()
                                }
                            }
                        } else {
                            Timber.d("🔍 [QUERY-FIX] 无JSON数据，使用关系型数据: ${entity.name}")
                            // 使用关系型数据
                            val exercises = exerciseInTemplateDao.getExercisesInTemplateSync(entity.id)
                            when (val result = entity.toDomainWithExercisesSafely(exercises)) {
                                is ModernResult.Success -> result.data
                                is ModernResult.Error -> {
                                    Timber.e("🔍 [QUERY-FIX] 关系型数据加载失败，返回空模板: ${result.error}")
                                    entity.toDomain()
                                }
                                is ModernResult.Loading -> entity.toDomain()
                            }
                        }
                    }

                    Timber.d("🔍 [QUERY-FIX] 用户模板查询完成: userId=$userId, 总数=${templatesWithExercises.size}")
                    templatesWithExercises.forEach { template ->
                        Timber.d("🔍 [QUERY-FIX] 模板: ${template.name}, exercises=${template.exercises.size}")
                    }

                    ModernResult.Success(templatesWithExercises)
                } catch (e: Exception) {
                    Timber.e(e, "🔍 [QUERY-FIX] 加载用户模板异常: userId=$userId")
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "getTemplatesByUser",
                            uiMessage = UiText.DynamicString("获取用户模板失败"),
                        ),
                    )
                }
            }
            .catch { e ->
                Timber.e(e, "获取用户模板失败: userId=$userId")
                emit(
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "getTemplatesByUser",
                            uiMessage = UiText.DynamicString("获取用户模板失败"),
                        ),
                    ),
                )
            }
            .flowOn(ioDispatcher)

    override suspend fun saveTemplate(template: WorkoutTemplate): ModernResult<String> =
        withContext(ioDispatcher) {
            safeCatch {
                // 🔥 Phase 0: 关键保存日志 - 记录保存前的数据状态
                Timber.tag(
                    "WK-TEMPLATE",
                ).d(
                    "🔥 [PHASE0-SAVE-START] 开始保存模板: ${template.name}, id=${template.id}, isDraft=${template.isDraft}",
                )
                Timber.tag(
                    "WK-TEMPLATE",
                ).d("🔥 [PHASE0-SAVE-START] 用户ID=${template.userId}, 动作数量=${template.exercises.size}")

                // 记录每个动作的customSets状态
                template.exercises.forEachIndexed { index, exercise ->
                    val customSetsInfo = if (exercise.notes?.contains("__CUSTOM_SETS_JSON__:") == true) {
                        try {
                            // 🔥 修复：使用统一的标记格式常量
                            val standardMarker = "__CUSTOM_SETS_JSON__:"
                            val markerStart = exercise.notes!!.indexOf(standardMarker)
                            if (markerStart != -1) {
                                val jsonStart = markerStart + standardMarker.length
                                val jsonData = exercise.notes!!.substring(jsonStart)
                                val parseJson = kotlinx.serialization.json.Json {
                                    ignoreUnknownKeys = true
                                    isLenient = true
                                }
                                val sets = parseJson.decodeFromString<List<com.example.gymbro.shared.models.workout.TemplateSetDto>>(
                                    jsonData,
                                )
                                "customSets=${sets.size}组"
                            } else {
                                "marker存在但无数据"
                            }
                        } catch (e: Exception) {
                            "解析失败: ${e.message}"
                        }
                    } else {
                        "无customSets数据"
                    }
                    Timber.tag(
                        "WK-TEMPLATE",
                    ).d("🔥 [PHASE0-SAVE-START] 动作${index + 1}[${exercise.name}]: $customSetsInfo")
                }

                Timber.d(
                    "🗄️ [DEBUG] Repository: 保存模板 id=${template.id}, name=${template.name}, isDraft=${template.isDraft}, 动作数量=${template.exercises.size}",
                )
                println(
                    "🗄️ [DEBUG] Repository: 保存模板 id=${template.id}, name=${template.name}, isDraft=${template.isDraft}",
                )

                // 🔥 修复双份保存问题：检查模板是否已存在
                val existingTemplate = templateDao.getTemplateById(template.id)
                val isUpdate = existingTemplate != null

                // 🔥 修复：只对新模板进行重名检测，更新时保持原名称
                val uniqueName =
                    if (!isUpdate) {
                        // 新模板需要检查重名
                        generateUniqueTemplateName(template.name, template.isDraft, template.id)
                    } else {
                        // 🔥 修复：更新现有模板时保持原名称，不进行重名检测
                        // 这样可以避免编辑模板时创建重复的模板（如 Template1_2）
                        template.name
                    }

                val templateWithUniqueName = template.copy(name = uniqueName)

                Timber.d(
                    "🗄️ [DEBUG] Repository: 模板${if (isUpdate) "已存在，执行更新" else "不存在，执行插入"}: 原名称=${template.name}, 唯一名称=$uniqueName",
                )
                println(
                    "🗄️ [DEBUG] Repository: 模板${if (isUpdate) "已存在，执行更新" else "不存在，执行插入"}: 原名称=${template.name}, 唯一名称=$uniqueName",
                )

                // 🔥 JSON 数据持久化修复：改进序列化错误处理
                val jsonData = try {
                    val serializedData = TemplateJsonDataProcessor.safeToJson(templateWithUniqueName)
                    if (serializedData != null) {
                        Timber.d("JSON序列化成功: ${serializedData.length} 字符")
                        // 验证序列化结果的完整性
                        if (serializedData.isNotBlank() && serializedData.contains("\"exercises\"")) {
                            serializedData
                        } else {
                            Timber.w("JSON序列化结果不完整，回退到关系型存储")
                            null
                        }
                    } else {
                        Timber.e("JSON序列化失败，回退到关系型存储")
                        // 记录序列化失败的详细信息，便于调试
                        Timber.e(
                            "失败的模板信息: id=${templateWithUniqueName.id}, name=${templateWithUniqueName.name}",
                        )
                        null
                    }
                } catch (e: Exception) {
                    Timber.e(e, "JSON序列化异常，回退到关系型存储")
                    null
                }

                // 创建包含JSON数据的实体
                val entityWithJson =
                    templateWithUniqueName.toEntity().copy(
                        jsonData = jsonData,
                        versionTag = if (isUpdate) (existingTemplate?.versionTag ?: 1) + 1 else 1,
                        updatedAt = System.currentTimeMillis(),
                    )

                // Phase 2: 使用事务确保原子性操作
                templateDatabase.withTransaction {
                    // 保存模板实体
                    if (isUpdate) {
                        Timber.d("🗄️ [Phase2] Repository: 更新现有模板: ${templateWithUniqueName.name}")
                        templateDao.updateTemplate(entityWithJson)
                    } else {
                        Timber.d("🗄️ [Phase2] Repository: 插入新模板: ${templateWithUniqueName.name}")
                        templateDao.insertTemplate(entityWithJson)
                    }

                    // 保存模板中的动作（关系型数据，作为备份）
                    if (templateWithUniqueName.exercises.isNotEmpty()) {
                        // 先删除旧的动作配置（如果存在）
                        exerciseInTemplateDao.deleteAllExercisesInTemplate(templateWithUniqueName.id)

                        // 为每个动作正确设置order
                        val exercisesWithCorrectIds =
                            templateWithUniqueName.exercises.mapIndexed { index, exercise ->
                                exercise.copy(
                                    order = index + 1, // 设置正确的order（从1开始）
                                )
                            }

                        val exerciseEntities = when (val result = exercisesWithCorrectIds.toExerciseInTemplateEntitySafely()) {
                            is ModernResult.Success -> {
                                Timber.d("动作实体转换成功: ${result.data.size} 个动作")
                                // 修复：设置正确的templateId
                                result.data.map { entity ->
                                    entity.copy(templateId = templateWithUniqueName.id)
                                }
                            }
                            is ModernResult.Error -> {
                                Timber.e("动作实体转换失败: ${result.error.message}")
                                throw Exception("动作转换失败: ${result.error.message}")
                            }
                            is ModernResult.Loading -> throw Exception("Unexpected loading state")
                        }

                        Timber.d("插入动作到数据库: ${exerciseEntities.size} 个动作")
                        exerciseInTemplateDao.insertExercisesInTemplate(exerciseEntities)
                    }
                }

                // 🔥 JSON 数据持久化修复：验证数据保存完整性
                val savedTemplate = templateDao.getTemplateById(templateWithUniqueName.id)
                if (savedTemplate == null) {
                    Timber.e("模板保存验证失败: 无法读取已保存的模板 ${templateWithUniqueName.id}")
                    throw Exception("模板保存失败：数据验证不通过")
                }

                // 验证 JSON 数据是否正确保存
                if (jsonData != null && savedTemplate.jsonData != jsonData) {
                    Timber.w("JSON 数据保存不一致，但模板基本信息已保存")
                    Timber.w("期望长度: ${jsonData.length}, 实际长度: ${savedTemplate.jsonData?.length ?: 0}")
                }

                // 🔥 Phase 0: 关键保存完成日志 - 验证保存后的数据状态
                Timber.tag(
                    "WK-TEMPLATE",
                ).d(
                    "🔥 [PHASE0-SAVE-COMPLETE] 保存完成: ${templateWithUniqueName.name}, id=${templateWithUniqueName.id}",
                )
                Timber.tag(
                    "WK-TEMPLATE",
                ).d(
                    "🔥 [PHASE0-SAVE-COMPLETE] 已保存用户ID=${savedTemplate.userId}, JSON数据长度: ${savedTemplate.jsonData?.length ?: 0}",
                )

                // 🔥 Phase 0: 验证保存后的JSON数据完整性
                if (savedTemplate.jsonData != null) {
                    Timber.tag(
                        "WK-TEMPLATE",
                    ).d("🔥 [PHASE0-SAVE-COMPLETE] JSON数据已保存，长度=${savedTemplate.jsonData!!.length}")
                    // 简化验证：只检查JSON是否包含关键字段，避免复杂的反序列化
                    val jsonContent = savedTemplate.jsonData!!
                    val hasCustomSets = jsonContent.contains("customSets")
                    val hasExercises = jsonContent.contains("exercises")
                    Timber.tag(
                        "WK-TEMPLATE",
                    ).d(
                        "🔥 [PHASE0-SAVE-COMPLETE] JSON验证: hasExercises=$hasExercises, hasCustomSets=$hasCustomSets",
                    )
                } else {
                    Timber.tag("WK-TEMPLATE").d("🚨 [PHASE0-SAVE-COMPLETE] JSON数据为空，保存可能失败")
                }

                Timber.d("✅ 模板保存成功并验证通过: 原名称=${template.name}, 最终名称=${templateWithUniqueName.name}")
                println("✅ 模板保存成功并验证通过: 原名称=${template.name}, 最终名称=${templateWithUniqueName.name}")
                templateWithUniqueName.id
            }
        }

    /**
     * 🔥 新增：检查模板名称是否已存在
     * @param name 要检查的名称
     * @param isDraft 是否为草稿（草稿和正式模板分别检查）
     * @param excludeId 排除的模板ID（用于更新时排除自己）
     */
    private suspend fun checkTemplateNameExists(
        name: String,
        isDraft: Boolean,
        excludeId: String?,
    ): Boolean =
        try {
            // 🔥 修复：使用现有的查询方法，通过时间戳获取所有模板
            val templates = templateDao.getTemplatesUpdatedAfter(0) // 获取所有模板
            templates.any { template ->
                template.name == name &&
                    template.isDraft == isDraft &&
                    template.id != excludeId
            }
        } catch (e: Exception) {
            Timber.w(e, "检查模板名称是否存在时出错")
            false // 出错时假设不存在，允许保存
        }

    /**
     * 🔥 新增：生成唯一的模板名称
     * @param baseName 基础名称
     * @param isDraft 是否为草稿
     * @param excludeId 排除的模板ID
     */
    private suspend fun generateUniqueTemplateName(
        baseName: String,
        isDraft: Boolean,
        excludeId: String?,
    ): String {
        // 如果基础名称不存在，直接返回
        if (!checkTemplateNameExists(baseName, isDraft, excludeId)) {
            return baseName
        }

        // 从2开始递增查找唯一名称
        var counter = 2
        var uniqueName: String

        do {
            uniqueName = "$baseName $counter"
            counter++
        } while (checkTemplateNameExists(uniqueName, isDraft, excludeId) && counter <= 100) // 防止无限循环

        Timber.d("🏷️ [DEBUG] 生成唯一名称: $baseName -> $uniqueName")
        println("🏷️ [DEBUG] 生成唯一名称: $baseName -> $uniqueName")

        return uniqueName
    }

    override suspend fun updateTemplate(template: WorkoutTemplate): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("更新模板: templateId=${template.id}")

                // 生成JSON格式数据
                val jsonData = TemplateJsonDataProcessor.safeToJson(template)

                // 创建包含JSON数据的实体
                val entityWithJson = template.toEntity().copy(
                    jsonData = jsonData,
                    versionTag = 1, // 更新时应该从现有版本+1，这里简化处理
                )

                // 更新模板基本信息（包含JSON）
                templateDao.updateTemplate(entityWithJson)

                // 删除旧的动作配置
                exerciseInTemplateDao.deleteAllExercisesInTemplate(template.id)

                // 插入新的动作配置（关系型数据，作为备份）
                if (template.exercises.isNotEmpty()) {
                    // 为每个动作正确设置order和templateId
                    val exercisesWithCorrectIds = template.exercises.mapIndexed { index, exercise ->
                        exercise.copy(
                            order = index + 1, // 设置正确的order（从1开始）
                        )
                    }

                    val exerciseEntities = when (val result = exercisesWithCorrectIds.toExerciseInTemplateEntitySafely()) {
                        is ModernResult.Success -> {
                            // 修复：设置正确的templateId
                            result.data.map { entity ->
                                entity.copy(templateId = template.id)
                            }
                        }
                        is ModernResult.Error -> throw Exception(result.error.message)
                        is ModernResult.Loading -> throw Exception("Unexpected loading state")
                    }
                    exerciseInTemplateDao.insertExercisesInTemplate(exerciseEntities)
                }
            }
        }

    override suspend fun deleteTemplate(templateId: String): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("删除模板: templateId=$templateId")
                templateDao.deleteTemplate(templateId)
                // 由于设置了 CASCADE，相关的 ExerciseInTemplate 会自动删除
            }
        }

    // ==================== Template 查询操作 ====================

    override fun getPublicTemplates(): Flow<ModernResult<List<WorkoutTemplate>>> =
        templateDao.getPublicTemplates()
            .map { entities ->
                entities.toDomainSafely()
            }
            .catch { e ->
                Timber.e(e, "获取公开模板失败")
                emit(
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "getPublicTemplates",
                            uiMessage = UiText.DynamicString("获取公开模板失败"),
                        ),
                    ),
                )
            }.flowOn(ioDispatcher)

    override fun getPublishedTemplates(): Flow<ModernResult<List<WorkoutTemplate>>> =
        templateDao
            .getTemplatesByDraftStatus(isDraft = false) // 只获取非草稿的模板
            .map { entities ->
                entities.toDomainSafely()
            }.catch { e ->
                Timber.e(e, "获取已发布模板失败")
                emit(
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "getPublishedTemplates",
                            uiMessage = UiText.DynamicString("获取已发布模板失败"),
                        ),
                    ),
                )
            }
            .flowOn(ioDispatcher)

    override fun getDraftTemplates(): Flow<ModernResult<List<WorkoutTemplate>>> =
        templateDao
            .getTemplatesByDraftStatus(isDraft = true) // 只获取草稿模板
            .map { entities ->
                entities.toDomainSafely()
            }.catch { e ->
                Timber.e(e, "获取草稿模板失败")
                emit(
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "getDraftTemplates",
                            uiMessage = UiText.DynamicString("获取草稿模板失败"),
                        ),
                    ),
                )
            }
            .flowOn(ioDispatcher)

    override fun getFavoriteTemplates(userId: String): Flow<ModernResult<List<WorkoutTemplate>>> =
        templateDao.getFavoriteTemplates(userId)
            .map { entities ->
                entities.toDomainSafely()
            }
            .catch { e ->
                Timber.e(e, "获取收藏模板失败: userId=$userId")
                emit(
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "getFavoriteTemplates",
                            uiMessage = UiText.DynamicString("获取收藏模板失败"),
                        ),
                    ),
                )
            }
            .flowOn(ioDispatcher)

    override fun searchTemplates(query: String): Flow<ModernResult<List<WorkoutTemplate>>> =
        templateDao.searchTemplatesByName(query)
            .map { entities ->
                entities.toDomainSafely()
            }
            .catch { e ->
                Timber.e(e, "搜索模板失败: query=$query")
                emit(
                    ModernResult.Error(
                        e.toModernDataError(
                            operationName = "searchTemplates",
                            uiMessage = UiText.DynamicString("搜索模板失败"),
                        ),
                    ),
                )
            }
            .flowOn(ioDispatcher)

    // ==================== ExerciseInTemplate 操作 ====================

    override suspend fun addExerciseToTemplate(
        templateId: String,
        exercise: TemplateExercise,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("添加动作到模板: templateId=$templateId, exerciseId=${exercise.exerciseId}")
                exerciseInTemplateDao.insertExerciseInTemplate(exercise.toEntity())
            }
        }

    override suspend fun removeExerciseFromTemplate(
        templateId: String,
        exerciseInTemplateId: String,
    ): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("从模板移除动作: templateId=$templateId, exerciseInTemplateId=$exerciseInTemplateId")
                exerciseInTemplateDao.deleteExerciseInTemplate(exerciseInTemplateId)
            }
        }

    override suspend fun updateExerciseInTemplate(exercise: TemplateExercise): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("更新模板中的动作: exerciseId=${exercise.id}")
                exerciseInTemplateDao.updateExerciseInTemplate(exercise.toEntity())
            }
        }

    // ==================== 状态管理操作 ====================

    override suspend fun updateTemplateFavoriteStatus(templateId: String, isFavorite: Boolean): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("更新模板收藏状态: templateId=$templateId, isFavorite=$isFavorite")
                templateDao.updateTemplateFavoriteStatus(templateId, isFavorite)
            }
        }

    override suspend fun updateTemplatePublicStatus(templateId: String, isPublic: Boolean): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("更新模板公开状态: templateId=$templateId, isPublic=$isPublic")
                templateDao.updateTemplatePublicStatus(templateId, isPublic)
            }
        }

    // ==================== 统计操作 ====================

    override suspend fun getUserTemplateCount(userId: String): ModernResult<Int> =
        withContext(ioDispatcher) {
            safeCatch {
                templateDao.getUserTemplateCount(userId)
            }
        }

    override suspend fun getPublicTemplateCount(): ModernResult<Int> =
        withContext(ioDispatcher) {
            safeCatch {
                templateDao.getPublicTemplateCount()
            }
        }

    // ==================== JSON格式支持方法 ====================

    /**
     * 保存模板（支持版本控制）
     *
     * @param template 要保存的模板
     * @param expectedVersionTag 期望的版本号（乐观锁）
     * @return 新的版本号
     */
    suspend fun saveTemplateWithVersionCheck(
        template: WorkoutTemplate,
        expectedVersionTag: Int? = null,
    ): ModernResult<Int> = withContext(ioDispatcher) {
        safeCatch {
            Timber.d("保存模板（版本控制）: templateId=${template.id}, expectedVersion=$expectedVersionTag")

            // 检查版本冲突
            if (expectedVersionTag != null) {
                val existingEntity = templateDao.getTemplateById(template.id)
                if (existingEntity != null && existingEntity.versionTag != expectedVersionTag) {
                    throw VersionConflictException(
                        "Version conflict: expected $expectedVersionTag, actual ${existingEntity.versionTag}",
                    )
                }
            }

            // 计算新版本号
            val newVersionTag = (expectedVersionTag ?: 0) + 1

            // 生成JSON格式数据
            val jsonData = TemplateJsonDataProcessor.safeToJson(template)

            // 创建包含JSON数据和新版本号的实体
            val entityWithJson = template.toEntity().copy(
                jsonData = jsonData,
                versionTag = newVersionTag,
                updatedAt = System.currentTimeMillis(),
            )

            // 保存模板
            templateDao.insertTemplate(entityWithJson)

            // 保存关系型数据作为备份
            if (template.exercises.isNotEmpty()) {
                // 先删除旧的动作配置
                exerciseInTemplateDao.deleteAllExercisesInTemplate(template.id)

                val exerciseEntities = when (val result = template.exercises.toExerciseInTemplateEntitySafely()) {
                    is ModernResult.Success -> result.data
                    is ModernResult.Error -> throw Exception(result.error.message)
                    is ModernResult.Loading -> throw Exception("Unexpected loading state")
                }
                exerciseInTemplateDao.insertExercisesInTemplate(exerciseEntities)
            }

            newVersionTag
        }
    }

    // ==================== 版本管理操作 (Phase1新增) ====================

    override suspend fun createVersion(templateId: String, description: String?): ModernResult<TemplateVersion> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("创建模板版本: templateId=$templateId, description=$description")

                // 获取当前模板
                val templateEntity = templateDao.getTemplateById(templateId)
                    ?: throw IllegalArgumentException("Template not found: $templateId")

                // 获取完整的模板内容
                val template = if (templateEntity.jsonData != null) {
                    val templateFromJson = TemplateJsonDataProcessor.fromJson(templateEntity.jsonData)
                    if (templateFromJson != null) {
                        templateFromJson
                    } else {
                        // 回退到关系型数据
                        val exercises = exerciseInTemplateDao.getExercisesInTemplateSync(templateId)
                        when (val legacyResult = templateEntity.toDomainWithExercisesSafely(exercises)) {
                            is ModernResult.Success -> legacyResult.data
                            is ModernResult.Error -> throw Exception(legacyResult.error.message)
                            is ModernResult.Loading -> throw Exception("Unexpected loading state")
                        }
                    }
                } else {
                    val exercises = exerciseInTemplateDao.getExercisesInTemplateSync(templateId)
                    when (val result = templateEntity.toDomainWithExercisesSafely(exercises)) {
                        is ModernResult.Success -> result.data
                        is ModernResult.Error -> throw Exception(result.error.message)
                        is ModernResult.Loading -> throw Exception("Unexpected loading state")
                    }
                }

                // 序列化模板内容 - 使用TemplateJsonDataProcessor处理复杂序列化
                val contentJson = TemplateJsonDataProcessor.safeToJson(template)
                    ?: throw Exception("模板序列化失败")

                // 创建版本实体
                val versionEntity = versionDao.createVersionSafely(
                    templateId = templateId,
                    contentJson = contentJson,
                    description = description,
                    isAutoSaved = false,
                )

                // 转换为领域模型
                TemplateVersion(
                    id = versionEntity.id,
                    templateId = versionEntity.templateId,
                    versionNumber = versionEntity.versionNumber,
                    content = template,
                    createdAt = java.time.Instant.ofEpochMilli(versionEntity.createdAt),
                    description = versionEntity.description,
                    isAutoSaved = versionEntity.isAutoSaved,
                )
            }.mapError { error ->
                Timber.e("创建版本失败: $error")
                error.toModernDataError(
                    operationName = "createVersion",
                    uiMessage = UiText.DynamicString("创建版本失败"),
                )
            }
        }

    override fun getVersionHistory(templateId: String): Flow<List<TemplateVersion>> =
        versionDao.getVersionsByTemplateId(templateId)
            .map { entities ->
                entities.mapNotNull { entity ->
                    try {
                        // 反序列化内容 - 使用TemplateJsonDataProcessor处理复杂反序列化
                        val content = TemplateJsonDataProcessor.fromJson(entity.contentJson)
                            ?: throw Exception("版本内容反序列化失败")
                        TemplateVersion(
                            id = entity.id,
                            templateId = entity.templateId,
                            versionNumber = entity.versionNumber,
                            content = content,
                            createdAt = java.time.Instant.ofEpochMilli(entity.createdAt),
                            description = entity.description,
                            isAutoSaved = entity.isAutoSaved,
                        )
                    } catch (e: Exception) {
                        Timber.e(e, "版本内容反序列化失败: versionId=${entity.id}")
                        null
                    }
                }
            }
            .catch { e ->
                Timber.e(e, "获取版本历史失败: templateId=$templateId")
                emit(emptyList())
            }
            .flowOn(ioDispatcher)

    override suspend fun getVersion(versionId: String): ModernResult<TemplateVersion> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("获取版本详情: versionId=$versionId")

                val entity = versionDao.getVersionById(versionId)
                    ?: throw IllegalArgumentException("Version not found: $versionId")

                // 反序列化内容 - 使用TemplateJsonDataProcessor处理复杂反序列化
                val content = TemplateJsonDataProcessor.fromJson(entity.contentJson)
                    ?: throw Exception("版本内容反序列化失败")

                TemplateVersion(
                    id = entity.id,
                    templateId = entity.templateId,
                    versionNumber = entity.versionNumber,
                    content = content,
                    createdAt = java.time.Instant.ofEpochMilli(entity.createdAt),
                    description = entity.description,
                    isAutoSaved = entity.isAutoSaved,
                )
            }.mapError { error ->
                Timber.e("获取版本失败: $error")
                error.toModernDataError(
                    operationName = "getVersion",
                    uiMessage = UiText.DynamicString("获取版本失败"),
                )
            }
        }

    override suspend fun restoreFromVersion(templateId: String, versionId: String): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("从版本恢复模板: templateId=$templateId, versionId=$versionId")

                // 获取版本内容
                val versionEntity = versionDao.getVersionById(versionId)
                    ?: throw IllegalArgumentException("Version not found: $versionId")

                // 验证版本属于指定模板
                if (versionEntity.templateId != templateId) {
                    throw IllegalArgumentException("Version does not belong to template")
                }

                // 反序列化版本内容 - 使用TemplateJsonDataProcessor处理复杂反序列化
                val versionContent = TemplateJsonDataProcessor.fromJson(versionEntity.contentJson)
                    ?: throw Exception("版本内容反序列化失败")

                // 更新当前模板 - 处理嵌套的ModernResult
                when (val updateResult = updateTemplate(versionContent)) {
                    is ModernResult.Success -> Unit // 成功，继续
                    is ModernResult.Error -> throw Exception(updateResult.error.message)
                    is ModernResult.Loading -> throw Exception("Unexpected loading state")
                }
            }.mapError { error ->
                Timber.e("版本恢复失败: $error")
                error.toModernDataError(
                    operationName = "restoreFromVersion",
                    uiMessage = UiText.DynamicString("版本恢复失败"),
                )
            }
        }

    override fun getPublishedVersions(templateId: String): Flow<List<TemplateVersion>> =
        versionDao.getPublishedVersions(templateId)
            .map { entities ->
                entities.mapNotNull { entity ->
                    try {
                        val content = TemplateJsonDataProcessor.fromJson(entity.contentJson)
                            ?: throw Exception("发布版本内容反序列化失败")
                        TemplateVersion(
                            id = entity.id,
                            templateId = entity.templateId,
                            versionNumber = entity.versionNumber,
                            content = content,
                            createdAt = java.time.Instant.ofEpochMilli(entity.createdAt),
                            description = entity.description,
                            isAutoSaved = entity.isAutoSaved,
                        )
                    } catch (e: Exception) {
                        Timber.e(e, "发布版本反序列化失败: versionId=${entity.id}")
                        null
                    }
                }
            }
            .catch { e ->
                Timber.e(e, "获取发布版本失败: templateId=$templateId")
                emit(emptyList())
            }
            .flowOn(ioDispatcher)

    override suspend fun cleanupAutoSavedVersions(templateId: String, keepCount: Int): ModernResult<Unit> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("清理自动保存版本: templateId=$templateId, keepCount=$keepCount")
                versionDao.keepRecentAutoSaves(templateId, keepCount)
            }.mapError { error ->
                Timber.e("清理自动保存版本失败: $error")
                error.toModernDataError(
                    operationName = "cleanupAutoSavedVersions",
                    uiMessage = UiText.DynamicString("清理自动保存版本失败"),
                )
            }
        }

    override suspend fun getLatestVersion(templateId: String): ModernResult<TemplateVersion?> =
        withContext(ioDispatcher) {
            safeCatch {
                Timber.d("获取模板最新版本: templateId=$templateId")

                val latestVersionEntity = versionDao.getLatestVersion(templateId)

                if (latestVersionEntity != null) {
                    // 反序列化版本内容 - 使用TemplateJsonDataProcessor处理复杂反序列化
                    val content = TemplateJsonDataProcessor.fromJson(latestVersionEntity.contentJson)
                        ?: throw Exception("最新版本内容反序列化失败")

                    TemplateVersion(
                        id = latestVersionEntity.id,
                        templateId = latestVersionEntity.templateId,
                        versionNumber = latestVersionEntity.versionNumber,
                        content = content,
                        createdAt = java.time.Instant.ofEpochMilli(latestVersionEntity.createdAt),
                        description = latestVersionEntity.description,
                        isAutoSaved = latestVersionEntity.isAutoSaved,
                    )
                } else {
                    // 如果没有版本，说明模板还没有发布过版本
                    null
                }
            }.mapError { error ->
                Timber.e("获取最新版本失败: $error")
                error.toModernDataError(
                    operationName = "getLatestVersion",
                    uiMessage = UiText.DynamicString("获取最新版本失败"),
                )
            }
        }
}

/**
 * 版本冲突异常
 */
class VersionConflictException(message: String) : Exception(message)

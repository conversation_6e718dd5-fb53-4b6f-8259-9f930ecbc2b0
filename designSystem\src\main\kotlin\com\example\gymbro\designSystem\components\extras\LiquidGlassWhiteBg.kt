package com.example.gymbro.designSystem.components.extras

import android.graphics.RenderEffect
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsHoveredAsState
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.blur
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Fill
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.ColorTokens
import com.example.gymbro.designSystem.theme.tokens.Tokens

/*
 * ==========================================================================
 * 🎯 纯白背景专用液态玻璃系统
 * ==========================================================================
 */

/**
 * 🔥 白色背景增强配置
 *
 * 核心策略：
 * 1. 反向对比 - 使用深色元素而非透明
 * 2. 阴影造型 - 用阴影定义形状边界
 * 3. 微妙纹理 - 添加可见的表面细节
 * 4. 色彩注入 - 注入微妙的色彩对比
 */
@Immutable
data class WhiteBackgroundEnhancement(
    // 🎨 对比度增强
    val shadowIntensity: Float = 0.15f, // 阴影强度
    val contrastBoost: Float = 2.0f, // 对比度提升倍数
    val edgeDefinition: Float = 0.8f, // 边缘清晰度

    // 🌈 色彩策略
    val tintColor: Color = Color(0xFF6366F1), // 主色调（紫色系）
    val tintIntensity: Float = 0.08f, // 色调强度
    val accentColor: Color = Color(0xFF8B5CF6), // 强调色
    val accentIntensity: Float = 0.12f, // 强调色强度

    // ✨ 表面纹理
    val surfaceTexture: Boolean = true, // 启用表面纹理
    val textureIntensity: Float = 0.06f, // 纹理强度
    val grainSize: Float = 2f, // 纹理颗粒大小

    // 🔆 光影效果
    val innerGlow: Boolean = true, // 内发光
    val innerGlowIntensity: Float = 0.4f, // 内发光强度
    val reflectionSpots: Boolean = true, // 反射光点
    val reflectionIntensity: Float = 0.6f, // 反射强度

    // 📐 几何增强
    val bevelEffect: Boolean = true, // 斜面效果
    val bevelDepth: Float = 2f, // 斜面深度
    val dropShadow: Boolean = true, // 投影
    val dropShadowBlur: Float = 8f, // 投影模糊
    val dropShadowOffset: Offset = Offset(0f, 2f), // 投影偏移
)

/**
 * 🎯 白色背景专用外观配置
 */
@Immutable
data class WhiteBgLiquidGlassAppearance(
    // 继承基础配置
    val base: LiquidGlassAppearance,
    // 白色背景增强
    val whiteEnhancement: WhiteBackgroundEnhancement = WhiteBackgroundEnhancement(),
    // 动态适应
    val adaptiveIntensity: Boolean = true, // 根据背景自动调整强度
    val backgroundSampling: Boolean = true, // 背景采样分析
) {
    companion object {
        /**
         * 🎯 预设配置 - 针对不同白色背景场景
         */

        // 纯白卡片背景
        val PureWhiteCard = WhiteBgLiquidGlassAppearance(
            base = LiquidGlassAppearance(
                blurRadius = 6f, // 减少模糊，增强边缘
                distortionStrength = 0.008f,
                rimLightIntensity = 0.1f, // 降低传统边缘光
                chromaticAberration = 0.015f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.BALANCED,
            ),
            whiteEnhancement = WhiteBackgroundEnhancement(
                shadowIntensity = 0.2f,
                contrastBoost = 2.5f,
                tintIntensity = 0.1f,
                accentIntensity = 0.15f,
                innerGlowIntensity = 0.5f,
            ),
        )

        // 白色输入框背景
        val WhiteInput = WhiteBgLiquidGlassAppearance(
            base = LiquidGlassAppearance(
                blurRadius = 4f,
                distortionStrength = 0.005f,
                rimLightIntensity = 0.05f,
                chromaticAberration = 0.01f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.BALANCED,
            ),
            whiteEnhancement = WhiteBackgroundEnhancement(
                shadowIntensity = 0.12f,
                contrastBoost = 1.8f,
                tintIntensity = 0.06f,
                surfaceTexture = true,
                textureIntensity = 0.04f,
                bevelDepth = 1f,
            ),
        )

        // 白色按钮背景
        val WhiteButton = WhiteBgLiquidGlassAppearance(
            base = LiquidGlassAppearance(
                blurRadius = 8f,
                distortionStrength = 0.012f,
                rimLightIntensity = 0.08f,
                chromaticAberration = 0.018f,
                performanceLevel = LiquidGlassAppearance.PerformanceLevel.PREMIUM,
            ),
            whiteEnhancement = WhiteBackgroundEnhancement(
                shadowIntensity = 0.25f,
                contrastBoost = 3.0f,
                tintIntensity = 0.12f,
                accentIntensity = 0.18f,
                innerGlowIntensity = 0.6f,
                reflectionIntensity = 0.8f,
                bevelDepth = 3f,
            ),
        )
    }
}

/*
 * ==========================================================================
 * 🎨 白色背景专用修饰符实现
 * ==========================================================================
 */

/**
 * 🔥 白色背景专用液态玻璃修饰符
 */
fun Modifier.liquidGlassForWhiteBg(
    appearance: WhiteBgLiquidGlassAppearance,
    debugMode: Boolean = false,
): Modifier = composed {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
        liquidGlassWhiteBgModern(appearance, debugMode)
    } else {
        liquidGlassWhiteBgLegacy(appearance)
    }
}

/**
 * 🚀 现代实现 - 使用着色器增强
 */
@RequiresApi(Build.VERSION_CODES.TIRAMISU)
@Composable
private fun Modifier.liquidGlassWhiteBgModern(
    appearance: WhiteBgLiquidGlassAppearance,
    debugMode: Boolean,
): Modifier = composed {
    val shaderCache = LocalShaderCache.current
    val pointerState = remember { OptimizedPointerState() }
    val enhancement = appearance.whiteEnhancement

    // 时间动画
    val animatedTime by rememberInfiniteTransition(label = "whiteTime").animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(6000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart,
        ),
        label = "time",
    )

    this
        // 🎯 关键：先应用投影，再应用其他效果
        .then(
            if (enhancement.dropShadow) {
                Modifier.drawBehind {
                    // 🔥 多层投影系统 - 创造深度感
                    val cornerRadius = appearance.base.cornerRadius.toPx()

                    // 主投影 - 深色，定义主要形状
                    drawRoundRect(
                        color = Color.Black.copy(alpha = enhancement.shadowIntensity * 0.8f),
                        topLeft = enhancement.dropShadowOffset + Offset(1f, 1f),
                        size = size,
                        cornerRadius = CornerRadius(cornerRadius),
                        style = Fill,
                    )

                    // 环境投影 - 更大更淡，模拟环境光遮挡
                    drawRoundRect(
                        color = Color.Black.copy(alpha = enhancement.shadowIntensity * 0.3f),
                        topLeft = enhancement.dropShadowOffset + Offset(2f, 3f),
                        size = Size(size.width + 4.dp.toPx(), size.height + 4.dp.toPx()),
                        cornerRadius = CornerRadius(cornerRadius + 2.dp.toPx()),
                        style = Fill,
                    )
                }
                    .blur(enhancement.dropShadowBlur.dp)
            } else {
                Modifier
            },
        )
        .clip(RoundedCornerShape(appearance.base.cornerRadius))
        .then(
            if (appearance.base.enableInteraction) {
                Modifier.pointerInput(Unit) {
                    detectDragGestures(
                        onDragStart = { offset -> pointerState.start(offset, size) },
                        onDragEnd = { pointerState.stop() },
                        onDragCancel = { pointerState.stop() },
                    ) { change, _ ->
                        pointerState.update(change.position)
                    }
                }
            } else {
                Modifier
            },
        )
        .graphicsLayer {
            // 🎯 白色背景专用着色器
            val whiteShader = shaderCache.getOrCreate(
                "liquid_white_bg",
                WHITE_BG_LIQUID_SHADER,
            ).apply {
                // 基础参数
                setFloatUniform("uResolution", size.width, size.height)
                setFloatUniform("uTime", animatedTime * 6f)
                setFloatUniform("uPointerPos", pointerState.normalizedX, pointerState.normalizedY)
                setFloatUniform("uPointerVel", pointerState.velocityX * 0.1f, pointerState.velocityY * 0.1f)

                // 基础效果
                setFloatUniform("uDistortion", appearance.base.optimizedDistortion)
                setFloatUniform("uChromatic", appearance.base.chromaticAberration)
                setFloatUniform("uBlur", appearance.base.optimizedBlurRadius)

                // 🔥 白色背景增强参数
                setFloatUniform("uContrastBoost", enhancement.contrastBoost)
                setFloatUniform(
                    "uTintColor",
                    enhancement.tintColor.red,
                    enhancement.tintColor.green,
                    enhancement.tintColor.blue,
                )
                setFloatUniform("uTintIntensity", enhancement.tintIntensity)
                setFloatUniform(
                    "uAccentColor",
                    enhancement.accentColor.red,
                    enhancement.accentColor.green,
                    enhancement.accentColor.blue,
                )
                setFloatUniform("uAccentIntensity", enhancement.accentIntensity)
                setFloatUniform("uTextureIntensity", enhancement.textureIntensity)
                setFloatUniform("uGrainSize", enhancement.grainSize)
                setFloatUniform("uInnerGlowIntensity", enhancement.innerGlowIntensity)
                setFloatUniform("uReflectionIntensity", enhancement.reflectionIntensity)

                // 布尔标志
                setIntUniform("uSurfaceTexture", if (enhancement.surfaceTexture) 1 else 0)
                setIntUniform("uInnerGlow", if (enhancement.innerGlow) 1 else 0)
                setIntUniform("uReflectionSpots", if (enhancement.reflectionSpots) 1 else 0)
                setIntUniform("uDebug", if (debugMode) 1 else 0)
            }

            val liquidEffect = RenderEffect.createRuntimeShaderEffect(whiteShader, "uTexture")
            renderEffect = liquidEffect.asComposeRenderEffect()
        }
        .drawBehind {
            val cornerRadius = appearance.base.cornerRadius.toPx()

            // 🎯 Compose端的增强绘制

            // 1. 斜面效果 - 模拟3D边缘
            if (enhancement.bevelEffect) {
                val bevelWidth = enhancement.bevelDepth.dp.toPx()

                // 高光边（左上）
                drawRoundRect(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color.White.copy(alpha = 0.4f),
                            Color.Transparent,
                        ),
                        start = Offset(0f, 0f),
                        end = Offset(bevelWidth, bevelWidth),
                    ),
                    size = size,
                    cornerRadius = CornerRadius(cornerRadius),
                    style = Stroke(width = bevelWidth),
                )

                // 阴影边（右下）
                drawRoundRect(
                    brush = Brush.linearGradient(
                        colors = listOf(
                            Color.Transparent,
                            Color.Black.copy(alpha = 0.2f),
                        ),
                        start = Offset(size.width - bevelWidth, size.height - bevelWidth),
                        end = Offset(size.width, size.height),
                    ),
                    size = size,
                    cornerRadius = CornerRadius(cornerRadius),
                    style = Stroke(width = bevelWidth),
                )
            }

            // 2. 增强边缘定义
            drawRoundRect(
                color = enhancement.tintColor.copy(alpha = enhancement.edgeDefinition * 0.3f),
                size = size,
                cornerRadius = CornerRadius(cornerRadius),
                style = Stroke(width = 1.dp.toPx()),
            )

            // 3. 微妙的内边框
            drawRoundRect(
                color = Color.White.copy(alpha = 0.6f),
                size = Size(size.width - 2.dp.toPx(), size.height - 2.dp.toPx()),
                topLeft = Offset(1.dp.toPx(), 1.dp.toPx()),
                cornerRadius = CornerRadius(cornerRadius - 1.dp.toPx()),
                style = Stroke(width = 0.5.dp.toPx()),
            )
        }
}

/**
 * 📱 兼容版本 - 纯Compose实现
 */
@Composable
private fun Modifier.liquidGlassWhiteBgLegacy(
    appearance: WhiteBgLiquidGlassAppearance,
): Modifier = composed {
    val enhancement = appearance.whiteEnhancement

    // 🎯 流动动画
    val flowAnimation by rememberInfiniteTransition(label = "flow").animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(4000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart,
        ),
        label = "flowOffset",
    )

    this
        // 投影层
        .drawBehind {
            if (enhancement.dropShadow) {
                val cornerRadius = appearance.base.cornerRadius.toPx()

                // 多层投影
                drawRoundRect(
                    color = Color.Black.copy(alpha = enhancement.shadowIntensity * 0.6f),
                    topLeft = enhancement.dropShadowOffset,
                    size = size,
                    cornerRadius = CornerRadius(cornerRadius),
                )
            }
        }
        .blur((enhancement.dropShadowBlur * 0.5f).dp)
        .clip(RoundedCornerShape(appearance.base.cornerRadius))
        // 🔥 核心：多层渐变背景系统
        .background(
            brush = Brush.radialGradient(
                colors = listOf(
                    // 中心：微妙的色彩注入
                    enhancement.tintColor.copy(alpha = enhancement.tintIntensity * 0.8f),
                    enhancement.accentColor.copy(alpha = enhancement.accentIntensity * 0.6f),
                    // 边缘：增强对比
                    Color.Gray.copy(alpha = 0.08f * enhancement.contrastBoost),
                    Color.Transparent,
                ),
                center = Offset.Infinite, // 动态中心
                radius = Float.POSITIVE_INFINITY,
            ),
        )
        // 表面纹理层
        .then(
            if (enhancement.surfaceTexture) {
                Modifier.drawBehind {
                    // 🎯 程序化纹理生成
                    val cornerRadius = appearance.base.cornerRadius.toPx()
                    val grainDensity = (size.width / enhancement.grainSize).toInt()

                    for (i in 0 until grainDensity) {
                        for (j in 0 until grainDensity) {
                            val x = (i * enhancement.grainSize) + (kotlin.random.Random.nextFloat() * enhancement.grainSize)
                            val y = (j * enhancement.grainSize) + (kotlin.random.Random.nextFloat() * enhancement.grainSize)

                            if (x < size.width && y < size.height) {
                                drawCircle(
                                    color = Color.White.copy(alpha = enhancement.textureIntensity * 0.3f),
                                    radius = 0.5f,
                                    center = Offset(x, y),
                                )
                            }
                        }
                    }
                }
            } else {
                Modifier
            },
        )
        // 内发光层
        .then(
            if (enhancement.innerGlow) {
                Modifier.drawBehind {
                    val cornerRadius = appearance.base.cornerRadius.toPx()

                    // 内发光效果
                    drawRoundRect(
                        brush = Brush.radialGradient(
                            colors = listOf(
                                enhancement.accentColor.copy(alpha = enhancement.innerGlowIntensity * 0.4f),
                                Color.Transparent,
                            ),
                            center = Offset(size.width * 0.5f, size.height * 0.3f),
                            radius = size.width * 0.6f,
                        ),
                        size = size,
                        cornerRadius = CornerRadius(cornerRadius),
                    )
                }
            } else {
                Modifier
            },
        )
        // 流动边框层
        .drawBehind {
            val cornerRadius = appearance.base.cornerRadius.toPx()

            // 🔥 流动彩色边框 - 关键的可见性增强
            val flowColors = listOf(
                enhancement.tintColor.copy(alpha = 0.6f),
                enhancement.accentColor.copy(alpha = 0.8f),
                enhancement.tintColor.copy(alpha = 0.4f),
                Color.Transparent,
            )

            drawRoundRect(
                brush = Brush.linearGradient(
                    colors = flowColors,
                    start = Offset(size.width * flowAnimation, 0f),
                    end = Offset(size.width * (flowAnimation + 0.3f), size.height),
                ),
                size = size,
                cornerRadius = CornerRadius(cornerRadius),
                style = Stroke(width = 2.dp.toPx()),
            )

            // 反射光点
            if (enhancement.reflectionSpots) {
                // 主反射点
                drawCircle(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            Color.White.copy(alpha = enhancement.reflectionIntensity * 0.8f),
                            Color.Transparent,
                        ),
                        radius = 15.dp.toPx(),
                    ),
                    radius = 12.dp.toPx(),
                    center = Offset(size.width * 0.25f, size.height * 0.2f),
                )

                // 次反射点
                drawCircle(
                    brush = Brush.radialGradient(
                        colors = listOf(
                            enhancement.accentColor.copy(alpha = enhancement.reflectionIntensity * 0.5f),
                            Color.Transparent,
                        ),
                        radius = 8.dp.toPx(),
                    ),
                    radius = 6.dp.toPx(),
                    center = Offset(size.width * 0.75f, size.height * 0.4f),
                )
            }

            // 🔥 最终边缘增强 - 确保在白色背景下可见
            drawRoundRect(
                color = Color.Black.copy(alpha = 0.1f * enhancement.contrastBoost),
                size = size,
                cornerRadius = CornerRadius(cornerRadius),
                style = Stroke(width = 0.5.dp.toPx()),
            )
        }
}

/*
 * ==========================================================================
 * 🎨 白色背景专用着色器
 * ==========================================================================
 */

private const val WHITE_BG_LIQUID_SHADER = """
uniform float2 uResolution;
uniform float uTime;
uniform float2 uPointerPos;
uniform float2 uPointerVel;
uniform float uDistortion;
uniform float uChromatic;
uniform float uBlur;
uniform float uContrastBoost;
uniform float3 uTintColor;
uniform float uTintIntensity;
uniform float3 uAccentColor;
uniform float uAccentIntensity;
uniform float uTextureIntensity;
uniform float uGrainSize;
uniform float uInnerGlowIntensity;
uniform float uReflectionIntensity;
uniform int uSurfaceTexture;
uniform int uInnerGlow;
uniform int uReflectionSpots;
uniform int uDebug;
uniform shader uTexture;

// 🎯 增强的噪声函数 - 用于纹理生成
float enhancedNoise(float2 p) {
    float value = 0.0;
    // 多层噪声叠加
    value += 0.5 * sin(p.x * 8.0 + uTime * 0.5) * cos(p.y * 8.0 + uTime * 0.7);
    value += 0.25 * sin(p.x * 16.0 + uTime * 0.8) * cos(p.y * 16.0 + uTime * 1.1);
    value += 0.125 * sin(p.x * 32.0 + uTime * 1.2) * cos(p.y * 32.0 + uTime * 0.9);
    return value;
}

// 🎨 对比度增强函数
float3 enhanceContrast(float3 color, float boost) {
    // 智能对比度增强，避免过度饱和
    float3 enhanced = (color - 0.5) * boost + 0.5;
    return clamp(enhanced, 0.0, 1.0);
}

// ✨ 表面纹理生成
float surfaceGrain(float2 uv) {
    float2 grainUV = uv * uResolution / uGrainSize;
    float grain = enhancedNoise(grainUV + uTime * 0.1);
    return grain * uTextureIntensity;
}

half4 main(float2 coord) {
    float2 uv = coord / uResolution;

    // 调试模式
    if (uDebug > 0) {
        float noise = enhancedNoise(uv * 10.0) * 0.5 + 0.5;
        return half4(noise, noise, noise, 1.0);
    }

    // 🎯 基础液态扭曲
    float2 distortion = float2(
        enhancedNoise(uv * 6.0 + uTime * 0.1),
        enhancedNoise(uv * 6.0 + float2(2.0, 3.0) + uTime * 0.1)
    ) * uDistortion;

    // 指针交互
    float2 pointerInfluence = (uPointerPos - uv) * length(uPointerVel) * 0.08;
    distortion += pointerInfluence;

    // 🌈 增强的色散效果
    float2 offset = distortion * uResolution;
    float chromatic = uChromatic * 20.0;

    half4 colorR = uTexture.eval(coord + offset + float2(chromatic, 0.0));
    half4 colorG = uTexture.eval(coord + offset);
    half4 colorB = uTexture.eval(coord + offset - float2(chromatic, 0.0));

    half4 baseColor = half4(colorR.r, colorG.g, colorB.b, colorG.a);

    // 🔥 白色背景专用增强

    // 1. 对比度增强
    baseColor.rgb = enhanceContrast(baseColor.rgb, uContrastBoost);

    // 2. 色彩注入 - 关键的可见性提升
    float centerDistance = distance(uv, float2(0.5));
    float tintMask = 1.0 - smoothstep(0.0, 0.7, centerDistance);

    // 主色调注入
    baseColor.rgb = mix(baseColor.rgb, uTintColor, uTintIntensity * tintMask);

    // 强调色注入（边缘区域）
    float edgeMask = smoothstep(0.3, 0.8, centerDistance);
    baseColor.rgb = mix(baseColor.rgb, uAccentColor, uAccentIntensity * edgeMask);

    // 3. 表面纹理
    if (uSurfaceTexture > 0) {
        float grain = surfaceGrain(uv);
        baseColor.rgb += grain;
    }

    // 4. 内发光
    if (uInnerGlow > 0) {
        float glowMask = 1.0 - smoothstep(0.0, 0.5, centerDistance);
        float3 glowColor = uAccentColor * uInnerGlowIntensity * glowMask;
        baseColor.rgb += glowColor;
    }

    // 5. 反射光点
    if (uReflectionSpots > 0) {
        // 主反射点
        float spot1 = 1.0 - smoothstep(0.0, 0.15, distance(uv, float2(0.25, 0.2)));
        baseColor.rgb += spot1 * uReflectionIntensity * 0.8;

        // 次反射点
        float spot2 = 1.0 - smoothstep(0.0, 0.1, distance(uv, float2(0.75, 0.4)));
        baseColor.rgb += spot2 * uReflectionIntensity * 0.5 * uAccentColor;
    }

    // 🎯 最终处理：确保在白色背景下可见
    // 添加微妙的暗边缘，增强形状定义
    float edgeDefinition = smoothstep(0.85, 1.0, centerDistance);
    baseColor.rgb = mix(baseColor.rgb, baseColor.rgb * 0.7, edgeDefinition * 0.3);

    return baseColor;
}
"""

/*
 * ==========================================================================
 * 🎯 便捷组件 - 白色背景专用
 * ==========================================================================
 */

/**
 * 🔥 白色背景聊天输入框
 */
@Composable
fun WhiteBgLiquidChatInput(
    modifier: Modifier = Modifier,
    content: @Composable BoxScope.() -> Unit = {},
) {
    Box(
        modifier = modifier.liquidGlassForWhiteBg(
            WhiteBgLiquidGlassAppearance.WhiteInput,
        ),
        content = content,
    )
}

/**
 * 🎯 白色背景按钮
 */
@Composable
fun WhiteBgLiquidButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    content: @Composable BoxScope.() -> Unit = {},
) {
    val isPressed by interactionSource.collectIsPressedAsState()
    val isHovered by interactionSource.collectIsHoveredAsState()

    // 🎯 动态增强 - 交互时提升可见性
    val dynamicAppearance = remember(isPressed, isHovered) {
        WhiteBgLiquidGlassAppearance.WhiteButton.copy(
            whiteEnhancement = WhiteBgLiquidGlassAppearance.WhiteButton.whiteEnhancement.copy(
                shadowIntensity = if (isPressed) 0.4f else if (isHovered) 0.3f else 0.25f,
                contrastBoost = if (isPressed) 3.5f else if (isHovered) 3.2f else 3.0f,
                tintIntensity = if (isPressed) 0.2f else if (isHovered) 0.16f else 0.12f,
            ),
        )
    }

    Box(
        modifier = modifier
            .liquidGlassForWhiteBg(dynamicAppearance)
            .pointerInput(onClick) {
                detectTapGestures(onTap = { onClick() })
            },
        content = content,
    )
}

/**
 * 🎯 白色背景输入框背景 - 超级性能优化版
 *
 * 专为解决高频重组问题设计的轻量级实现：
 * 1. 完全禁用复杂着色器和动画
 * 2. 使用简单的静态背景和边框
 * 3. 缓存所有计算结果
 * 4. 应用@Stable优化
 */
@Stable
@Composable
fun LiquidInputBackground(
    modifier: Modifier = Modifier,
    content: @Composable BoxScope.() -> Unit = {},
) {
    // 🚀 超级性能优化：使用最简化的静态实现
    val cornerRadius = remember { 8.dp }
    val strokeWidth = remember { 0.5.dp }

    // 缓存的颜色配置
    val cachedColors = remember {
        object {
            val backgroundGradient = Brush.radialGradient(
                colors = listOf(
                    Color.White.copy(alpha = 0.95f),
                    Color(0xFFF8F9FA).copy(alpha = 0.8f),
                    Color(0xFFE9ECEF).copy(alpha = 0.6f),
                ),
            )
            val borderColor = Color(0xFF6366F1).copy(alpha = 0.2f)
            val shadowColor = Color.Black.copy(alpha = 0.08f)
        }
    }

    Box(
        modifier = modifier
            // 简单阴影效果
            .drawBehind {
                val cornerRadiusPx = cornerRadius.toPx()
                val shadowOffset = Offset(0f, 2.dp.toPx())

                // 单层阴影，避免复杂绘制
                drawRoundRect(
                    color = cachedColors.shadowColor,
                    topLeft = shadowOffset,
                    size = size,
                    cornerRadius = CornerRadius(cornerRadiusPx),
                )
            }
            .clip(RoundedCornerShape(cornerRadius))
            // 静态背景渐变
            .background(cachedColors.backgroundGradient)
            // 简单边框
            .drawBehind {
                val cornerRadiusPx = cornerRadius.toPx()
                val strokeWidthPx = strokeWidth.toPx()

                // 单一边框，避免多层绘制
                drawRoundRect(
                    color = cachedColors.borderColor,
                    size = size,
                    cornerRadius = CornerRadius(cornerRadiusPx),
                    style = Stroke(width = strokeWidthPx),
                )
            },
        content = content,
    )
}

/*
 * ==========================================================================
 * 预览
 * ==========================================================================
 */

@GymBroPreview
@Composable
private fun WhiteBgLiquidGlassPreview() {
    GymBroTheme {
        Column(
            modifier = Modifier
                .background(ColorTokens.Light.Background) // 使用 Token 背景色
                .padding(Tokens.Spacing.Medium), // 1个字符宽度的内部间距
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Medium),
        ) {
            // 纯白卡片 - 使用 Token 高度和圆角
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Card.HeightMin) // 80dp - 标准最小卡片高度
                    .clip(RoundedCornerShape(Tokens.Radius.Card)) // 自然美观的圆角
                    .liquidGlassForWhiteBg(WhiteBgLiquidGlassAppearance.PureWhiteCard),
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "纯白液态卡片",
                        style = MaterialTheme.typography.bodyLarge,
                        color = ColorTokens.Light.OnSurface, // 使用 Token 文本色
                    )
                }
            }

            // 输入框背景 - 使用 Token 高度
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Input.HeightStandard) // 56dp - 标准输入框高度
                    .clip(RoundedCornerShape(Tokens.Radius.Input)) // 输入框圆角
                    .liquidGlassForWhiteBg(WhiteBgLiquidGlassAppearance.WhiteInput),
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "液态输入框背景",
                        style = MaterialTheme.typography.bodyLarge,
                        color = ColorTokens.Light.OnSurface, // 使用 Token 文本色
                    )
                }
            }

            // 按钮背景 - 使用 Token 高度
            WhiteBgLiquidButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Button.HeightPrimary) // 56dp - 标准按钮高度
                    .clip(RoundedCornerShape(Tokens.Radius.Button)), // 按钮圆角
                onClick = { /* 按钮点击 */ },
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "白色背景按钮",
                        style = MaterialTheme.typography.labelLarge,
                        color = ColorTokens.Light.Primary, // 使用 Token 主色
                    )
                }
            }

            // LiquidInputBackground示例 - 使用 Token 高度
            LiquidInputBackground(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(Tokens.Input.HeightStandard) // 56dp - 标准输入框高度
                    .clip(RoundedCornerShape(Tokens.Radius.Input)), // 输入框圆角
            ) {
                Box(
                    modifier = Modifier.padding(Tokens.Spacing.Medium), // 内部间距
                    contentAlignment = androidx.compose.ui.Alignment.Center,
                ) {
                    Text(
                        text = "输入框背景示例",
                        style = MaterialTheme.typography.bodyLarge,
                        color = ColorTokens.Light.OnSurface, // 使用 Token 文本色
                    )
                }
            }
        }
    }
}

package com.example.gymbro.core.network.security

import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

/**
 * StringXmlEscaper 单元测试
 * 
 * 验证 XML 字符转义和非法标签清理功能
 */
class StringXmlEscaperTest {

    private lateinit var stringXmlEscaper: StringXmlEscaper

    @BeforeEach
    fun setUp() {
        stringXmlEscaper = StringXmlEscaper()
    }

    @Nested
    @DisplayName("XML 字符转义测试")
    inner class XmlEscapeTests {

        @Test
        @DisplayName("应该正确转义基本 XML 特殊字符")
        fun `should escape basic xml special characters`() {
            val input = "Hello & <world> \"test\" 'value'"
            val expected = "Hello &amp; &lt;world&gt; &quot;test&quot; &#39;value&#39;"
            
            val result = stringXmlEscaper.escapeXmlContent(input)
            
            assertEquals(expected, result)
        }

        @Test
        @DisplayName("应该处理空字符串")
        fun `should handle empty string`() {
            val result = stringXmlEscaper.escapeXmlContent("")
            assertEquals("", result)
        }

        @Test
        @DisplayName("应该处理不包含特殊字符的字符串")
        fun `should handle string without special characters`() {
            val input = "Hello world test value"
            val result = stringXmlEscaper.escapeXmlContent(input)
            assertEquals(input, result)
        }

        @Test
        @DisplayName("应该处理复杂的嵌套字符")
        fun `should handle complex nested characters`() {
            val input = "<tag attr=\"value & 'test'\">content</tag>"
            val expected = "&lt;tag attr=&quot;value &amp; &#39;test&#39;&quot;&gt;content&lt;/tag&gt;"
            
            val result = stringXmlEscaper.escapeXmlContent(input)
            
            assertEquals(expected, result)
        }
    }

    @Nested
    @DisplayName("Phase 标签清理测试")
    inner class PhaseTagCleaningTests {

        @Test
        @DisplayName("应该清理完整的 phase 标签并保留内容")
        fun `should clean complete phase tags and preserve content`() {
            val input = "<phase:PLAN>我们需要制定计划</phase:PLAN>"
            val expected = "我们需要制定计划"
            
            val result = stringXmlEscaper.cleanPhaseTagsToText(input)
            
            assertEquals(expected, result)
        }

        @Test
        @DisplayName("应该清理多个 phase 标签")
        fun `should clean multiple phase tags`() {
            val input = "<phase:PLAN>计划内容</phase:PLAN>中间文本<phase:EXECUTE>执行内容</phase:EXECUTE>"
            val expected = "计划内容中间文本执行内容"
            
            val result = stringXmlEscaper.cleanPhaseTagsToText(input)
            
            assertEquals(expected, result)
        }

        @Test
        @DisplayName("应该清理单独的开始和结束标签")
        fun `should clean standalone start and end tags`() {
            val input = "<phase:PLAN>开始内容</phase:PLAN>中间<phase:TEST>结束内容"
            val expected = "开始内容中间结束内容"
            
            val result = stringXmlEscaper.cleanPhaseTagsToText(input)
            
            assertEquals(expected, result)
        }

        @Test
        @DisplayName("应该处理不完整的标签")
        fun `should handle incomplete tags`() {
            val input = "开始<phase:PL"
            val expected = "开始"
            
            val result = stringXmlEscaper.cleanPhaseTagsToText(input)
            
            assertEquals(expected, result)
        }

        @Test
        @DisplayName("应该保留合法的 XML 标签")
        fun `should preserve legal xml tags`() {
            val input = "<phase id=\"1\">合法内容</phase><thinking>思考内容</thinking>"
            val expected = "<phase id=\"1\">合法内容</phase><thinking>思考内容</thinking>"
            
            val result = stringXmlEscaper.cleanPhaseTagsToText(input)
            
            assertEquals(expected, result)
        }
    }

    @Nested
    @DisplayName("组合方法测试")
    inner class CombinedMethodTests {

        @Test
        @DisplayName("sanitizeAndEscape 应该先清理标签再转义")
        fun `sanitizeAndEscape should clean tags then escape`() {
            val input = "<phase:PLAN>计划 & 执行 <test></phase:PLAN>"
            val expected = "计划 &amp; 执行 &lt;test&gt;"
            
            val result = stringXmlEscaper.sanitizeAndEscape(input)
            
            assertEquals(expected, result)
        }

        @Test
        @DisplayName("sanitizeToken 应该清理控制字符")
        fun `sanitizeToken should clean control characters`() {
            val input = "Hello\u0000World\uFFFD\u0001Test"
            val expected = "HelloWorldTest"
            
            val result = stringXmlEscaper.sanitizeToken(input)
            
            assertEquals(expected, result)
        }
    }

    @Nested
    @DisplayName("验证方法测试")
    inner class ValidationTests {

        @Test
        @DisplayName("hasIllegalTags 应该检测非法标签")
        fun `hasIllegalTags should detect illegal tags`() {
            assertTrue(stringXmlEscaper.hasIllegalTags("<phase:PLAN>内容</phase:PLAN>"))
            assertTrue(stringXmlEscaper.hasIllegalTags("<phase:TEST>"))
            assertTrue(stringXmlEscaper.hasIllegalTags("</phase:END>"))
            assertFalse(stringXmlEscaper.hasIllegalTags("<phase id=\"1\">合法内容</phase>"))
            assertFalse(stringXmlEscaper.hasIllegalTags("普通文本"))
        }

        @Test
        @DisplayName("getCleaningStats 应该返回正确的统计信息")
        fun `getCleaningStats should return correct statistics`() {
            val original = "<phase:PLAN>测试内容</phase:PLAN>额外文本"
            val cleaned = "测试内容额外文本"
            
            val stats = stringXmlEscaper.getCleaningStats(original, cleaned)
            
            assertEquals(original.length, stats.originalLength)
            assertEquals(cleaned.length, stats.cleanedLength)
            assertEquals(original.length - cleaned.length, stats.removedChars)
            assertTrue(stats.hasEffectiveCleaning)
            assertTrue(stats.cleaningEfficiency > 0)
        }
    }

    @Nested
    @DisplayName("边界情况测试")
    inner class EdgeCaseTests {

        @Test
        @DisplayName("应该处理空字符串")
        fun `should handle empty strings`() {
            assertEquals("", stringXmlEscaper.escapeXmlContent(""))
            assertEquals("", stringXmlEscaper.cleanPhaseTagsToText(""))
            assertEquals("", stringXmlEscaper.sanitizeAndEscape(""))
            assertFalse(stringXmlEscaper.hasIllegalTags(""))
        }

        @Test
        @DisplayName("应该处理只包含空白字符的字符串")
        fun `should handle whitespace only strings`() {
            val whitespace = "   \n\t  "
            assertEquals(whitespace, stringXmlEscaper.escapeXmlContent(whitespace))
            assertEquals(whitespace, stringXmlEscaper.cleanPhaseTagsToText(whitespace))
        }

        @Test
        @DisplayName("应该处理非常长的字符串")
        fun `should handle very long strings`() {
            val longContent = "测试内容".repeat(1000)
            val input = "<phase:PLAN>$longContent</phase:PLAN>"
            
            val result = stringXmlEscaper.cleanPhaseTagsToText(input)
            
            assertEquals(longContent, result)
        }
    }
}

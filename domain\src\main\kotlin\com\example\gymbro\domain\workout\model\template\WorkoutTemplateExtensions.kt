package com.example.gymbro.domain.workout.model.template

import com.example.gymbro.shared.models.workout.*

/**
 * WorkoutTemplate转换扩展函数
 *
 * Phase1: 数据类型不匹配修复
 * 实现Domain模型到DTO的转换，支持Function Call兼容性
 */

/**
 * WorkoutTemplate (Domain) → WorkoutTemplateDto
 * 按照文档要求，UseCase返回DTO而不是Domain模型
 */
fun WorkoutTemplate.toDto(): WorkoutTemplateDto =
    WorkoutTemplateDto(
        id = this.id,
        name = this.name,
        description = this.description ?: "",
        difficulty = mapDifficultyToDto(this.difficulty),
        category = mapCategoryToDto(this.category),
        exercises = this.exercises.map { it.toDto() },
        source = TemplateSource.USER, // 默认用户创建
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        version = 1, // 默认版本
        // === Phase1版本控制字段 (Function Call兼容性) ===
        isDraft = this.isDraft, // 🔥 修复：直接使用Domain模型的isDraft字段
        currentVersion = this.currentVersion, // 🔥 修复：使用Domain模型的版本号
        isPublished = this.isPublished, // 🔥 修复：使用Domain模型的发布状态
        lastPublishedAt = this.lastPublishedAt, // 🔥 修复：使用Domain模型的发布时间
    )

/**
 * WorkoutTemplateDto → WorkoutTemplate (Domain)
 * 支持从DTO恢复Domain模型
 * @param userId 用户ID，用于正确的数据关联
 */
fun WorkoutTemplateDto.toDomain(userId: String? = null): WorkoutTemplate =
    WorkoutTemplate(
        id = this.id,
        name = this.name.takeIf { it.isNotBlank() } ?: "未命名训练", // 🔥 修复：防止空名称导致崩溃
        description = this.description.takeIf { it.isNotBlank() },
        userId = userId ?: "system", // 🔥 修复：使用传入的用户ID，fallback到system
        exercises = this.exercises.map { it.toDomain() },
        targetMuscleGroups = extractTargetMuscleGroups(this.exercises),
        estimatedDuration = calculateEstimatedDuration(this.exercises),
        difficulty = mapDifficultyFromDto(this.difficulty),
        tags = emptyList(), // 从exercises中提取或使用默认值
        imageUrl = null,
        isFavorite = false, // 默认值
        isPublic = this.actualIsPublished, // 使用兼容性属性
        usageCount = 0, // 默认值
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        category = this.category.name,
        lastUsedDate = null,
    )

/**
 * TemplateExercise (Domain) → TemplateExerciseDto
 * 🔥 修复：正确提取 customSets 数据，避免数据丢失
 */
fun TemplateExercise.toDto(): TemplateExerciseDto {
    return TemplateExerciseDto(
        id = this.id,
        exerciseId = this.exerciseId,
        exerciseName = this.name,
        sets = this.sets,
        reps = this.reps,
        rpe = null, // 默认值，需要后续扩展
        targetWeight = this.weight,
        restTimeSeconds = this.restSeconds,
        imageUrl = this.imageUrl, // 🔥 关键修复：保持动作库JSON数据
        videoUrl = this.videoUrl, // 🔥 关键修复：保持动作库JSON数据
        notes = this.notes,
        // 🔥 关键修复：直接映射 customSets 字段
        customSets = this.customSets.map { set ->
            com.example.gymbro.shared.models.workout.TemplateSetDto(
                setNumber = set.setNumber,
                targetWeight = set.targetWeight,
                targetReps = set.targetReps,
                restTimeSeconds = set.restTimeSeconds,
                targetDuration = set.targetDuration,
                rpe = set.rpe,
            )
        },
    )
}

/**
 * TemplateExerciseDto → TemplateExercise (Domain)
 * 🔥 修复：直接映射 customSets 字段，不需要序列化到 notes
 */
fun TemplateExerciseDto.toDomain(): TemplateExercise {
    return TemplateExercise(
        id = this.id ?: java.util.UUID.randomUUID().toString(),
        exerciseId = this.exerciseId,
        name = this.exerciseName ?: "Unknown Exercise",
        order = 0, // 默认值
        sets = this.sets,
        reps = this.reps,
        restSeconds = this.restTimeSeconds,
        weight = this.targetWeight,
        notes = this.notes?.takeIf { it.isNotBlank() },
        imageUrl = this.imageUrl, // 🔥 关键修复：保持动作库JSON数据
        videoUrl = this.videoUrl, // 🔥 关键修复：保持动作库JSON数据
        // 🔥 关键修复：直接映射 customSets 字段
        customSets = this.customSets.map { set ->
            TemplateSet(
                setNumber = set.setNumber,
                targetWeight = set.targetWeight,
                targetReps = set.targetReps,
                restTimeSeconds = set.restTimeSeconds,
                targetDuration = set.targetDuration,
                rpe = set.rpe,
            )
        },
    )
}

// === 辅助映射函数 ===

/**
 * 将Domain difficulty映射到DTO difficulty
 */
private fun mapDifficultyToDto(difficulty: Int?): Difficulty =
    when (difficulty) {
        1 -> Difficulty.EASY
        2 -> Difficulty.EASY
        3 -> Difficulty.MEDIUM
        4 -> Difficulty.HARD
        5 -> Difficulty.EXPERT
        else -> Difficulty.MEDIUM
    }

/**
 * 将DTO difficulty映射到Domain difficulty
 */
private fun mapDifficultyFromDto(difficulty: Difficulty): Int =
    when (difficulty) {
        Difficulty.EASY -> 2
        Difficulty.MEDIUM -> 3
        Difficulty.HARD -> 4
        Difficulty.EXPERT -> 5
    }

/**
 * 将Domain category映射到DTO category
 */
private fun mapCategoryToDto(category: String?): TemplateCategory =
    when (category?.lowercase()) {
        "strength" -> TemplateCategory.STRENGTH
        "cardio" -> TemplateCategory.CARDIO
        "flexibility" -> TemplateCategory.FLEXIBILITY
        "mobility" -> TemplateCategory.FLEXIBILITY
        else -> TemplateCategory.STRENGTH
    }

/**
 * 从exercises中提取目标肌群
 */
private fun extractTargetMuscleGroups(exercises: List<TemplateExerciseDto>): List<String> {
    // 简化实现，后续可以从Exercise服务获取详细信息
    return exercises
        .mapNotNull {
            it.exerciseName?.let { name ->
                when {
                    name.contains("胸", ignoreCase = true) -> "胸部"
                    name.contains("背", ignoreCase = true) -> "背部"
                    name.contains("腿", ignoreCase = true) -> "腿部"
                    name.contains("肩", ignoreCase = true) -> "肩部"
                    name.contains("臂", ignoreCase = true) -> "手臂"
                    else -> null
                }
            }
        }.distinct()
}

/**
 * 计算预估训练时长
 */
private fun calculateEstimatedDuration(exercises: List<TemplateExerciseDto>): Int {
    if (exercises.isEmpty()) return 0

    // 简化计算：每个动作平均5分钟
    val exerciseTime = exercises.size * 5
    // 加上热身和整理时间
    return exerciseTime + 15
}

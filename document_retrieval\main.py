# 文档检索系统主程序

import os
import os
import markdown
from whoosh.fields import Schema, TEXT, ID
from whoosh.index import create_in, open_dir
from whoosh.qparser import QueryParser
from whoosh.writing import AsyncWriter

INDEX_DIR = "indexdir"
DOC_PATH = "../docs" # 假设文档在项目根目录下的 docs 文件夹

def create_index():
    """创建或打开索引"""
    if not os.path.exists(INDEX_DIR):
        os.mkdir(INDEX_DIR)
        schema = Schema(title=TEXT(stored=True), path=ID(stored=True), content=TEXT)
        ix = create_in(INDEX_DIR, schema)
        print(f"索引目录 '{INDEX_DIR}' 创建成功。")
    else:
        ix = open_dir(INDEX_DIR)
        print(f"已打开现有索引目录 '{INDEX_DIR}'。")
    return ix

def index_documents(ix, doc_path):
    """索引指定路径下的 Markdown 文档"""
    writer = AsyncWriter(ix)
    for root, _, files in os.walk(doc_path):
        for file in files:
            if file.endswith(".md"):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        content = f.read()
                        # 简单的从第一个标题提取标题，否则使用文件名
                        lines = content.splitlines()
                        title = os.path.basename(filepath)
                        for line in lines:
                            if line.strip().startswith("# "):
                                title = line.strip("# ").strip()
                                break

                        writer.add_document(title=title, path=filepath, content=content)
                        print(f"已索引文件: {filepath}")
                except Exception as e:
                    print(f"索引文件失败 {filepath}: {e}")
    writer.commit()
    print("文档索引完成。")

def search_documents(ix, query_string):
    """在索引中搜索文档"""
    with ix.searcher() as searcher:
        query_parser = QueryParser("content", ix.schema)
        query = query_parser.parse(query_string)
        results = searcher.search(query)
        print(f"找到 {len(results)} 个匹配结果：")
        for hit in results:
            print(f"- {hit['title']} ({hit['path']})")

def main():
    print("文档检索系统启动！")
    ix = create_index()
    index_documents(ix, DOC_PATH)

    while True:
        query = input("\n请输入查询关键词 (输入 'quit' 退出): ")
        if query.lower() == 'quit':
            break
        search_documents(ix, query)

if __name__ == "__main__":
    main()

name: GymBro CI/CD Pipeline - ktlint & detekt

on:
  push:
    branches: [ master, develop ]
  pull_request:
    branches: [ master ]

jobs:
  code-quality:
    name: Code Quality Check (ktlint & detekt)
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
      
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Grant execute permission for architectural guardian
      run: chmod +x architectural_guardian.sh
      
    - name: Run Architectural Guardian (ktlint & detekt)
      run: ./architectural_guardian.sh
      
    - name: Upload detekt reports
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: detekt-reports
        path: |
          **/build/reports/detekt/detekt.html
          **/build/reports/detekt/detekt.xml
          
    - name: Upload ktlint reports  
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: ktlint-reports
        path: |
          **/build/reports/ktlint/**
          
  build:
    name: Build APK
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4
      
    - name: Set up JDK 17
      uses: actions/setup-java@v4
      with:
        java-version: '17'
        distribution: 'temurin'
        
    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-
          
    - name: Grant execute permission for gradlew
      run: chmod +x gradlew
      
    - name: Build Debug APK
      run: ./gradlew assembleDebug
      
    - name: Upload APK
      uses: actions/upload-artifact@v3
      with:
        name: debug-apk
        path: app/build/outputs/apk/debug/*.apk
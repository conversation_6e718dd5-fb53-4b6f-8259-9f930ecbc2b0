package com.example.gymbro.domain.workout.repository

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.workout.model.session.WorkoutSession
import com.example.gymbro.domain.workout.model.stats.DailyStats
import com.example.gymbro.domain.workout.model.stats.TimeRange
import com.example.gymbro.domain.workout.model.stats.UnifiedWorkoutStatistics
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.LocalDate

/**
 * 训练统计数据仓库接口
 *
 * 定义了统计数据的存储和检索操作接口，
 * 遵循Clean Architecture的Repository模式。
 *
 * 核心职责：
 * - 日级统计数据的CRUD操作
 * - 多时间维度统计数据查询
 * - Session数据的获取和聚合
 * - 提供响应式数据流
 * - 缓存策略和性能优化
 *
 * 实现层将在data模块中完成，支持：
 * - Room数据库本地存储
 * - SQL聚合查询优化
 * - 内存缓存层
 * - 数据同步机制
 */
interface StatsRepository {

    // ==================== 日级统计操作 ====================

    /**
     * 保存日级统计数据
     *
     * @param dailyStats 日级统计数据
     * @return 操作结果
     */
    suspend fun saveDailyStats(dailyStats: DailyStats): ModernResult<Unit>

    /**
     * 获取指定日期的统计数据
     *
     * @param date 日期
     * @return 日级统计数据，如果不存在则返回null
     */
    suspend fun getDailyStats(date: LocalDate): ModernResult<DailyStats?>

    /**
     * 获取指定日期范围内的日级统计数据
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 日级统计数据列表
     */
    suspend fun getDailyStatsInRange(
        startDate: LocalDate,
        endDate: LocalDate,
    ): ModernResult<List<DailyStats>>

    /**
     * 删除指定日期的统计数据
     *
     * @param date 日期
     * @return 操作结果
     */
    suspend fun deleteDailyStats(date: LocalDate): ModernResult<Unit>

    // ==================== 聚合统计操作 ====================

    /**
     * 获取指定时间范围的统计数据流
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param timeRange 时间范围类型
     * @return 统计数据流
     */
    fun getStatsInRange(
        startDate: LocalDate,
        endDate: LocalDate,
        timeRange: TimeRange,
    ): Flow<ModernResult<UnifiedWorkoutStatistics>>

    /**
     * 获取用户的所有统计数据（用于数据导出等）
     *
     * @param userId 用户ID
     * @param limit 限制条数（可选）
     * @return 统计数据列表
     */
    suspend fun getAllUserStats(
        userId: String,
        limit: Int? = null,
    ): ModernResult<List<DailyStats>>

    // ==================== Session相关操作 ====================

    /**
     * 获取Session及其Exercise详情（用于统计聚合）
     *
     * @param sessionId Session ID
     * @return Session详细数据
     */
    suspend fun getSessionWithExercises(sessionId: String): ModernResult<WorkoutSession>

    /**
     * 获取指定日期的所有Session
     *
     * @param userId 用户ID
     * @param date 日期
     * @return Session列表
     */
    suspend fun getSessionsByDate(
        userId: String,
        date: LocalDate,
    ): ModernResult<List<WorkoutSession>>

    /**
     * 获取指定日期范围内的Session数量
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return Session数量
     */
    suspend fun getSessionCountInRange(
        userId: String,
        startDate: LocalDate,
        endDate: LocalDate,
    ): ModernResult<Int>

    // ==================== 缓存和优化 ====================

    /**
     * 清除统计数据缓存
     *
     * @param userId 用户ID（可选，如果不提供则清除所有缓存）
     */
    suspend fun clearStatsCache(userId: String? = null)

    /**
     * 预加载指定时间范围的统计数据
     * 用于性能优化，提前加载常用数据
     *
     * @param userId 用户ID
     * @param timeRange 时间范围
     * @return 操作结果
     */
    suspend fun preloadStatsForRange(
        userId: String,
        timeRange: TimeRange,
    ): ModernResult<Unit>

    // ==================== 数据维护操作 ====================

    /**
     * 重新计算指定时间范围的统计数据
     * 用于数据修复或重新计算场景
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 操作结果
     */
    suspend fun recalculateStatsInRange(
        userId: String,
        startDate: LocalDate,
        endDate: LocalDate,
    ): ModernResult<Unit>

    /**
     * 检查数据完整性
     * 验证统计数据与Session数据的一致性
     *
     * @param userId 用户ID
     * @return 完整性检查结果
     */
    suspend fun checkDataIntegrity(userId: String): ModernResult<DataIntegrityReport>

    /**
     * 数据迁移辅助方法
     * 将旧格式的统计数据迁移到新格式
     *
     * @param userId 用户ID
     * @return 迁移结果
     */
    suspend fun migrateStatsData(userId: String): ModernResult<MigrationReport>
}

/**
 * 数据完整性检查报告
 */
data class DataIntegrityReport(
    val totalChecked: Int,
    val missingStats: List<LocalDate>,
    val inconsistentStats: List<LocalDate>,
    val isHealthy: Boolean,
) {
    companion object {
        fun healthy() = DataIntegrityReport(
            totalChecked = 0,
            missingStats = emptyList(),
            inconsistentStats = emptyList(),
            isHealthy = true,
        )
    }
}

/**
 * 数据迁移报告
 */
data class MigrationReport(
    val totalMigrated: Int,
    val successCount: Int,
    val failedCount: Int,
    val errors: List<String>,
)
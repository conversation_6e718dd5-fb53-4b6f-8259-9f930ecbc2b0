package com.example.gymbro.data.workout.stats.dao

import androidx.room.*
import com.example.gymbro.data.workout.stats.entity.DailyStatsEntity
import com.example.gymbro.data.workout.stats.entity.StatsAggregationResult
import com.example.gymbro.data.workout.stats.entity.TimeRangeStatsResult
import kotlinx.coroutines.flow.Flow

/**
 * 日级统计数据访问对象 - Room DAO
 *
 * 提供日级统计数据的完整CRUD操作和高效聚合查询功能。
 * 基于Clean Architecture设计，支持响应式数据流和复杂统计分析。
 *
 * 核心功能：
 * - 基础CRUD操作
 * - 时间范围查询
 * - 统计聚合计算
 * - 响应式数据流
 * - 性能优化查询
 */
@Dao
interface DailyStatsDao {

    // ==================== 基础CRUD操作 ====================

    /**
     * 插入或更新日级统计数据
     * 使用REPLACE策略，确保每用户每日唯一记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdate(stats: DailyStatsEntity)

    /**
     * 批量插入或更新统计数据
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertOrUpdateMultiple(statsList: List<DailyStatsEntity>)

    /**
     * 更新统计数据
     */
    @Update
    suspend fun update(stats: DailyStatsEntity)

    /**
     * 删除指定用户指定日期的统计数据
     */
    @Query("DELETE FROM daily_stats WHERE userId = :userId AND date = :date")
    suspend fun delete(userId: String, date: String)

    /**
     * 删除指定用户的所有统计数据
     */
    @Query("DELETE FROM daily_stats WHERE userId = :userId")
    suspend fun deleteAllUserStats(userId: String)

    // ==================== 基础查询操作 ====================

    /**
     * 获取指定用户指定日期的统计数据
     */
    @Query("SELECT * FROM daily_stats WHERE userId = :userId AND date = :date")
    suspend fun getStatsByDate(userId: String, date: String): DailyStatsEntity?

    /**
     * 观察指定用户指定日期的统计数据（响应式）
     */
    @Query("SELECT * FROM daily_stats WHERE userId = :userId AND date = :date")
    fun observeStatsByDate(userId: String, date: String): Flow<DailyStatsEntity?>

    /**
     * 获取指定用户的所有统计数据
     */
    @Query("SELECT * FROM daily_stats WHERE userId = :userId ORDER BY date DESC")
    suspend fun getAllUserStats(userId: String): List<DailyStatsEntity>

    /**
     * 观察指定用户的所有统计数据（响应式）
     */
    @Query("SELECT * FROM daily_stats WHERE userId = :userId ORDER BY date DESC")
    fun observeAllUserStats(userId: String): Flow<List<DailyStatsEntity>>

    // ==================== 时间范围查询 ====================

    /**
     * 获取指定时间范围内的统计数据
     */
    @Query(
        """
        SELECT * FROM daily_stats 
        WHERE userId = :userId 
        AND date >= :startDate 
        AND date <= :endDate 
        ORDER BY date ASC
    """,
    )
    suspend fun getStatsInRange(
        userId: String,
        startDate: String,
        endDate: String,
    ): List<DailyStatsEntity>

    /**
     * 观察指定时间范围内的统计数据（响应式）
     */
    @Query(
        """
        SELECT * FROM daily_stats 
        WHERE userId = :userId 
        AND date >= :startDate 
        AND date <= :endDate 
        ORDER BY date ASC
    """,
    )
    fun observeStatsInRange(
        userId: String,
        startDate: String,
        endDate: String,
    ): Flow<List<DailyStatsEntity>>

    /**
     * 获取最近N天的统计数据
     */
    @Query(
        """
        SELECT * FROM daily_stats 
        WHERE userId = :userId 
        ORDER BY date DESC 
        LIMIT :days
    """,
    )
    suspend fun getRecentStats(userId: String, days: Int): List<DailyStatsEntity>

    /**
     * 观察最近N天的统计数据（响应式）
     */
    @Query(
        """
        SELECT * FROM daily_stats 
        WHERE userId = :userId 
        ORDER BY date DESC 
        LIMIT :days
    """,
    )
    fun observeRecentStats(userId: String, days: Int): Flow<List<DailyStatsEntity>>

    // ==================== 统计聚合查询 ====================

    /**
     * 获取指定时间范围的聚合统计
     */
    @Query(
        """
        SELECT 
            SUM(completedSessions) as totalSessions,
            SUM(completedExercises) as totalExercises,
            SUM(completedSets) as totalSets,
            SUM(totalReps) as totalReps,
            SUM(totalWeight) as totalWeight,
            SUM(sessionDurationSec) as totalDuration,
            AVG(avgRpe) as avgRpe,
            MIN(date) as firstDate,
            MAX(date) as lastDate
        FROM daily_stats 
        WHERE userId = :userId 
        AND date >= :startDate 
        AND date <= :endDate
    """,
    )
    suspend fun getAggregatedStats(
        userId: String,
        startDate: String,
        endDate: String,
    ): StatsAggregationResult?

    /**
     * 获取时间范围统计摘要
     */
    @Query(
        """
        SELECT 
            userId,
            :startDate as startDate,
            :endDate as endDate,
            COUNT(*) as totalSessions,
            SUM(totalWeight) as totalWeight,
            SUM(sessionDurationSec) as totalDuration,
            CAST(COUNT(*) AS FLOAT) / (julianday(:endDate) - julianday(:startDate) + 1) as avgSessionsPerDay,
            CASE 
                WHEN COUNT(*) > 0 THEN SUM(totalWeight) / COUNT(*) 
                ELSE 0 
            END as avgWeightPerSession,
            CASE 
                WHEN COUNT(*) > 0 THEN SUM(sessionDurationSec) / COUNT(*) 
                ELSE 0 
            END as avgDurationPerSession
        FROM daily_stats 
        WHERE userId = :userId 
        AND date >= :startDate 
        AND date <= :endDate
        GROUP BY userId
    """,
    )
    suspend fun getTimeRangeStats(
        userId: String,
        startDate: String,
        endDate: String,
    ): TimeRangeStatsResult?

    // ==================== 性能查询 ====================

    /**
     * 获取统计数据总数
     */
    @Query("SELECT COUNT(*) FROM daily_stats WHERE userId = :userId")
    suspend fun getStatsCount(userId: String): Int

    /**
     * 检查指定日期是否有统计数据
     */
    @Query("SELECT COUNT(*) > 0 FROM daily_stats WHERE userId = :userId AND date = :date")
    suspend fun hasStatsForDate(userId: String, date: String): Boolean

    /**
     * 获取最早的统计日期
     */
    @Query("SELECT MIN(date) FROM daily_stats WHERE userId = :userId")
    suspend fun getEarliestStatsDate(userId: String): String?

    /**
     * 获取最新的统计日期
     */
    @Query("SELECT MAX(date) FROM daily_stats WHERE userId = :userId")
    suspend fun getLatestStatsDate(userId: String): String?

    // ==================== 数据维护操作 ====================

    /**
     * 清理指定日期之前的旧数据
     */
    @Query("DELETE FROM daily_stats WHERE userId = :userId AND date < :cutoffDate")
    suspend fun cleanupOldStats(userId: String, cutoffDate: String): Int

    /**
     * 获取需要清理的数据数量
     */
    @Query("SELECT COUNT(*) FROM daily_stats WHERE userId = :userId AND date < :cutoffDate")
    suspend fun getCleanupCount(userId: String, cutoffDate: String): Int
}

# ThinkingBox v3.0 MVI架构核心设计

## 核心架构原则
- **直接监听架构**: ThinkingBoxViewModel直接监听ConversationScope，消除重复Token流处理
- **MVI架构实现**: Intent → State → Effect的完整数据流，ThinkingBoxContract定义契约
- **Coach/ThinkingBox完全分离**: 单向数据流，零状态共享，Coach只负责UI容器+数据接收
- **双时序架构保持**: 数据时序(Backend Processing) + UI时序(Frontend Rendering)完全分离

## 关键组件
- **ThinkingBoxContract**: 定义Intent/State/Effect契约，@Immutable状态
- **ThinkingBoxViewModel**: 统一状态管理，直接监听ConversationScope.tokens
- **ThinkingBoxScreen**: 纯UI组合器，100%复用现有组件
- **StreamingThinkingMLParser**: 无状态XML解析，完全可重入
- **ThinkingReducer**: 双时序调度逻辑，LinkedHashMap+ArrayDeque优化

## Token流处理架构
v3.0单一处理: ConversationScope → ThinkingBoxViewModel → StreamingThinkingMLParser → DomainMapper → ThinkingReducer → Contract.State → UI
消除v2.x重复处理: 减少50%处理开销，提升响应速度
package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.coachTheme // 🔥 【颜色修复】添加 Coach 主题导入
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.shared.models.coach.ExtendedSuggestion
import com.example.gymbro.shared.models.coach.SuggestionConfig

/**
 * 建议面板状态枚举 - 精确控制展开逻辑
 */
enum class SuggestionPanelState {
    COLLAPSED, // 收起状态
    EXPANDING, // 展开中
    EXPANDED, // 完全展开
    COLLAPSING, // 收起中
}

/**
 * 建议分类枚举
 */
enum class SuggestionCategory {
    TRAINING, // 训练相关
    NUTRITION, // 营养相关
    RECOVERY, // 恢复相关
    GENERAL, // 通用建议
}

/**
 * 🎨 ChatGPT风格建议面板 - Token化优化版
 *
 * 🎯 设计哲学：
 * - 完全模仿ChatGPT的建议面板视觉效果
 * - 使用统一Token系统，零硬编码
 * - 安全的高度约束，避免布局冲突
 * - 流畅的动画过渡效果
 * - 极简的文本建议项设计
 *
 * 🔧 Token化要点：
 * - 圆角：Tokens.Radius.XLarge (24dp) 匹配输入框
 * - 间距：Tokens.Spacing.* 替代硬编码
 * - 字体：Tokens.Typography.* 替代硬编码
 * - 阴影：Tokens.Shadow.* 替代硬编码
 * - 颜色：完全基于MaterialTheme
 *
 * 🚨 高度安全策略：
 * - 使用wrapContentHeight() + heightIn()组合
 * - 限制最大显示项目数（避免过长）
 * - Column替代LazyColumn（避免嵌套滚动）
 * - 自适应内容高度，绝不强制约束
 *
 * @param modifier 修饰符
 * @param onSuggestionClick 建议点击回调
 * @param onDismiss 关闭回调
 * @param showCloseButton 是否显示关闭按钮
 * @param panelState 面板状态（用于精确控制展开逻辑）
 * @param animationEnabled 是否启用动画效果
 */
@Composable
fun ExpandedSuggestionPanel(
    isVisible: Boolean,
    inputText: String,
    suggestionConfig: SuggestionConfig?,
    onSuggestionClick: (String) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (!isVisible) return

    // 🔥 使用统一的建议匹配逻辑，消除重复代码
    val suggestions = remember(inputText, suggestionConfig) {
        SuggestionMatcher.getMatchingSuggestions(inputText, suggestionConfig, maxCount = 4)
    }

    if (suggestions.isEmpty()) return

    // 🔥 【透明化修复】移除外层Surface，直接使用Column确保完全透明
    Column(
        modifier =
        modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        // 🔥 减少垂直padding
        verticalArrangement = Arrangement.spacedBy(0.dp),
    ) {
        suggestions.forEach { suggestion ->
            ChatGPTSuggestionItem(
                text = suggestion.title,
                onClick = { onSuggestionClick(suggestion.prompt) },
            )
        }
    }
}

/**
 * 🎨 ChatGPT风格建议项 - 极简文本行设计
 */
@Composable
private fun ChatGPTSuggestionItem(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        onClick = onClick,
        modifier = modifier.fillMaxWidth(),
        color = Color.Transparent, // 🔥 透明背景，点击时有波纹
        shape = RoundedCornerShape(0.dp), // 🔥 无圆角，纯净文本行
    ) {
        Text(
            text = text,
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    horizontal = Tokens.Spacing.Medium, // 🔥 【颜色修复】使用项目tokens
                    vertical = Tokens.Spacing.Small, // 🔥 【颜色修复】使用项目tokens
                ),
            style = MaterialTheme.typography.bodyLarge,
            color = MaterialTheme.coachTheme.textSecondary, // 🔥 【颜色修复】使用Coach主题颜色
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )
    }
}

/**
 * 🗑️ 移除Demo版本，保持单一实现
 * 原有的ExpandedSuggestionPanelDemo已整合到主组件中
 */

// ==================== 预览组件 ====================

@GymBroPreview
@Composable
private fun ExpandedSuggestionPanelPreview() {
    GymBroTheme {
        val mockConfig = SuggestionConfig(
            quickSuggestions = emptyList(),
            extendedSuggestions = mapOf(
                "减肥" to listOf(
                    ExtendedSuggestion("1", "制定下一次假期的出行计划", "..."),
                    ExtendedSuggestion("2", "构思一个品牌活动的点子", "..."),
                    ExtendedSuggestion("3", "构思新的锻炼计划", "..."),
                    ExtendedSuggestion("4", "构思一下晚会菜单", "..."),
                ),
            ),
        )

        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(Tokens.Spacing.Medium),
        ) {
            ExpandedSuggestionPanel(
                isVisible = true,
                inputText = "减肥",
                suggestionConfig = mockConfig,
                onSuggestionClick = { },
                onDismiss = { },
            )
        }
    }
}

@GymBroPreview
@Composable
private fun ChatGPTSuggestionItemPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            color = MaterialTheme.colorScheme.surface, // 🔥 与建议面板相同背景
        ) {
            ChatGPTSuggestionItem(
                text = "制定个人训练计划",
                onClick = { },
            )
        }
    }
}

@GymBroPreview
@Composable
private fun ChatGPTSuggestionPanelDarkThemePreview() {
    GymBroTheme(darkTheme = true) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background)
                .padding(Tokens.Spacing.Medium),
        ) {
            ExpandedSuggestionPanel(
                isVisible = true,
                inputText = "健身",
                suggestionConfig = SuggestionConfig(
                    quickSuggestions = emptyList(),
                    extendedSuggestions = mapOf(
                        "健身" to listOf(
                            ExtendedSuggestion("1", "如何开始健身训练", "..."),
                            ExtendedSuggestion("2", "健身房器械使用指南", "..."),
                        ),
                    ),
                ),
                onSuggestionClick = { },
                onDismiss = { },
                modifier = Modifier.align(Alignment.Center),
            )
        }
    }
}

package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.coach.repository.AICoachRepository
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * AI教练功能的用例 - 专注于AI交互
 *
 * 职责简化：
 * - 只负责AI消息发送和接收
 * - 会话管理功能已移至ChatSessionManagementUseCase
 *
 * 优化说明：
 * - 移除了getCoachingHistory和clearCoachingHistory方法
 * - 这些功能现在由ChatSessionManagementUseCase统一管理
 * - 保持单一职责原则：专注AI交互逻辑
 */
@Singleton
class AiCoachUseCase
@Inject
constructor(
    private val aiCoachRepository: AICoachRepository,
    private val logger: Logger,
) {
    /**
     * 发送消息给AI教练并获取回复（支持多轮对话）
     *
     * @param sessionId 会话ID，用于维护对话上下文
     * @param userInput 用户输入的消息内容
     * @return 包含AI回复消息或错误的Flow
     */
    suspend fun sendMessage(
        sessionId: String,
        userInput: String,
    ): Flow<ModernResult<CoachMessage>> {
        logger.d("Sending message to AI coach: sessionId=$sessionId, input=${userInput.take(50)}...")
        return aiCoachRepository.sendMessageToCoach(sessionId, userInput)
    }

    /**
     * 发送消息给AI教练并获取原始的流式响应
     *
     * 🔥 新增：接收已构建的消息列表，避免重复prompt构建
     * @param sessionId 会话ID
     * @param messages 已构建的消息列表
     * @return 包含原始字符串token的Flow
     */
    fun streamMessage(
        sessionId: String,
        messages: List<com.example.gymbro.core.ai.prompt.builder.CoreChatMessage>,
    ): Flow<String> {
        logger.d(
            "🚀 AiCoachUseCase.streamMessage() 开始 - sessionId=$sessionId, messages=${messages.size}条",
        )

        val flow = aiCoachRepository.getStreamingResponse(sessionId, messages)
        logger.d("🔄 AiCoachUseCase.streamMessage() 获取到Flow，开始返回")

        return flow
    }

    /**
     * 发送消息给AI教练并获取原始的流式响应（兼容性方法）
     *
     * @deprecated 使用streamMessage(messages)避免重复prompt构建
     * @param sessionId 会话ID
     * @param userInput 用户输入的消息内容
     * @return 包含原始字符串token的Flow
     */
    @Deprecated("使用streamMessage(messages)避免重复prompt构建")
    fun streamMessageLegacy(
        sessionId: String,
        userInput: String,
    ): Flow<String> {
        logger.d(
            "🚀 AiCoachUseCase.streamMessageLegacy() 开始 - sessionId=$sessionId, input=${userInput.take(
                50,
            )}...",
        )
        val flow = aiCoachRepository.getStreamingResponseLegacy(sessionId, userInput)
        logger.d("🔄 AiCoachUseCase.streamMessageLegacy() 获取到Flow，开始返回")
        return flow
    }
}

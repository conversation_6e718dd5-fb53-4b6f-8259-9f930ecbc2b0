package com.example.gymbro.data.workout.template.json

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.common.CommonFeatureErrors
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.workout.mapper.toDomain
import com.example.gymbro.domain.workout.mapper.toDto
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.port.JsonProcessorPort
import com.example.gymbro.shared.models.workout.WorkoutTemplateDto
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * JSON处理器实现（Data层）
 * 实现Domain层定义的JsonProcessorPort接口
 *
 * 这个实现类将原有的TemplateJsonDataProcessor重构为符合
 * Clean Architecture的接口实现，确保依赖关系正确
 */
@Singleton
class JsonProcessorImpl @Inject constructor() : JsonProcessorPort {

    private val json = Json {
        ignoreUnknownKeys = true
        encodeDefaults = true
        isLenient = true
        prettyPrint = true
        explicitNulls = true
        coerceInputValues = true
    }

    override suspend fun serializeTemplate(template: WorkoutTemplate): ModernResult<String> {
        return try {
            val dto = template.toDto()
            val jsonString = json.encodeToString(WorkoutTemplateDto.serializer(), dto)
            Timber.d("Template序列化成功: ${template.id}")
            ModernResult.success(jsonString)
        } catch (e: SerializationException) {
            Timber.e(e, "Template序列化失败: ${template.id}")
            ModernResult.error(
                CommonFeatureErrors.JsonError.serializationError(
                    operationName = "JsonProcessorImpl.serializeTemplate",
                    message = UiText.DynamicString("锻炼模板序列化失败"),
                    dataType = "WorkoutTemplate",
                    cause = e,
                    metadataMap = mapOf("templateId" to template.id),
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "Template序列化意外错误: ${template.id}")
            ModernResult.error(
                CommonFeatureErrors.JsonError.serializationError(
                    operationName = "JsonProcessorImpl.serializeTemplate",
                    message = UiText.DynamicString("锻炼模板序列化意外错误"),
                    dataType = "WorkoutTemplate",
                    cause = e,
                    metadataMap = mapOf("templateId" to template.id),
                ),
            )
        }
    }

    override suspend fun deserializeTemplate(jsonString: String): ModernResult<WorkoutTemplate> {
        return try {
            val dto = json.decodeFromString(WorkoutTemplateDto.serializer(), jsonString)

            // 调试日志：仅在数据异常时记录
            if (dto.exercises.isEmpty()) {
                Timber.tag("WK-DEBUG").w("⚠️ [JSON-TO-DTO] 解析DTO成功但无动作数据")
            } else {
                val exercisesWithoutSets = dto.exercises.count { it.customSets.isEmpty() }
                if (exercisesWithoutSets > 0) {
                    Timber.tag("WK-DEBUG").w("⚠️ [JSON-TO-DTO] $exercisesWithoutSets 个动作缺少customSets数据")
                }
            }

            val domain = dto.toDomain()

            // 调试日志：仅在数据异常时记录
            if (domain.exercises.isEmpty()) {
                Timber.tag("WK-DEBUG").w("⚠️ [DTO-TO-DOMAIN] 转换Domain成功但无动作数据")
            } else {
                val exercisesWithoutSets = domain.exercises.count { it.customSets.isEmpty() }
                if (exercisesWithoutSets > 0) {
                    Timber.tag("WK-DEBUG").w("⚠️ [DTO-TO-DOMAIN] $exercisesWithoutSets 个动作缺少customSets数据")
                }
            }

            ModernResult.success(domain)
        } catch (e: SerializationException) {
            Timber.e(e, "Template反序列化失败")
            ModernResult.error(
                CommonFeatureErrors.JsonError.deserializationError(
                    operationName = "JsonProcessorImpl.deserializeTemplate",
                    message = UiText.DynamicString("锻炼模板反序列化失败"),
                    dataType = "WorkoutTemplate",
                    cause = e,
                    metadataMap = mapOf("jsonLength" to jsonString.length),
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "Template反序列化意外错误")
            ModernResult.error(
                CommonFeatureErrors.JsonError.deserializationError(
                    operationName = "JsonProcessorImpl.deserializeTemplate",
                    message = UiText.DynamicString("锻炼模板反序列化意外错误"),
                    dataType = "WorkoutTemplate",
                    cause = e,
                    metadataMap = mapOf("jsonLength" to jsonString.length),
                ),
            )
        }
    }

    override suspend fun validateJson(jsonString: String): ModernResult<Boolean> {
        return try {
            // 尝试解析JSON来验证格式
            json.parseToJsonElement(jsonString)
            ModernResult.success(true)
        } catch (e: SerializationException) {
            Timber.w("JSON验证失败: ${e.message}")
            ModernResult.success(false)
        } catch (e: Exception) {
            Timber.e(e, "JSON验证意外错误")
            ModernResult.error(
                CommonFeatureErrors.JsonError.deserializationError(
                    operationName = "JsonProcessorImpl.validateJson",
                    message = UiText.DynamicString("JSON验证失败"),
                    dataType = "JSON",
                    cause = e,
                    metadataMap = mapOf("jsonLength" to jsonString.length),
                ),
            )
        }
    }

    override suspend fun formatJson(jsonString: String): ModernResult<String> {
        return try {
            val jsonElement = json.parseToJsonElement(jsonString)
            val formattedJson = Json {
                prettyPrint = true
                ignoreUnknownKeys = true
            }.encodeToString(kotlinx.serialization.json.JsonElement.serializer(), jsonElement)
            ModernResult.success(formattedJson)
        } catch (e: SerializationException) {
            Timber.e(e, "JSON格式化失败")
            ModernResult.error(
                CommonFeatureErrors.JsonError.serializationError(
                    operationName = "JsonProcessorImpl.formatJson",
                    message = UiText.DynamicString("JSON格式化失败"),
                    dataType = "JSON",
                    cause = e,
                    metadataMap = mapOf("jsonLength" to jsonString.length),
                ),
            )
        } catch (e: Exception) {
            Timber.e(e, "JSON格式化意外错误")
            ModernResult.error(
                CommonFeatureErrors.JsonError.serializationError(
                    operationName = "JsonProcessorImpl.formatJson",
                    message = UiText.DynamicString("JSON格式化意外错误"),
                    dataType = "JSON",
                    cause = e,
                    metadataMap = mapOf("jsonLength" to jsonString.length),
                ),
            )
        }
    }
}
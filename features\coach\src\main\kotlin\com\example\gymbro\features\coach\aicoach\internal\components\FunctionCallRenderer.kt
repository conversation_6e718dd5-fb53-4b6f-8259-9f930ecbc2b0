package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.FitnessCenter
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.theme.coachTheme
import com.example.gymbro.features.coach.aicoach.AiCoachContract

/**
 * Function Call 智能渲染器
 *
 * 根据 Function Call 结果的类型动态渲染不同的UI界面
 * 完全符合 710funtioncall UI.md 文档规范
 *
 * @param result Function Call 结果数据
 * @param onDismiss 关闭弹窗回调
 * @param onAction 操作回调，用于回填文本到输入框
 * @param modifier 修饰符
 */
@Composable
internal fun FunctionCallRenderer(
    result: AiCoachContract.FunctionCallResult,
    onDismiss: () -> Unit,
    onAction: (prefillText: String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        // 🔥 标题区域
        FunctionCallHeader(result = result)

        // 🔥 内容区域 - 根据函数类型智能渲染
        when {
            result.functionName.startsWith("gymbro.plan.search") -> {
                RenderPlanSearchResults(result = result, onAction = onAction)
            }
            result.functionName.startsWith("gymbro.plan.generate") -> {
                RenderGeneratedPlan(result = result, onAction = onAction)
            }
            result.functionName.startsWith("gymbro.exercise.search") -> {
                RenderExerciseSearchResults(result = result, onAction = onAction)
            }
            result.functionName.startsWith("gymbro.session.start") -> {
                RenderSessionStart(result = result, onAction = onAction)
            }
            else -> {
                RenderDefaultResult(result = result, onAction = onAction)
            }
        }

        // 🔥 操作按钮区域
        FunctionCallActions(
            result = result,
            onDismiss = onDismiss,
            onAction = onAction,
        )
    }
}

/**
 * Function Call 标题区域
 */
@Composable
private fun FunctionCallHeader(
    result: AiCoachContract.FunctionCallResult,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        // 状态图标
        Icon(
            imageVector = if (result.success) Icons.Filled.CheckCircle else Icons.Filled.Error,
            contentDescription = null,
            tint = if (result.success) {
                MaterialTheme.coachTheme.accentPrimary
            } else {
                MaterialTheme.colorScheme.error
            },
            modifier = Modifier.size(32.dp),
        )

        // 标题文本
        Column(
            modifier = Modifier.weight(1f),
        ) {
            Text(
                text = getFunctionDisplayName(result.functionName),
                style = MaterialTheme.typography.headlineSmall,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.coachTheme.textPrimary,
            )

            Text(
                text = if (result.success) "执行成功" else "执行失败",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.coachTheme.textSecondary,
            )
        }
    }
}

/**
 * 计划搜索结果渲染
 */
@Composable
private fun RenderPlanSearchResults(
    result: AiCoachContract.FunctionCallResult,
    onAction: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.coachTheme.backgroundElevated,
        ),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                Icon(
                    imageVector = Icons.Filled.Search,
                    contentDescription = null,
                    tint = MaterialTheme.coachTheme.iconPrimary,
                    modifier = Modifier.size(20.dp),
                )
                Text(
                    text = "搜索结果",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.coachTheme.textPrimary,
                )
            }

            if (result.success && result.data != null) {
                Text(
                    text = result.data,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.coachTheme.textPrimary,
                )
            } else if (!result.success && result.error != null) {
                Text(
                    text = result.error,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error,
                )
            }
        }
    }
}

/**
 * 生成计划结果渲染
 */
@Composable
private fun RenderGeneratedPlan(
    result: AiCoachContract.FunctionCallResult,
    onAction: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.coachTheme.backgroundElevated,
        ),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp),
            ) {
                Icon(
                    imageVector = Icons.Filled.FitnessCenter,
                    contentDescription = null,
                    tint = MaterialTheme.coachTheme.iconPrimary,
                    modifier = Modifier.size(20.dp),
                )
                Text(
                    text = "训练计划",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold,
                    color = MaterialTheme.coachTheme.textPrimary,
                )
            }

            if (result.success && result.data != null) {
                Text(
                    text = result.data,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.coachTheme.textPrimary,
                )
            } else if (!result.success && result.error != null) {
                Text(
                    text = result.error,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error,
                )
            }
        }
    }
}

/**
 * 动作搜索结果渲染
 */
@Composable
private fun RenderExerciseSearchResults(
    result: AiCoachContract.FunctionCallResult,
    onAction: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.coachTheme.backgroundElevated,
        ),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                text = "动作搜索结果",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.coachTheme.textPrimary,
            )

            if (result.success && result.data != null) {
                Text(
                    text = result.data,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.coachTheme.textPrimary,
                )
            } else if (!result.success && result.error != null) {
                Text(
                    text = result.error,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error,
                )
            }
        }
    }
}

/**
 * 训练会话开始结果渲染
 */
@Composable
private fun RenderSessionStart(
    result: AiCoachContract.FunctionCallResult,
    onAction: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.coachTheme.backgroundElevated,
        ),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                text = "训练会话",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.coachTheme.textPrimary,
            )

            if (result.success && result.data != null) {
                Text(
                    text = result.data,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.coachTheme.textPrimary,
                )
            } else if (!result.success && result.error != null) {
                Text(
                    text = result.error,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error,
                )
            }
        }
    }
}

/**
 * 默认结果渲染
 */
@Composable
private fun RenderDefaultResult(
    result: AiCoachContract.FunctionCallResult,
    onAction: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Card(
        modifier = modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.coachTheme.backgroundElevated,
        ),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
        ) {
            Text(
                text = "执行结果",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.SemiBold,
                color = MaterialTheme.coachTheme.textPrimary,
            )

            if (result.success && result.data != null) {
                Text(
                    text = result.data,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.coachTheme.textPrimary,
                )
            } else if (!result.success && result.error != null) {
                Text(
                    text = result.error,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.error,
                )
            }

            // 显示执行路径（调试信息）
            if (result.executionPath != null) {
                HorizontalDivider(color = MaterialTheme.coachTheme.dividerPrimary)
                Text(
                    text = "执行路径: ${result.executionPath}",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.coachTheme.textSecondary,
                    fontFamily = androidx.compose.ui.text.font.FontFamily.Monospace,
                )
            }
        }
    }
}

/**
 * Function Call 操作按钮区域
 */
@Composable
private fun FunctionCallActions(
    result: AiCoachContract.FunctionCallResult,
    onDismiss: () -> Unit,
    onAction: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
    ) {
        // 关闭按钮
        OutlinedButton(
            onClick = onDismiss,
            modifier = Modifier.weight(1f),
            colors = ButtonDefaults.outlinedButtonColors(
                contentColor = MaterialTheme.coachTheme.textSecondary,
            ),
        ) {
            Text("关闭")
        }

        // 操作按钮（仅在成功时显示）
        if (result.success) {
            Button(
                onClick = {
                    val actionText = generateActionText(result)
                    onAction(actionText)
                },
                modifier = Modifier.weight(1f),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.coachTheme.accentCTA,
                    contentColor = MaterialTheme.coachTheme.textPrimary,
                ),
            ) {
                Text(getActionButtonText(result))
            }
        }
    }
}

/**
 * 获取工具显示名称
 */
private fun getFunctionDisplayName(functionName: String): String {
    return when {
        functionName.startsWith("gymbro.plan.search") -> "搜索训练计划"
        functionName.startsWith("gymbro.plan.generate") -> "生成训练计划"
        functionName.startsWith("gymbro.exercise.search") -> "搜索训练动作"
        functionName.startsWith("gymbro.session.start") -> "开始训练会话"
        functionName.startsWith("gymbro.template.generate") -> "制作训练模板"
        functionName.startsWith("gymbro.nutrition.advice") -> "饮食建议"
        functionName.startsWith("gymbro.training.analyze") -> "训练分析"
        else -> "工具执行"
    }
}

/**
 * 获取操作按钮文本
 */
private fun getActionButtonText(result: AiCoachContract.FunctionCallResult): String {
    return when {
        result.functionName.startsWith("gymbro.plan.search") -> "使用此计划"
        result.functionName.startsWith("gymbro.plan.generate") -> "查看详情"
        result.functionName.startsWith("gymbro.exercise.search") -> "添加动作"
        result.functionName.startsWith("gymbro.session.start") -> "继续训练"
        else -> "使用结果"
    }
}

/**
 * 生成操作文本，用于回填到输入框
 */
private fun generateActionText(result: AiCoachContract.FunctionCallResult): String {
    return when {
        result.functionName.startsWith("gymbro.plan.search") -> "帮我开始执行这个训练计划"
        result.functionName.startsWith("gymbro.plan.generate") -> "请详细介绍这个训练计划的内容"
        result.functionName.startsWith("gymbro.exercise.search") -> "请告诉我这些动作的详细做法"
        result.functionName.startsWith("gymbro.session.start") -> "继续指导我的训练"
        else -> "请继续"
    }
}

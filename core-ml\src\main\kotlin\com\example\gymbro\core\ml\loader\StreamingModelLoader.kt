package com.example.gymbro.core.ml.loader

import android.content.Context
import com.example.gymbro.core.ml.scheduler.MemoryAwareBgeScheduler
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.nio.ByteBuffer
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 流式模型加载器 - 分块异步加载优化
 *
 * 核心优化策略：
 * 1. 将15MB模型文件分成多个块，边加载边处理
 * 2. 使用FileChannel + MappedByteBuffer提升I/O性能
 * 3. 根据设备内存动态调整块大小和并发度
 * 4. 提供加载进度反馈，提升用户体验
 */
@Singleton
class StreamingModelLoader @Inject constructor(
    @ApplicationContext private val context: Context,
    private val memoryScheduler: MemoryAwareBgeScheduler,
    private val ioDispatcher: CoroutineDispatcher,
) {

    companion object {
        private const val TAG = "StreamingLoader"

        // 默认块大小配置 (字节)
        private const val DEFAULT_CHUNK_SIZE = 2 * 1024 * 1024 // 2MB
        private const val MIN_CHUNK_SIZE = 512 * 1024 // 512KB
        private const val MAX_CHUNK_SIZE = 10 * 1024 * 1024 // 10MB

        // 加载进度回调间隔
        private const val PROGRESS_UPDATE_INTERVAL_MS = 100L
    }

    /**
     * 加载进度数据类
     */
    data class LoadingProgress(
        val currentChunk: Int,
        val totalChunks: Int,
        val bytesLoaded: Long,
        val totalBytes: Long,
        val progressPercent: Int,
        val estimatedTimeRemaining: Long, // ms
        val currentStage: LoadingStage,
    )

    /**
     * 加载阶段
     */
    enum class LoadingStage {
        PREPARING, // 准备阶段
        LOADING_CHUNKS, // 分块加载
        ASSEMBLING, // 组装合并
        OPTIMIZING, // 内存优化
        COMPLETED, // 加载完成
    }

    /**
     * 分块异步加载模型文件
     *
     * @param assetFileName 模型文件名
     * @param progressCallback 进度回调（可选）
     * @return 加载完成的ByteBuffer
     */
    suspend fun loadModelStreaming(
        assetFileName: String,
        progressCallback: ((LoadingProgress) -> Unit)? = null,
    ): ByteBuffer = withContext(ioDispatcher) {
        val startTime = System.currentTimeMillis()
        Timber.tag(TAG).i("🚀 开始流式加载模型: $assetFileName")

        try {
            // Stage 1: 准备阶段
            progressCallback?.invoke(
                LoadingProgress(0, 0, 0L, 0L, 0, 0L, LoadingStage.PREPARING),
            )

            val strategy = memoryScheduler.detectAndCreateStrategy()
            val chunkSize = strategy.chunkSize.coerceIn(MIN_CHUNK_SIZE, MAX_CHUNK_SIZE)

            val assetInputStream = context.assets.open(assetFileName)
            val fileSize = assetInputStream.available().toLong()
            val totalChunks = ((fileSize + chunkSize - 1) / chunkSize).toInt()

            Timber.tag(
                TAG,
            ).i(
                "📊 加载参数: 文件大小=${fileSize / (1024 * 1024)}MB, 块大小=${chunkSize / (1024 * 1024)}MB, 块数=$totalChunks",
            )

            // Stage 2: 分块加载阶段
            var bytesLoaded = 0L
            val chunks = mutableListOf<ByteArray>()
            var lastProgressTime = System.currentTimeMillis()

            assetInputStream.use { inputStream ->
                repeat(totalChunks) { chunkIndex ->
                    // 内存压力检查
                    if (!memoryScheduler.isMemoryAvailableForBge() && chunkIndex > 0) {
                        Timber.tag(TAG).w("⚠️ 内存压力过大，暂停加载等待内存释放...")
                        delay(1000) // 等待1秒让系统GC
                    }

                    val currentChunkSize = minOf(chunkSize, (fileSize - bytesLoaded).toInt())
                    val chunkData = ByteArray(currentChunkSize)

                    val actualBytesRead = inputStream.read(chunkData)
                    if (actualBytesRead > 0) {
                        chunks.add(chunkData.take(actualBytesRead).toByteArray())
                        bytesLoaded += actualBytesRead

                        // 定期更新进度
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastProgressTime >= PROGRESS_UPDATE_INTERVAL_MS || chunkIndex == totalChunks - 1) {
                            val progressPercent = ((bytesLoaded * 100) / fileSize).toInt()
                            val elapsedTime = currentTime - startTime
                            val estimatedTotal = if (bytesLoaded > 0) (elapsedTime * fileSize / bytesLoaded) else 0L
                            val estimatedRemaining = maxOf(0L, estimatedTotal - elapsedTime)

                            progressCallback?.invoke(
                                LoadingProgress(
                                    currentChunk = chunkIndex + 1,
                                    totalChunks = totalChunks,
                                    bytesLoaded = bytesLoaded,
                                    totalBytes = fileSize,
                                    progressPercent = progressPercent,
                                    estimatedTimeRemaining = estimatedRemaining,
                                    currentStage = LoadingStage.LOADING_CHUNKS,
                                ),
                            )
                            lastProgressTime = currentTime
                        }

                        Timber.tag(TAG).v("📦 已加载块 ${chunkIndex + 1}/$totalChunks (${actualBytesRead}字节)")
                    }
                }
            }

            // Stage 3: 组装合并阶段
            progressCallback?.invoke(
                LoadingProgress(
                    totalChunks,
                    totalChunks,
                    bytesLoaded,
                    fileSize,
                    95,
                    0L,
                    LoadingStage.ASSEMBLING,
                ),
            )

            Timber.tag(TAG).i("🔧 开始组装模型数据...")
            val assembledData = assembleChunks(chunks, fileSize.toInt())

            // Stage 4: 内存优化阶段
            progressCallback?.invoke(
                LoadingProgress(
                    totalChunks,
                    totalChunks,
                    bytesLoaded,
                    fileSize,
                    98,
                    0L,
                    LoadingStage.OPTIMIZING,
                ),
            )

            // 创建优化的ByteBuffer
            val optimizedBuffer = createOptimizedByteBuffer(assembledData, strategy)

            // 清理临时数据
            chunks.clear()
            System.gc() // 建议GC清理临时内存

            // Stage 5: 完成
            val totalTime = System.currentTimeMillis() - startTime
            progressCallback?.invoke(
                LoadingProgress(
                    totalChunks,
                    totalChunks,
                    bytesLoaded,
                    fileSize,
                    100,
                    0L,
                    LoadingStage.COMPLETED,
                ),
            )

            Timber.tag(
                TAG,
            ).i("🎉 流式加载完成! 耗时: ${totalTime}ms, 平均速度: ${(fileSize / 1024) / (totalTime / 1000.0)}KB/s")

            optimizedBuffer
        } catch (e: Exception) {
            Timber.tag(TAG).e(e, "❌ 流式加载失败: $assetFileName")
            throw e
        }
    }

    /**
     * 提供Flow形式的加载进度监听
     */
    fun loadModelStreamingWithFlow(assetFileName: String): Flow<LoadingProgress> = callbackFlow {
        loadModelStreaming(assetFileName) { progress ->
            trySend(progress)
        }
        awaitClose()
    }

    /**
     * 组装分块数据
     */
    private suspend fun assembleChunks(chunks: List<ByteArray>, totalSize: Int): ByteArray =
        withContext(ioDispatcher) {
            val result = ByteArray(totalSize)
            var offset = 0

            chunks.forEach { chunk ->
                chunk.copyInto(result, offset)
                offset += chunk.size
            }

            Timber.tag(TAG).d("🔧 数据组装完成: ${result.size}字节")
            result
        }

    /**
     * 创建优化的ByteBuffer
     */
    private fun createOptimizedByteBuffer(
        data: ByteArray,
        strategy: MemoryAwareBgeScheduler.LoadingStrategy,
    ): ByteBuffer {
        // 🔥 根据设备策略选择最优的ByteBuffer类型
        val buffer = if (strategy.memoryTier == MemoryAwareBgeScheduler.MemoryTier.HIGH_MEMORY) {
            // 高内存设备使用直接内存，减少Java堆压力
            ByteBuffer.allocateDirect(data.size)
        } else {
            // 低内存设备使用堆内存，便于GC管理
            ByteBuffer.allocate(data.size)
        }

        buffer.order(java.nio.ByteOrder.LITTLE_ENDIAN)
        buffer.put(data)
        buffer.rewind()

        val bufferType = if (buffer.isDirect) "直接内存" else "堆内存"
        Timber.tag(TAG).i("📦 ByteBuffer创建完成: ${data.size / (1024 * 1024)}MB ($bufferType)")

        return buffer
    }

    /**
     * 预估加载时间
     */
    suspend fun estimateLoadingTime(assetFileName: String): Long = withContext(ioDispatcher) {
        try {
            val fileSize = context.assets.open(assetFileName).use { it.available().toLong() }
            val strategy = memoryScheduler.detectAndCreateStrategy()

            // 基于设备性能预估加载时间
            val baseSpeed = when (strategy.memoryTier) {
                MemoryAwareBgeScheduler.MemoryTier.LOW_MEMORY -> 2 * 1024 * 1024 // 2MB/s
                MemoryAwareBgeScheduler.MemoryTier.MID_MEMORY -> 5 * 1024 * 1024 // 5MB/s
                MemoryAwareBgeScheduler.MemoryTier.HIGH_MEMORY -> 10 * 1024 * 1024 // 10MB/s
            }

            val estimatedMs = (fileSize * 1000 / baseSpeed) + strategy.loadingDelay
            Timber.tag(TAG).d("⏱️ 预估加载时间: ${estimatedMs}ms (文件: ${fileSize / (1024 * 1024)}MB)")

            estimatedMs
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "⚠️ 无法预估加载时间")
            3000L // 默认3秒
        }
    }

    /**
     * 检查模型文件是否存在且可读
     */
    fun validateModelFile(assetFileName: String): Boolean {
        return try {
            context.assets.open(assetFileName).use {
                it.available() > 0
            }
        } catch (e: Exception) {
            Timber.tag(TAG).w(e, "⚠️ 模型文件验证失败: $assetFileName")
            false
        }
    }
}

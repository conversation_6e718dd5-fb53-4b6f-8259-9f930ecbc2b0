# GymBro AI动作库JSON存储规范

## 📋 概述

GymBro项目AI专用动作库JSON存储系统，为Function Call和AI对话提供高效、准确的动作数据检索服务。

基于**Claude 4.0 sonnet**设计的"元数据 + JSON"混合模式，实现精准的AI动作推荐和识别。

## 🏗️ 文件结构

```
assets/ai/
├── README.md                          # 本文档
├── exercise_library_official.json     # 官方动作库
├── exercise_library_custom.json       # 用户自定义动作库
├── library_meta.json                  # 元数据信息
└── schemas/                           # JSON Schema定义
    ├── ai_exercise_reference.schema.json
    └── library_wrapper.schema.json
```

## 📊 JSON格式规范

### 1. 统一表头格式 (EntityWrapper)

所有JSON文件都遵循GymBro项目的EntityWrapper规范：

```json
{
  "schemaVersion": "1.0.0",
  "entity": "EXERCISE",
  "entityVersion": 1,
  "generatedAt": 1718716800000,
  "payload": [...]
}
```

### 2. 官方动作库格式 (exercise_library_official.json)

```json
{
  "schemaVersion": "1.0.0",
  "entity": "EXERCISE",
  "entityVersion": 1,
  "generatedAt": 1718716800000,
  "payload": [
    {
      "id": "off_bench_press_001",
      "name": {
        "zh": "杠铃卧推",
        "en": "Barbell Bench Press"
      },
      "muscleGroup": "CHEST",
      "equipment": ["BARBELL"],
      "difficulty": "INTERMEDIATE",
      "source": "OFFICIAL",
      "category": "UPPER_BODY",
      "targetMuscles": ["CHEST"],
      "secondaryMuscles": ["SHOULDERS", "ARMS"],
      "contentHash": "abc123def456",
      "updatedAt": 1718716800000
    }
  ]
}
```

### 3. 用户自定义动作库格式 (exercise_library_custom.json)

```json
{
  "schemaVersion": "1.0.0",
  "entity": "EXERCISE",
  "entityVersion": 1,
  "generatedAt": 1718716800000,
  "payload": [
    {
      "id": "u_user123_abc456",
      "name": {
        "zh": "我的特殊卧推",
        "en": "My Special Bench Press"
      },
      "muscleGroup": "CHEST",
      "equipment": ["DUMBBELL"],
      "difficulty": "ADVANCED",
      "source": "CUSTOM",
      "category": "UPPER_BODY",
      "targetMuscles": ["CHEST"],
      "secondaryMuscles": ["SHOULDERS"],
      "contentHash": "def456ghi789",
      "updatedAt": 1718716900000
    }
  ]
}
```

### 4. 元数据文件格式 (library_meta.json)

```json
{
  "lastUpdateHash": "abc123def456ghi789",
  "lastUpdateTime": 1718716800000,
  "officialExerciseCount": 150,
  "customExerciseCount": 25,
  "totalExerciseCount": 175,
  "schemaVersion": "1.0.0",
  "extractorVersion": "1.0.0",
  "statistics": {
    "muscleGroupDistribution": {
      "CHEST": 20,
      "BACK": 18,
      "LEGS": 25,
      "ARMS": 15,
      "SHOULDERS": 12,
      "CORE": 10,
      "OTHER": 5
    },
    "equipmentDistribution": {
      "BARBELL": 35,
      "DUMBBELL": 40,
      "MACHINE": 25,
      "NONE": 15,
      "OTHER": 10
    },
    "difficultyDistribution": {
      "BEGINNER": 60,
      "INTERMEDIATE": 80,
      "ADVANCED": 35
    }
  }
}
```

## 🔧 数据字段说明

### AiExerciseReference字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `id` | String | ✅ | 动作唯一标识符 |
| `name` | Map<String, String> | ✅ | 多语言名称映射 |
| `muscleGroup` | MuscleGroup | ✅ | 主要训练肌肉群 |
| `equipment` | List<Equipment> | ✅ | 使用的器械类型 |
| `difficulty` | DifficultyLevel | ✅ | 难度等级 |
| `source` | ExerciseSource | ✅ | 动作来源 |
| `category` | ExerciseCategory | ✅ | 动作分类 |
| `targetMuscles` | List<MuscleGroup> | ❌ | 目标肌肉群 |
| `secondaryMuscles` | List<MuscleGroup> | ❌ | 次要肌肉群 |
| `contentHash` | String | ✅ | 内容哈希值 |
| `updatedAt` | Long | ✅ | 更新时间戳 |

### 枚举值定义

**MuscleGroup (肌肉群)**:
- `CHEST` - 胸部
- `BACK` - 背部  
- `SHOULDERS` - 肩部
- `ARMS` - 手臂
- `LEGS` - 腿部
- `CORE` - 核心
- `CARDIO` - 有氧
- `FULL_BODY` - 全身

**Equipment (器械)**:
- `NONE` - 无器械
- `BARBELL` - 杠铃
- `DUMBBELL` - 哑铃
- `KETTLEBELL` - 壶铃
- `CABLE` - 拉索
- `MACHINE` - 器械

**DifficultyLevel (难度)**:
- `BEGINNER` - 初级
- `INTERMEDIATE` - 中级
- `ADVANCED` - 高级

**ExerciseSource (来源)**:
- `OFFICIAL` - 官方
- `CUSTOM` - 用户自定义

**ExerciseCategory (分类)**:
- `UPPER_BODY` - 上半身
- `LOWER_BODY` - 下半身
- `CORE` - 核心
- `FULL_BODY` - 全身
- `CARDIO` - 有氧

## 🔄 更新机制

### 增量更新流程

1. **变化检测**: 通过`contentHash`检测动作库数据变化
2. **数据分离**: 将官方和用户自定义动作分别处理
3. **JSON生成**: 生成符合EntityWrapper规范的JSON文件
4. **元数据更新**: 更新统计信息和版本信息
5. **哈希保存**: 保存新的内容哈希，用于下次比较

### 触发条件

- 新增动作
- 修改动作信息
- 删除动作
- 用户自定义动作变更
- 手动强制更新

## 🎯 Function Call集成

### 搜索接口返回格式

```json
{
  "exercises": [
    {
      "id": "off_bench_press_001",
      "name": "杠铃卧推",
      "nameEn": "Barbell Bench Press",
      "muscleGroup": "CHEST",
      "equipment": ["BARBELL"],
      "difficulty": "INTERMEDIATE",
      "source": "OFFICIAL"
    }
  ],
  "source": "function_call",
  "query": "练胸",
  "resultCount": 1
}
```

## 📈 性能优化

### 文件大小控制

- 官方动作库: ~50KB (约150条动作)
- 用户自定义: ~10KB (约25条动作)
- 元数据文件: ~5KB
- 总计: ~65KB

### 加载策略

- 按需加载: 只加载需要的动作库文件
- 缓存机制: 内存缓存热点数据
- 增量更新: 只更新变化的部分

## 🔒 版本控制

### Schema版本管理

- `schemaVersion`: 全局架构版本 (1.0.0)
- `entityVersion`: 实体版本 (1)
- `extractorVersion`: 提取器版本 (1.0.0)

### 兼容性策略

- 向后兼容: 新版本支持旧格式
- 渐进升级: 逐步迁移到新格式
- 错误处理: 格式错误时的降级策略

---

*此文档随着P6阶段动作库Function Call链路的实施持续更新*

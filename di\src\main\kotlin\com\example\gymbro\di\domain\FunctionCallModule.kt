package com.example.gymbro.di.domain

import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

/**
 * Function Call模块依赖注入配置
 *
 * 负责提供Function Call执行器及其相关依赖的DI配置
 * 确保FunctionCallExecutor及其依赖项能够被正确注入
 *
 * 包含的组件：
 * - FunctionCallExecutor: Function Call统一执行器
 * - FunctionCallRouter: Function Call路由器
 * - FunctionCallValidator: Function Call验证器
 * - MinimalFunctionSet: 最小化Function集合
 * - 各个域模块: ExerciseFunctionModule, TemplateFunctionModule等
 *
 * 注意：所有组件都使用@Inject constructor和@Singleton注解，
 * 由Hilt自动管理，这里不需要手动提供
 *
 * <AUTHOR> 4.0 sonnet
 * @since 1.0.0 (Function Call Debug功能修复)
 */
@Module
@InstallIn(SingletonComponent::class)
object FunctionCallModule {

    /**
     * 注意：以下组件都使用@Inject constructor，由Hilt自动管理：
     *
     * - FunctionCallExecutor: 统一Function Call执行器
     * - FunctionCallRouter: Function Call路由器
     * - FunctionCallValidator: Function Call验证器
     * - MinimalFunctionSet: 最小化Function集合
     * - ExerciseFunctionModule: 动作库域模块
     * - TemplateFunctionModule: 训练模板域模块
     * - PlanFunctionModule: 训练计划域模块
     * - SessionFunctionModule: 训练会话域模块
     * - CalendarFunctionModule: 日历域模块
     *
     * 这些组件不需要手动提供，Hilt会自动处理依赖注入
     */
}

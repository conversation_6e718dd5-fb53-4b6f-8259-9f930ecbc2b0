package com.example.gymbro.di.core.userdata

import com.example.gymbro.core.userdata.internal.dao.UserDataDao
import com.example.gymbro.core.userdata.internal.datasource.UserLocalDataSource
import com.example.gymbro.core.userdata.internal.di.DefaultUserDataSource
import com.example.gymbro.core.userdata.internal.di.RoomUserDataSource
import dagger.Binds
import dagger.Module
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton
import com.example.gymbro.core.userdata.internal.dao.UserProfileDao as CoreUserProfileDao

/**
 * UserDataCenter 在 di 模块中的依赖注入配置
 *
 * 这个模块负责将 data 模块的具体 DAO 实现绑定到 core-user-data-center 的抽象接口，
 * 解决循环依赖问题的同时提供真正的 Room 数据库持久化。
 *
 * 架构设计：
 * - data 模块提供具体的 Room DAO 实现
 * - core-user-data-center 定义抽象的 DAO 接口
 * - di 模块通过适配器模式连接两者
 * - 避免了 core-user-data-center → data 的直接依赖
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class UserDataCenterDiModule {

    /**
     * 绑定 UserDataDao 抽象接口到适配器实现
     *
     * 通过适配器模式将 data 模块的 UserDao 适配到 core-user-data-center 的接口。
     *
     * @param adapter UserDataDaoAdapter 适配器实现
     * @return UserDataDao 抽象接口
     */
    @Binds
    @Singleton
    internal abstract fun bindUserDataDao(
        adapter: UserDataDaoAdapter,
    ): UserDataDao

    /**
     * 绑定 UserProfileDao 抽象接口到适配器实现
     *
     * 通过适配器模式将 data 模块的 UserProfileDao 适配到 core-user-data-center 的接口。
     *
     * @param adapter UserProfileDaoAdapter 适配器实现
     * @return CoreUserProfileDao 抽象接口
     */
    @Binds
    @Singleton
    internal abstract fun bindUserProfileDao(
        adapter: UserProfileDaoAdapter,
    ): CoreUserProfileDao

    /**
     * 绑定 UserLocalDataSource 到 Room 实现
     *
     * 提供基于真正 Room 数据库的 UserLocalDataSource 实现。
     * 使用 @RoomUserDataSource 限定符避免与简化实现冲突。
     *
     * @param roomImpl UserLocalDataSourceRoomImpl Room 实现
     * @return UserLocalDataSource 接口
     */
    @Binds
    @Singleton
    @RoomUserDataSource
    internal abstract fun bindRoomUserLocalDataSource(
        roomImpl: UserLocalDataSourceRoomImpl,
    ): UserLocalDataSource

    /**
     * 绑定默认的 UserLocalDataSource 实现
     *
     * 将 Room 实现设置为默认实现，这是应用程序中实际使用的实现。
     * 使用 @DefaultUserDataSource 限定符标识默认实现。
     *
     * @param roomImpl UserLocalDataSourceRoomImpl Room 实现
     * @return UserLocalDataSource 接口
     */
    @Binds
    @Singleton
    @DefaultUserDataSource
    internal abstract fun bindDefaultUserLocalDataSource(
        roomImpl: UserLocalDataSourceRoomImpl,
    ): UserLocalDataSource
}

/**
 * 使用说明：
 *
 * 1. **依赖关系**：
 *    - di 模块依赖 data 模块（获取具体 DAO）
 *    - di 模块依赖 core-user-data-center 模块（实现抽象接口）
 *    - core-user-data-center 不依赖 data 模块（避免循环依赖）
 *
 * 2. **工作原理**：
 *    - UserDataDaoAdapter 将 data.UserDao 适配为 core.UserDataDao
 *    - UserProfileDaoAdapter 将 data.UserProfileDao 适配为 core.UserProfileDao
 *    - UserLocalDataSourceRoomImpl 使用适配后的接口提供真正的持久化
 *
 * 3. **优先级**：
 *    - di 模块的绑定会覆盖 core-user-data-center 模块中的简化实现
 *    - 应用运行时会使用真正的 Room 数据库持久化
 *
 * 4. **扩展性**：
 *    - 可以轻松添加其他数据源（如远程 API）
 *    - 可以通过 @Qualifier 提供多种实现
 *    - 便于单元测试时提供 Mock 实现
 */

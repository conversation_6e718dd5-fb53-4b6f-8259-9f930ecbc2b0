package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.workout.model.WorkoutSession
import com.example.gymbro.domain.workout.repository.SessionRepository
import io.mockk.MockKAnnotations
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertTrue

/**
 * EndWorkoutSessionUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试UseCase的核心业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证ModernResult返回类型
 * ✔️ 测试错误处理和边界情况
 */
class EndWorkoutSessionUseCaseTest {

    @MockK
    private lateinit var sessionRepository: SessionRepository

    @MockK
    private lateinit var logger: Logger

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: EndWorkoutSessionUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = EndWorkoutSessionUseCase(
            sessionRepository = sessionRepository,
            dispatcher = testDispatcher,
            logger = logger,
        )
    }

    @Test
    fun `execute with FromSession should end session successfully`() = runTest(testDispatcher) {
        // Given
        val session = createActiveWorkoutSession()
        val params = EndWorkoutSessionUseCase.Params.FromSession(session)

        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with FromSession should return error when session already completed`() = runTest(
        testDispatcher,
    ) {
        // Given
        val completedSession = createWorkoutSession(status = "COMPLETED")
        val params = EndWorkoutSessionUseCase.Params.FromSession(completedSession)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with FromSession should return error when session already aborted`() = runTest(
        testDispatcher,
    ) {
        // Given
        val abortedSession = createWorkoutSession(status = "ABORTED")
        val params = EndWorkoutSessionUseCase.Params.FromSession(abortedSession)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with FromId should end existing session successfully`() = runTest(testDispatcher) {
        // Given
        val sessionId = "session-123"
        val activeSession = createActiveWorkoutSession(id = sessionId)
        val params = EndWorkoutSessionUseCase.Params.FromId(sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(activeSession)
        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with FromId should return error when session not found`() = runTest(testDispatcher) {
        // Given
        val sessionId = "nonexistent-session"
        val params = EndWorkoutSessionUseCase.Params.FromId(sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(null)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is DataErrors.DataError)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with FromId should return error when session already completed`() = runTest(testDispatcher) {
        // Given
        val sessionId = "completed-session"
        val completedSession = createWorkoutSession(id = sessionId, status = "COMPLETED")
        val params = EndWorkoutSessionUseCase.Params.FromId(sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(completedSession)

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `execute with FromId should return error when repository returns error`() = runTest(testDispatcher) {
        // Given
        val sessionId = "error-session"
        val params = EndWorkoutSessionUseCase.Params.FromId(sessionId)

        coEvery {
            sessionRepository.getSessionById(sessionId)
        } returns ModernResult.Error(mockk<DataErrors.DataError>())

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 0) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `invoke with WorkoutSession should delegate to execute with FromSession`() = runTest(testDispatcher) {
        // Given
        val session = createActiveWorkoutSession()

        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(session)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `invoke with sessionId should delegate to execute with FromId`() = runTest(testDispatcher) {
        // Given
        val sessionId = "session-123"
        val activeSession = createActiveWorkoutSession(id = sessionId)

        coEvery { sessionRepository.getSessionById(sessionId) } returns ModernResult.Success(activeSession)
        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Success(Unit)

        // When
        val result = useCase.invoke(sessionId)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify(exactly = 1) { sessionRepository.getSessionById(sessionId) }
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    @Test
    fun `should handle repository save error gracefully`() = runTest(testDispatcher) {
        // Given
        val session = createActiveWorkoutSession()
        val params = EndWorkoutSessionUseCase.Params.FromSession(session)

        coEvery { sessionRepository.saveSession(any()) } returns ModernResult.Error(mockk<DataErrors.DataError>())

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        coVerify(exactly = 1) { sessionRepository.saveSession(any()) }
    }

    // === 辅助方法 ===

    private fun createWorkoutSession(
        id: String = "session-123",
        status: String = "DRAFT",
    ): WorkoutSession {
        return WorkoutSession(
            id = id,
            name = "测试训练会话",
            templateId = null,
            status = status,
            startTime = if (status == "IN_PROGRESS") System.currentTimeMillis() else null,
            endTime = if (status == "COMPLETED") System.currentTimeMillis() else null,
            exercises = emptyList(),
            createdAt = System.currentTimeMillis(),
            updatedAt = System.currentTimeMillis(),
        )
    }

    private fun createActiveWorkoutSession(id: String = "session-123"): WorkoutSession {
        return createWorkoutSession(id = id, status = "IN_PROGRESS")
    }
}
package com.example.gymbro

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navOptions
import com.example.gymbro.app.loading.LoadingScreen
import com.example.gymbro.core.theme.LocalThemeManager
import com.example.gymbro.core.theme.ThemeConfig
import com.example.gymbro.core.theme.ThemeManager
import com.example.gymbro.data.local.datastore.UserPreferencesRepository
import com.example.gymbro.designSystem.components.animations.GymBroPageTransitions
import com.example.gymbro.designSystem.overlay.GlobalOverlayHost
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.domain.coach.config.AiProviderManager
import com.example.gymbro.features.auth.navigation.AuthRoutes
import com.example.gymbro.features.auth.navigation.authNavGraph
import com.example.gymbro.features.coach.navigation.coachGraph
import com.example.gymbro.features.coach.navigation.navigateToCoach
import com.example.gymbro.features.exerciselibrary.api.ExerciseLibraryNavigatable
import com.example.gymbro.features.home.navigation.homeGraph
import com.example.gymbro.features.profile.api.ProfileNavigatable
import com.example.gymbro.features.subscription.presentation.navigation.subscriptionNavigation
import com.example.gymbro.features.workout.navigation.workoutGraph
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

/**
 * 主Activity
 * 应用程序的入口点，负责设置主题、状态栏和导航
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    // 注入用户偏好仓库
    @Inject
    lateinit var userPreferencesRepository: UserPreferencesRepository

    // 注入主题管理器
    @Inject
    lateinit var themeManager: ThemeManager

    // 注入AI提供商管理器，用于主应用启动后的后台初始化
    @Inject
    lateinit var aiProviderManager: AiProviderManager

    // 注入动作库导航模块
    @Inject
    lateinit var exerciseLibraryNavigatable: ExerciseLibraryNavigatable

    // 注入个人资料导航模块
    @Inject
    lateinit var profileNavigatable: ProfileNavigatable

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 启用边到边显示
        enableEdgeToEdge()

        setContent {
            // 显示Loading页面，然后进行后台地区检测，最后跳转到主应用
            GymBroAppWithRegionDetection(
                userPreferencesRepository = userPreferencesRepository,
                themeManager = themeManager,
                aiProviderManager = aiProviderManager,
                exerciseLibraryNavigatable = exerciseLibraryNavigatable,
                profileNavigatable = profileNavigatable,
            )
        }
    }
}

/**
 * 带地区检测的主应用入口
 * 流程：Loading -> 后台地区检测 -> 主应用
 */
@Composable
fun GymBroAppWithRegionDetection(
    userPreferencesRepository: UserPreferencesRepository,
    themeManager: ThemeManager,
    aiProviderManager: AiProviderManager,
    exerciseLibraryNavigatable: ExerciseLibraryNavigatable,
    profileNavigatable: ProfileNavigatable,
) {
    var appState by remember { mutableStateOf(AppState.LOADING) }
    val coroutineScope = rememberCoroutineScope()

    when (appState) {
        AppState.LOADING -> {
            // 显示Loading页面，带有彩虹色跃动的"G"
            LoadingScreen(
                onLoadingFinished = {
                    // Loading完成后，启动后台地区检测并直接进入主应用
                    appState = AppState.MAIN_APP
                },
            )
        }

        AppState.MAIN_APP -> {
            // 显示主应用，地区检测在后台进行
            GymBroApp(
                userPreferencesRepository = userPreferencesRepository,
                themeManager = themeManager,
                aiProviderManager = aiProviderManager,
                exerciseLibraryNavigatable = exerciseLibraryNavigatable,
                profileNavigatable = profileNavigatable,
                startDestination = AuthRoutes.AUTH_GRAPH,
            )
        }
    }
}

/**
 * 主应用Composable，使用ThemeManager管理主题状态
 */
@Composable
fun GymBroApp(
    userPreferencesRepository: UserPreferencesRepository,
    themeManager: ThemeManager,
    aiProviderManager: AiProviderManager,
    exerciseLibraryNavigatable: ExerciseLibraryNavigatable,
    profileNavigatable: ProfileNavigatable,
    startDestination: String = AuthRoutes.AUTH_GRAPH,
) {
    // 使用动态主题系统
    val themeConfig by themeManager.themeConfig.collectAsStateWithLifecycle(
        initialValue = ThemeConfig(),
    )

    // 协程作用域用于后台任务
    val coroutineScope = rememberCoroutineScope()

    // 🔥 【架构修正】移除重复的AI提供商初始化代码
    // AI提供商的初始化现在由AppStartupManager统一管理
    // MainActivity只负责UI层面的初始化，不再管理业务组件的生命周期

    // 记录主题变化用于调试
    LaunchedEffect(themeConfig) {
        Timber.d("主题配置变化: $themeConfig")
    }

    // 提供LocalThemeManager给整个应用
    CompositionLocalProvider(LocalThemeManager provides themeManager) {
        GymBroTheme(themeConfig = themeConfig) {
            Surface(
                modifier = Modifier.fillMaxSize(),
            ) {
                // 全局覆盖层宿主 - 在导航树外部挂载，确保跨页面展示倒计时
                GlobalOverlayHost()

                GymBroNavHost(
                    startDestination = startDestination,
                    exerciseLibraryNavigatable = exerciseLibraryNavigatable,
                    profileNavigatable = profileNavigatable,
                )
            }
        }
    }
}

/**
 * 应用程序主导航图
 */
@Composable
fun GymBroNavHost(
    navController: NavHostController = rememberNavController(),
    startDestination: String = AuthRoutes.AUTH_GRAPH,
    exerciseLibraryNavigatable: ExerciseLibraryNavigatable,
    profileNavigatable: ProfileNavigatable,
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,
        enterTransition = GymBroPageTransitions.ChatGPTStyle.enterTransition,
        exitTransition = GymBroPageTransitions.ChatGPTStyle.exitTransition,
        popEnterTransition = GymBroPageTransitions.ChatGPTStyle.popEnterTransition,
        popExitTransition = GymBroPageTransitions.ChatGPTStyle.popExitTransition,
    ) {
        // 认证导航图
        authNavGraph(
            navController = navController,
            onAuthenticated = {
                // 🔥 修改：导航到AI教练模块作为默认首页
                navController.navigateToCoach(
                    navOptions =
                    navOptions {
                        popUpTo(AuthRoutes.AUTH_GRAPH) { inclusive = true }
                    },
                )
            },
        )

        // 主页导航图
        homeGraph(
            navController = navController,
            exerciseLibraryNavigatable = exerciseLibraryNavigatable,
        )

        // 动作库导航图（独立注册）
        exerciseLibraryNavigatable.registerGraph(
            navGraphBuilder = this,
            navController = navController,
        )
        Timber.d("MainActivity: 已注册动作库导航图")

        /**
         * 订阅导航图
         * 使用features/subscription/presentation/navigation包中的subscriptionNavigation函数
         * 此函数将注册所有订阅相关的路由和目标屏幕，包括SubscriptionScreen.kt
         */
        subscriptionNavigation(navController = navController)
        Timber.d("MainActivity: 已注册订阅导航图")

        // 训练导航图
        workoutGraph(
            navController = navController,
            exerciseLibraryNavigatable = exerciseLibraryNavigatable,
        )
        Timber.d("MainActivity: 已注册训练导航图")

        // AI教练导航图
        coachGraph(
            navController = navController,
            exerciseLibraryNavigatable = exerciseLibraryNavigatable,
        )
        Timber.d("MainActivity: 已注册AI教练导航图")

        // 个人资料导航图
        profileNavigatable.registerGraph(
            navGraphBuilder = this,
            navController = navController,
            onNavigateToAuth = {
                // 退出登录，返回认证页面
                navController.navigate(AuthRoutes.AUTH_GRAPH) {
                    popUpTo(0) { inclusive = true }
                }
            },
        )
        Timber.d("MainActivity: 已注册个人资料导航图")
    }

    Timber.d("MainActivity: 导航图初始化完成")
}

/**
 * 应用状态枚举
 */
private enum class AppState {
    LOADING, // 加载中
    MAIN_APP, // 主应用（地区检测在后台进行）
}

# ThinkingBox 责任边界分析

## 当前问题
Phase 创建和结束事件不匹配，导致双握手机制失效：
- StreamingThinkingMLParser 发送 PhaseStart 事件
- DomainMapper 忽略 TagOpened("phase") 但处理 TagClosed("phase")
- 结果：PhaseEnd 事件找不到对应的 phase

## 责任边界重新设计

### StreamingThinkingMLParser 职责
- **XML 解析**：将 token 流解析为结构化的 XML 标签
- **状态机管理**：维护 ParserState (PRE_THINK → THINKING → POST_FINAL)
- **基础事件生成**：生成 SemanticEvent (TagOpened/TagClosed/Text)
- **边界**：只负责语法解析，不涉及业务逻辑

### DomainMapper 职责  
- **语义映射**：将 SemanticEvent 映射为 ThinkingEvent
- **业务逻辑**：Phase 生命周期管理、状态转换逻辑
- **上下文管理**：维护 MappingContext，跟踪当前状态
- **边界**：负责所有业务规则和状态管理

## 修复原则
1. **单一职责**：每个组件只负责自己的核心功能
2. **事件一致性**：确保 PhaseStart 和 PhaseEnd 事件配对
3. **状态同步**：Parser 和 Mapper 的状态保持一致
package com.example.gymbro.features.coach.aicoach.internal.viewmodel

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.coach.usecase.ChatSessionManagementUseCase
import com.example.gymbro.domain.coach.usecase.GenerateChatTitleUseCase
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * AI Coach 会话管理处理器
 *
 * 负责处理会话的创建、加载、消息保存等操作
 * 遵循 Clean Architecture + MVI 2.0 的设计原则
 *
 * 🎯 架构迁移：移除 HistoryPersister 依赖，直接使用 ChatSessionManagementUseCase
 * ChatRaw 作为单一数据源，所有历史记录操作通过 UseCase 统一管理
 */
internal class AiCoachSessionHandler
@Inject
constructor(
    private val chatSessionManagementUseCase: ChatSessionManagementUseCase,
    private val generateChatTitleUseCase: GenerateChatTitleUseCase,
) {
    // 🔥 延迟初始化的 delegate
    private lateinit var delegate: AiCoachViewModelDelegate

    /**
     * 初始化会话处理器
     */
    fun initialize(delegate: AiCoachViewModelDelegate) {
        this.delegate = delegate
    }

    // ================================
    // 便捷方法：访问 delegate 功能
    // ================================

    private fun dispatch(intent: AiCoachContract.Intent) {
        delegate.dispatch(intent)
    }

    private val state: AiCoachContract.State
        get() = delegate.getCurrentState()

    private val resourceProvider: com.example.gymbro.core.resources.ResourceProvider
        get() = delegate.getResourceProvider()

    private val scope: kotlinx.coroutines.CoroutineScope
        get() = delegate.getViewModelScope()

    private fun logDebug(message: String) {
        Timber.d("🔥 ${this::class.simpleName}: $message")
    }

    private fun logError(
        throwable: Throwable,
        message: String,
    ) {
        Timber.e(throwable, "❌ ${this::class.simpleName}: $message")
    }

    // 🔥 新增：会话创建状态管理
    private var isCreatingSession = false

    /**
     * 处理创建新会话Effect
     * 会话创建后将发送SessionCreatedResult Intent
     */
    fun handleCreateNewSession() {
        logDebug("开始创建新会话...")

        // 🔥 修复：防止重复创建
        if (isCreatingSession) {
            logDebug("会话已经在创建中，跳过重复创建")
            return
        }

        isCreatingSession = true

        scope.launch {
            try {
                // 获取当前用户ID
                val userId = getUserId()
                logDebug("用户ID: $userId")

                // 🔥 修复：先检查是否有活跃会话
                val hasActiveSession = checkForActiveSession(userId)
                if (hasActiveSession) {
                    logDebug("发现活跃会话，跳过创建")
                    return@launch
                }

                // 创建新会话
                logDebug("创建新会话...")
                val result =
                    chatSessionManagementUseCase.createSession(
                        userId = userId,
                        title = "新对话",
                    )

                when (result) {
                    is ModernResult.Success<ChatSession> -> {
                        val session = result.data
                        logDebug("会话创建成功: sessionId=${session.id}, title=${session.title}")

                        // 发送会话创建完成的Intent
                        dispatch(
                            AiCoachContract.Intent.SessionCreatedResult(session),
                        )

                        logDebug("SessionCreatedResult Intent已分发")

                        // 检查是否有待发送的消息
                        val pendingMessage = state.pendingMessage
                        if (!pendingMessage.isNullOrBlank()) {
                            logDebug("发现待发送消息，准备发送")
                            dispatch(
                                AiCoachContract.Intent.SendMessage(
                                    content = pendingMessage,
                                ),
                            )
                            logDebug("待发送消息已分发")
                        }
                    }

                    is ModernResult.Error -> {
                        Timber
                            .tag("HISTORY-FIX")
                            .e("❌ [Phase2] AiCoachSessionHandler创建会话失败: ${result.error}")
                        logError(result.error, "创建会话失败")
                        dispatch(
                            AiCoachContract.Intent.OperationFailed(
                                AiCoachContract.ErrorCode.SESSION_CREATION_FAILED,
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        logDebug("会话创建中...")
                    }

                    else -> {
                        Timber
                            .tag("HISTORY-FIX")
                            .w("⚠️ [Phase2] AiCoachSessionHandler创建会话返回未知状态")
                        logDebug("创建会话返回未知状态")
                        dispatch(
                            AiCoachContract.Intent.OperationFailed(
                                AiCoachContract.ErrorCode.SESSION_CREATION_FAILED,
                            ),
                        )
                    }
                }

                // 最后重置创建状态标志
                isCreatingSession = false
            } catch (e: Exception) {
                Timber.tag("HISTORY-FIX").e(e, "❌ [Phase2] AiCoachSessionHandler创建会话异常")
                logError(e, "创建会话异常")
                dispatch(
                    AiCoachContract.Intent.OperationFailed(
                        AiCoachContract.ErrorCode.SESSION_CREATION_FAILED,
                    ),
                )
            } finally {
                // 🔥 关键修复：确保标志总是被重置
                isCreatingSession = false
                Timber.tag("HISTORY-FIX").d("🔄 [Phase2] isCreatingSession标志已重置")
            }
        }
    }

    /**
     * 检查用户是否有活跃会话
     *
     * @param userId 用户ID
     * @return 如果有活跃会话返回true
     */
    private suspend fun checkForActiveSession(userId: String): Boolean {
        try {
            val result =
                chatSessionManagementUseCase
                    .getUserSessions(
                        userId = userId,
                        includeMessages = false,
                        limit = 1,
                    ).first()

            return when (result) {
                is ModernResult.Success<*> -> {
                    @Suppress("UNCHECKED_CAST")
                    val sessions = result.data as? List<ChatSession> ?: emptyList()
                    if (sessions.isNotEmpty()) {
                        logDebug("发现${sessions.size}个活跃会话")
                        true
                    } else {
                        logDebug("没有找到活跃会话")
                        false
                    }
                }

                is ModernResult.Error -> {
                    logError(result.error, "检查活跃会话失败")
                    false
                }

                is ModernResult.Loading -> {
                    logDebug("检查活跃会话中...")
                    false
                }

                else -> {
                    logDebug("检查活跃会话返回未知状态")
                    false
                }
            }
        } catch (e: Exception) {
            logError(e, "检查活跃会话异常")
            return false
        }
    }

    /**
     * 🔥 【历史导航修复】处理会话切换（统一使用ChatRaw表）
     *
     * 修复说明：移除双读策略，统一使用ChatRaw表作为单一数据源
     * 解决MessageEvent表写入已禁用但读取仍被调用导致的消息清除问题
     */
    fun handleSwitchSession(sessionId: String) {
        scope.launch {
            try {
                Timber.tag("HISTORY-FIX").d("🔄 [Phase3] AiCoachSessionHandler开始切换会话: $sessionId")
                logDebug("🔥 [历史导航修复] 切换到会话: $sessionId")
                Timber.tag("HISTORY-NAV").i("🔄 [历史导航修复] 开始切换会话: sessionId=$sessionId")

                // 🔥 【关键修复】设置加载状态，给用户反馈
                dispatch(AiCoachContract.Intent.UpdateLoadingState(true))

                // 🔥 【单一数据源修复】直接使用ChatRaw表，移除双读策略
                // 原因：MessageEvent表写入已被禁用，但双读策略仍尝试从该表读取，导致消息丢失
                val sessionResult = chatSessionManagementUseCase.loadSession(sessionId)
                Timber
                    .tag("HISTORY-NAV")
                    .d("🔍 [历史导航修复] 会话加载结果: ${sessionResult::class.simpleName}")

                val (session, messages) =
                    when (sessionResult) {
                        is ModernResult.Success -> {
                            val loadedSession = sessionResult.data
                            logDebug("🔥 [历史导航修复] 从ChatRaw表加载到${loadedSession.messages.size}条消息")
                            Timber
                                .tag("HISTORY-NAV")
                                .i(
                                    "✅ [历史导航修复] 会话加载成功: sessionId=$sessionId, messages=${loadedSession.messages.size}",
                                )
                            Pair(loadedSession, loadedSession.messages)
                        }

                        is ModernResult.Error -> {
                            logDebug("🔥 [历史导航修复] 从ChatRaw表加载会话失败: ${sessionResult.error}")
                            Timber
                                .tag("HISTORY-NAV")
                                .e(
                                    "❌ [历史导航修复] 会话加载失败: sessionId=$sessionId, error=${sessionResult.error}",
                                )

                            // 🔥 【关键修复】创建空会话，避免UI崩溃
                            val emptySession =
                                ChatSession(
                                    id = sessionId,
                                    title = "会话加载失败",
                                    userId = "default_user",
                                    createdAt = kotlinx.datetime.Instant.fromEpochMilliseconds(
                                        System.currentTimeMillis(),
                                    ),
                                    lastActiveAt = kotlinx.datetime.Instant.fromEpochMilliseconds(
                                        System.currentTimeMillis(),
                                    ),
                                    isActive = true,
                                    status = ChatSession.SessionStatus.ACTIVE,
                                    messageCount = 0,
                                    messages = emptyList(),
                                )
                            Pair(emptySession, emptyList<CoachMessage>())
                        }

                        is ModernResult.Loading -> {
                            logDebug("🔥 [历史导航修复] ChatRaw表查询中...")
                            Timber
                                .tag("HISTORY-NAV")
                                .d("⏳ [历史导航修复] 会话加载中: sessionId=$sessionId")
                            return@launch // 等待加载完成
                        }
                    }

                logDebug("🔥 [历史导航修复] 最终加载到${messages.size}条消息")
                Timber
                    .tag("HISTORY-NAV")
                    .d("📊 [历史导航修复] 消息统计: sessionId=$sessionId, messages=${messages.size}")

                // 🔥 【MessageId一致性修复】保持数据库中的原始messageId，确保ThinkingBox能正确关联
                val messageUiList =
                    messages.map { message ->
                        when (message) {
                            is CoachMessage.UserMessage -> {
                                Timber.tag("HISTORY-NAV").v(
                                    "👤 [历史导航修复] 用户消息: id=${message.id}, content=${
                                        message.content.take(50)
                                    }...",
                                )
                                AiCoachContract.MessageUi(
                                    id = message.id, // 🔥 使用原始messageId，不重新生成
                                    content = message.content,
                                    isFromUser = true,
                                    timestamp = kotlinx.datetime.Instant.fromEpochMilliseconds(
                                        message.timestamp,
                                    ),
                                    saveStatus = AiCoachContract.SaveStatus.SAVED,
                                )
                            }

                            is CoachMessage.AiMessage -> {
                                Timber.tag("HISTORY-NAV").v(
                                    "🤖 [历史导航修复] AI消息: id=${message.id}, content=${
                                        message.content.take(50)
                                    }...",
                                )
                                AiCoachContract.MessageUi(
                                    id = message.id, // 🔥 使用原始messageId，确保ThinkingBox能找到对应消息
                                    content = message.content,
                                    isFromUser = false,
                                    timestamp = kotlinx.datetime.Instant.fromEpochMilliseconds(
                                        message.timestamp,
                                    ),
                                    saveStatus = AiCoachContract.SaveStatus.SAVED,
                                    // 🔥 【多轮对话修复】保留finalMarkdown，用于历史ThinkingBox查看
                                    finalMarkdown = message.finalMarkdown ?: message.content,
                                )
                            }
                        }
                    }

                Timber
                    .tag("HISTORY-NAV")
                    .i(
                        "🎯 [历史导航修复] 准备发送SessionWithMessagesLoaded: sessionId=$sessionId, uiMessages=${messageUiList.size}",
                    )

                // 🔥 【关键修复】发送会话加载完成Intent，清除加载状态
                Timber
                    .tag("HISTORY-FIX")
                    .d(
                        "🎯 [Phase3] 准备发送SessionWithMessagesLoaded Intent: sessionId=$sessionId, messages=${messageUiList.size}",
                    )
                dispatch(
                    AiCoachContract.Intent.SessionWithMessagesLoaded(
                        session = session,
                        messages = messageUiList.toImmutableList(),
                    ),
                )

                // 🔥 【关键修复】清除加载状态
                Timber.tag("HISTORY-FIX").d("🔄 [Phase3] 清除加载状态")
                dispatch(AiCoachContract.Intent.UpdateLoadingState(false))

                Timber.tag("HISTORY-NAV").i("✅ [历史导航修复] 会话切换完成: sessionId=$sessionId")
            } catch (e: Exception) {
                Timber.tag("HISTORY-FIX").e(e, "❌ [Phase3] AiCoachSessionHandler切换会话异常: $sessionId")
                logError(e, "🔥 [多轮对话修复] 切换会话异常: $sessionId")
                // 🔥 确保清除加载状态
                dispatch(AiCoachContract.Intent.UpdateLoadingState(false))
                dispatch(
                    AiCoachContract.Intent.OperationFailed(AiCoachContract.ErrorCode.SESSION_LOAD_FAILED),
                )
            }
        }
    }

    /**
     * 🔥 实现：加载初始会话数据
     */
    fun handleLoadInitialSession() {
        scope.launch {
            logDebug("加载初始会话数据")

            try {
                val userId = getUserId()
                logDebug("加载会话参数: userId=$userId")

                // 调用 getRecentSessions 获取最新的一个会话
                val result = chatSessionManagementUseCase.getRecentSessions(userId, 1).first()

                logDebug("加载会话结果类型: ${result::class.simpleName}")

                when (result) {
                    is ModernResult.Success<*> -> {
                        @Suppress("UNCHECKED_CAST")
                        val sessions = result.data as? List<ChatSession> ?: emptyList()
                        val session = sessions.firstOrNull()

                        if (session != null) {
                            logDebug("加载最新会话成功: sessionId=${session.id}, title=${session.title}")
                            logDebug(
                                "会话详情: messageCount=${session.messages.size}, userId=${session.userId}",
                            )

                            // 直接使用获取到的会话及其消息
                            logDebug(
                                "准备发送SessionCreatedResult Intent: sessionId=${session.id}, title=${session.title}",
                            )

                            val sessionCreatedIntent =
                                AiCoachContract.Intent.SessionCreatedResult(session)
                            logDebug("SessionCreatedResult Intent对象: $sessionCreatedIntent")

                            dispatch(sessionCreatedIntent)
                            logDebug("SessionCreatedResult Intent已分发")

                            // 🔥 修复：使用现有的转换函数加载历史消息
                            if (session.messages.isNotEmpty()) {
                                logDebug("🔍 [UI-DEBUG] 转换历史消息: ${session.messages.size}条消息")

                                // 🔍 详细记录每条消息
                                session.messages.forEachIndexed { index, msg ->
                                    val messageContent =
                                        when (msg) {
                                            is CoachMessage.UserMessage -> msg.content
                                            is CoachMessage.AiMessage -> msg.content
                                        }
                                    logDebug(
                                        "🔍 [UI-DEBUG] 原始消息[$index]: id=${msg.id}, type=${msg::class.simpleName}, content=${
                                            messageContent.take(
                                                50,
                                            )
                                        }...",
                                    )
                                }

                                val messagesUi =
                                    com.example.gymbro.features.coach.aicoach.AiCoachMapper
                                        .convertCoachMessagesToMessageUi(session.messages)
                                        .toImmutableList()

                                logDebug("🔍 [UI-DEBUG] 历史消息转换完成: ${messagesUi.size}条UI消息")

                                // 🔍 详细记录每条UI消息
                                messagesUi.forEachIndexed { index, msg ->
                                    logDebug(
                                        "🔍 [UI-DEBUG] UI消息[$index]: id=${msg.id}, isFromUser=${msg.isFromUser}, content=${
                                            msg.content.take(
                                                50,
                                            )
                                        }...",
                                    )
                                }

                                logDebug("🔍 [UI-DEBUG] 发送 SessionWithMessagesLoaded Intent")
                                dispatch(
                                    AiCoachContract.Intent.SessionWithMessagesLoaded(
                                        session = session,
                                        messages = messagesUi,
                                    ),
                                )
                                logDebug("🔍 [UI-DEBUG] 历史消息已加载到UI: ${messagesUi.size}条消息")
                            } else {
                                logDebug("🔍 [UI-DEBUG] 会话无历史消息，保持空状态")
                            }

                            // 🔥 强化验证：等待片刻后检查状态是否更新
                            delay(100) // 等待100ms让Reducer处理
                            val currentActiveSessionId = state.activeSessionId
                            logDebug("Intent分发后验证: currentActiveSessionId=$currentActiveSessionId")
                            if (currentActiveSessionId == session.id) {
                                logDebug("会话状态更新成功，activeSessionId已设置")
                            } else {
                                logError(
                                    Exception("会话状态更新失败"),
                                    "期望: ${session.id}, 实际: $currentActiveSessionId",
                                )
                                logDebug("当前activeSession: ${state.activeSession}")
                            }
                        } else {
                            logDebug("🔍 [SessionHandler] 没有找到现有会话，但不自动创建，等待用户操作")
                        }
                    }

                    is ModernResult.Error -> {
                        logError(result.error, "加载初始会话失败")
                        logDebug("🔍 [SessionHandler] 不再自动创建会话，等待用户操作")
                    }

                    is ModernResult.Loading -> {
                        logDebug("加载初始会话中...")
                    }

                    else -> {
                        logDebug("🔍 [SessionHandler] 不再自动创建会话，等待用户操作")
                    }
                }
            } catch (e: Exception) {
                logError(e, "加载初始会话异常")
                logDebug("🔍 [SessionHandler] 异常情况下也不自动创建会话，等待用户操作")
            }
        }
    }

    /**
     * 处理保存用户消息Effect
     */
    fun handleSaveUserMessage(effect: AiCoachContract.Effect.SaveUserMessage) {
        scope.launch {
            try {
                logDebug(
                    "🔥 [消息保存修复] 开始保存用户消息: sessionId=${effect.sessionId}, messageId=${effect.userMessageId}",
                )
                Timber
                    .tag("MESSAGE-SAVE")
                    .d(
                        "🔥 [消息保存修复] 用户消息保存流程开始: messageId=${effect.userMessageId}, content长度=${effect.content.length}",
                    )

                // 🔥 更新保存状态为正在保存
                dispatch(
                    AiCoachContract.Intent.UpdateMessageSaveStatus(
                        messageId = effect.userMessageId,
                        saveStatus = AiCoachContract.SaveStatus.SAVING,
                    ),
                )

                Timber
                    .tag("MESSAGE-SAVE")
                    .d("🔥 [消息保存修复] 保存状态已更新为SAVING: messageId=${effect.userMessageId}")

                val userMessage =
                    CoachMessage.UserMessage(
                        id = effect.userMessageId,
                        content = effect.content,
                        timestamp = System.currentTimeMillis(),
                    )

                Timber
                    .tag("MESSAGE-SAVE")
                    .d(
                        "🔥 [消息保存修复] 调用chatSessionManagementUseCase.saveUserMessage: sessionId=${effect.sessionId}",
                    )

                val result =
                    chatSessionManagementUseCase.saveUserMessage(
                        effect.sessionId,
                        userMessage,
                    )

                Timber
                    .tag("MESSAGE-SAVE")
                    .d("🔥 [消息保存修复] UseCase调用完成，处理结果: ${result::class.simpleName}")

                when (result) {
                    is ModernResult.Success -> {
                        logDebug("🔥 [消息保存修复] 用户消息保存成功: messageId=${effect.userMessageId}")
                        Timber
                            .tag("MESSAGE-SAVE")
                            .d("✅ [消息保存修复] 用户消息保存成功: messageId=${effect.userMessageId}")

                        // 🔥 新增：如果是第一条消息，生成标题
                        tryGenerateSessionTitle(effect.sessionId, effect.content)

                        // 🔥 更新保存状态为已保存
                        dispatch(
                            AiCoachContract.Intent.MessageSaveCompletedResult(
                                messageId = effect.userMessageId,
                                success = true,
                            ),
                        )

                        Timber
                            .tag("MESSAGE-SAVE")
                            .d("✅ [消息保存修复] 保存状态已更新为SAVED: messageId=${effect.userMessageId}")
                    }

                    is ModernResult.Error -> {
                        logError(result.error, "🔥 [消息保存修复] 用户消息保存失败")
                        Timber
                            .tag("MESSAGE-SAVE")
                            .e(
                                "❌ [消息保存修复] 用户消息保存失败: messageId=${effect.userMessageId}, error=${result.error}",
                            )

                        // 🔥 更新保存状态为失败
                        dispatch(
                            AiCoachContract.Intent.MessageSaveCompletedResult(
                                messageId = effect.userMessageId,
                                success = false,
                            ),
                        )

                        Timber
                            .tag("MESSAGE-SAVE")
                            .d("❌ [消息保存修复] 保存状态已更新为FAILED: messageId=${effect.userMessageId}")
                    }

                    is ModernResult.Loading -> {
                        logDebug("🔥 [消息保存修复] 用户消息保存中...")
                        Timber
                            .tag("MESSAGE-SAVE")
                            .d("🔄 [消息保存修复] 用户消息保存中: messageId=${effect.userMessageId}")
                    }
                }
            } catch (e: Exception) {
                logError(e, "🔥 [消息保存修复] 保存用户消息异常: messageId=${effect.userMessageId}")
                Timber.tag("MESSAGE-SAVE")
                    .e(e, "💥 [消息保存修复] 保存用户消息异常: messageId=${effect.userMessageId}")

                // 🔥 异常时也要更新保存状态为失败
                dispatch(
                    AiCoachContract.Intent.MessageSaveCompletedResult(
                        messageId = effect.userMessageId,
                        success = false,
                    ),
                )

                Timber
                    .tag("MESSAGE-SAVE")
                    .d("💥 [消息保存修复] 异常后保存状态已更新为FAILED: messageId=${effect.userMessageId}")
            }
        }
    }

    /**
     * 处理保存AI消息Effect
     */
    fun handleSaveAiMessage(effect: AiCoachContract.Effect.SaveAiMessage) {
        scope.launch {
            try {
                logDebug(
                    "开始保存AI消息: sessionId=${effect.sessionId}, aiResponseId=${effect.aiResponseId}, content.length=${effect.content.length}",
                )

                // 🔥 【关键调试】验证thinkingNodes是否正确传递
                val thinkingNodesLength = effect.thinkingNodes?.length ?: 0
                Timber
                    .tag("MESSAGE-SAVE")
                    .d("🔥 [消息保存修复] AI消息保存开始: messageId=${effect.aiResponseId}")
                Timber
                    .tag("TOKEN-FLOW")
                    .i("📊 [AiCoachSessionHandler] 接收到thinkingNodes: length=$thinkingNodesLength")
                if (thinkingNodesLength > 0) {
                    Timber.tag("TOKEN-FLOW").d(
                        "📊 [AiCoachSessionHandler] thinkingNodes前100字符: '${
                            effect.thinkingNodes?.take(100)
                        }...'",
                    )
                } else {
                    Timber.tag("TOKEN-FLOW").w("⚠️ [AiCoachSessionHandler] thinkingNodes为空或null")
                }

                // 🔥 更新保存状态为正在保存
                dispatch(
                    AiCoachContract.Intent.UpdateMessageSaveStatus(
                        messageId = effect.aiResponseId,
                        saveStatus = AiCoachContract.SaveStatus.SAVING,
                    ),
                )

                val aiMessage =
                    CoachMessage.AiMessage(
                        id = effect.aiResponseId,
                        content = effect.content,
                        timestamp = System.currentTimeMillis(),
                        // 🔥 【单一数据源修复】保存完整的思考内容到ChatRaw架构
                        finalMarkdown = effect.finalMarkdown,
                        rawTokens = effect.thinkingNodes,
                    )

                val result =
                    chatSessionManagementUseCase.saveAiMessage(
                        effect.sessionId,
                        aiMessage,
                    )

                when (result) {
                    is ModernResult.Success -> {
                        logDebug("AI消息保存成功: aiResponseId=${effect.aiResponseId}")
                        // 🔥 更新保存状态为已保存
                        dispatch(
                            AiCoachContract.Intent.MessageSaveCompletedResult(
                                messageId = effect.aiResponseId,
                                success = true,
                            ),
                        )
                    }

                    is ModernResult.Error -> {
                        logError(result.error, "AI消息保存失败")
                        // 🔥 更新保存状态为失败
                        dispatch(
                            AiCoachContract.Intent.MessageSaveCompletedResult(
                                messageId = effect.aiResponseId,
                                success = false,
                            ),
                        )
                    }

                    is ModernResult.Loading -> {
                        logDebug("AI消息保存中...")
                    }
                }
            } catch (e: Exception) {
                logError(e, "保存AI消息异常: aiResponseId=${effect.aiResponseId}")
                // 🔥 异常时也要更新保存状态为失败
                dispatch(
                    AiCoachContract.Intent.MessageSaveCompletedResult(
                        messageId = effect.aiResponseId,
                        success = false,
                    ),
                )
            }
        }
    }

    /**
     * 🔥 新增：尝试为会话生成标题
     *
     * 只在会话标题为默认标题时才生成新标题
     */
    private fun tryGenerateSessionTitle(
        sessionId: String,
        firstMessage: String,
    ) {
        scope.launch {
            try {
                // 检查当前会话是否需要生成标题
                val sessionResult = chatSessionManagementUseCase.loadSession(sessionId)
                when (sessionResult) {
                    is ModernResult.Success -> {
                        val session = sessionResult.data
                        // 只有当标题是默认标题时才生成新标题
                        if (session.title == "新的对话" || session.title == "新对话" || session.title.isBlank()) {
                            logDebug("为会话生成标题: sessionId=$sessionId, message=${firstMessage.take(20)}...")

                            // 使用现有的标题生成系统
                            val titleResult = generateChatTitleUseCase(sessionId, firstMessage)
                            when (titleResult) {
                                is ModernResult.Success -> {
                                    logDebug("标题生成成功: ${titleResult.data.title}")
                                    // 标题已经在UseCase中更新到数据库了
                                }

                                is ModernResult.Error -> {
                                    logError(titleResult.error, "标题生成失败")
                                }

                                is ModernResult.Loading -> {
                                    logDebug("标题生成中...")
                                }
                            }
                        } else {
                            logDebug("会话已有自定义标题，跳过生成: ${session.title}")
                        }
                    }

                    is ModernResult.Error -> {
                        logError(sessionResult.error, "加载会话失败，无法生成标题")
                    }

                    is ModernResult.Loading -> {
                        logDebug("会话加载中，稍后生成标题")
                    }
                }
            } catch (e: Exception) {
                logError(e, "生成会话标题异常")
            }
        }
    }

    /**
     * 🔥 修复：统一用户ID管理，确保一致性
     */
    private fun getUserId(): String {
        // 🔥 修复：统一使用 "default_user" 确保与历史查询一致
        // 这确保了会话创建和历史查询使用相同的用户ID
        return "default_user"
    }
}

package com.example.gymbro.designSystem.theme.tokens

import androidx.compose.ui.graphics.Color

/**
 * GymBro 高级颜色系统
 * 采用13级精致灰阶系统，提供更丰富的视觉层次
 */

// === 13级精致灰阶系统 ===
object GrayScale {
    val Gray000 = Color(0xFF000000) // 纯黑 - 最深背景
    val Gray050 = Color(0xFF0A0A0A) // 深空黑 - 深层界面背景
    val Gray100 = Color(0xFF1A1A1A) // 深炭黑 - 卡片背景（深色主题）
    val Gray200 = Color(0xFF2A2A2A) // 炭灰 - 次级背景/分割线
    val Gray300 = Color(0xFF3A3A3A) // 深石墨 - 禁用状态/边框
    val Gray400 = Color(0xFF4A4A4A) // 石墨 - 次要文本（深色主题）
    val Gray500 = Color(0xFF6A6A6A) // 中性灰 - 占位符文本
    val Gray600 = Color(0xFF8A8A8A) // 暖灰 - 标签文本
    val Gray700 = Color(0xFFABABAB) // 浅石墨 - 主要文本（浅色主题）
    val Gray800 = Color(0xFFCCCCCC) // 银灰 - 次要文本（浅色主题）
    val Gray850 = Color(0xFFE0E0E0) // 淡银 - 分割线（浅色主题）
    val Gray900 = Color(0xFFF5F5F5) // 珍珠白 - 背景变体（浅色主题）
    val Gray950 = Color(0xFFFFFFFF) // 纯白 - 主背景（浅色主题）
}

// === 高级品牌色系统 ===
object BrandColors {
    // 主品牌色 - 石墨银系列（更高级的银色调）
    val PlatinumSilver = Color(0xFF9B9B9B) // 主品牌色 - 铂金银
    val DeepSilver = Color(0xFF6B6B6B) // 深银色 - 用于强调
    val LightSilver = Color(0xFFCACACA) // 浅银色 - 用于次要元素

    // CTA 颜色 - 更优雅的橙色系列
    val PremiumOrange = Color(0xFFE55A1F) // 主CTA色 - 优雅橙
    val SoftOrange = Color(0xFFFF8A65) // 浅橙色 - 用于悬停状态
    val DeepOrange = Color(0xFFD84315) // 深橙色 - 用于按压状态
}

// === 功能性颜色系统 ===
object FunctionalColors {
    // 成功色系 - 优雅绿色
    val Success = Color(0xFF4CAF50)
    val SuccessLight = Color(0xFF81C784)
    val SuccessDark = Color(0xFF388E3C)

    // 警告色系 - 琥珀色
    val Warning = Color(0xFFFF9800)
    val WarningLight = Color(0xFFFFB74D)
    val WarningDark = Color(0xFFF57C00)

    // 错误色系 - 优雅红色
    val Error = Color(0xFFE57373)
    val ErrorLight = Color(0xFFEF9A9A)
    val ErrorDark = Color(0xFFD32F2F)

    // 信息色系 - 冷蓝色
    val Info = Color(0xFF42A5F5)
    val InfoLight = Color(0xFF90CAF9)
    val InfoDark = Color(0xFF1976D2)

    // 进度色系 - 用于计划进度追踪
    object Progress {
        // 未开始状态 - 中性灰色
        val IdleLight = Color(0xFFE0E0E0)
        val IdleDark = Color(0xFF424242)

        // 进行中状态 - 活跃橙色
        val OngoingLight = Color(0xFFFFB74D)
        val OngoingDark = Color(0xFFFF8A65)

        // 已完成状态 - 成功绿色
        val DoneLight = Color(0xFF81C784)
        val DoneDark = Color(0xFF66BB6A)
    }
}

// === 深色主题颜色方案 ===
object DarkThemeColors {
    // 主要颜色
    val primary = BrandColors.PlatinumSilver
    val onPrimary = GrayScale.Gray000
    val primaryContainer = GrayScale.Gray200
    val onPrimaryContainer = GrayScale.Gray800

    // 次要颜色
    val secondary = GrayScale.Gray600
    val onSecondary = GrayScale.Gray950
    val secondaryContainer = GrayScale.Gray200
    val onSecondaryContainer = GrayScale.Gray800

    // 第三级颜色
    val tertiary = GrayScale.Gray700
    val onTertiary = GrayScale.Gray100
    val tertiaryContainer = GrayScale.Gray300
    val onTertiaryContainer = GrayScale.Gray850

    // 错误颜色
    val error = FunctionalColors.Error
    val onError = GrayScale.Gray000
    val errorContainer = FunctionalColors.ErrorDark
    val onErrorContainer = GrayScale.Gray950

    // 背景颜色
    val background = GrayScale.Gray000
    val onBackground = GrayScale.Gray800
    val surface = GrayScale.Gray100
    val onSurface = GrayScale.Gray800

    // 表面变体
    val surfaceVariant = GrayScale.Gray200
    val onSurfaceVariant = GrayScale.Gray700
    val surfaceTint = BrandColors.PlatinumSilver

    // 轮廓颜色
    val outline = GrayScale.Gray400
    val outlineVariant = GrayScale.Gray300

    // 反转颜色
    val inverseSurface = GrayScale.Gray800
    val inverseOnSurface = GrayScale.Gray200
    val inversePrimary = GrayScale.Gray500

    // 遮罩
    val scrim = Color(0x99000000)
}

// === 浅色主题颜色方案 ===
object LightThemeColors {
    // 主要颜色
    val primary = BrandColors.DeepSilver
    val onPrimary = GrayScale.Gray950
    val primaryContainer = GrayScale.Gray900
    val onPrimaryContainer = GrayScale.Gray300

    // 次要颜色
    val secondary = GrayScale.Gray600
    val onSecondary = GrayScale.Gray950
    val secondaryContainer = GrayScale.Gray850
    val onSecondaryContainer = GrayScale.Gray300

    // 第三级颜色
    val tertiary = GrayScale.Gray500
    val onTertiary = GrayScale.Gray950
    val tertiaryContainer = GrayScale.Gray900
    val onTertiaryContainer = GrayScale.Gray400

    // 错误颜色
    val error = FunctionalColors.ErrorDark
    val onError = GrayScale.Gray950
    val errorContainer = FunctionalColors.ErrorLight
    val onErrorContainer = GrayScale.Gray300

    // 背景颜色
    val background = GrayScale.Gray950
    val onBackground = GrayScale.Gray300
    val surface = GrayScale.Gray950
    val onSurface = GrayScale.Gray300

    // 表面变体
    val surfaceVariant = GrayScale.Gray900
    val onSurfaceVariant = GrayScale.Gray500
    val surfaceTint = BrandColors.DeepSilver

    // 轮廓颜色
    val outline = GrayScale.Gray700
    val outlineVariant = GrayScale.Gray850

    // 反转颜色
    val inverseSurface = GrayScale.Gray300
    val inverseOnSurface = GrayScale.Gray900
    val inversePrimary = BrandColors.PlatinumSilver

    // 遮罩
    val scrim = Color(0x33000000)
}

// === 特殊用途颜色（向后兼容） ===
@Deprecated("使用 BrandColors.PremiumOrange 替代", ReplaceWith("BrandColors.PremiumOrange"))
val SubscriptionCTAOrange = BrandColors.PremiumOrange

@Deprecated("使用 GrayScale.Gray000 + 90% alpha 替代", ReplaceWith("GrayScale.Gray000.copy(alpha = 0.9f)"))
val SubscriptionBackgroundOverlay = Color(0xE6000000)

// 保持向后兼容的颜色别名
val StatusBarIconColor = GrayScale.Gray950
val SubscriptionFeatureTextColor = GrayScale.Gray950
val SubscriptionSubtitleColor = GrayScale.Gray600
val SubscriptionPriceDescColor = GrayScale.Gray700
val SubscriptionLegalTextColor = GrayScale.Gray500
val SubscriptionTitleColor = GrayScale.Gray800
val SegmentedControlUnselectedText = GrayScale.Gray800
val SegmentedControlUnselectedBackground = GrayScale.Gray100
val SubscriptionFeatureIconColor = GrayScale.Gray950

// === Coach页面专用颜色配置（基于13阶GrayScale系统） ===

/**
 * Coach页面颜色配置
 * 基于13阶精致灰阶系统，为ChatGPT风格界面优化
 * 提供深色和浅色主题的最佳颜色搭配，提升美观度
 */
object CoachThemeColors {
    /**
     * 深色主题配色方案
     * 基于workout模块配色逻辑，确保与训练页面一致性
     */
    object Dark {
        // 背景层次 - 使用更合理的深色配色，提高可读性
        val backgroundPrimary = Color(0xFF1A1A1A) // 主背景 - 与workout一致的深灰
        val backgroundSecondary = Color(0xFF212121) // 深层背景 - 更亮的深灰
        val backgroundElevated = Color(0xFF2A2A2A) // 卡片背景 - 提升的深灰
        val backgroundInput = Color(0xFF2A2A2A) // 输入框背景 - 与卡片一致

        // 文本层次 - 提高对比度，确保可读性
        val textPrimary = Color(0xFFFFFFFF) // 主要文本 - 纯白，最高可读性
        val textSecondary = Color(0xFFE0E0E0) // 次要文本 - 浅灰，清晰层次
        val textTertiary = Color(0xFFB0B0B0) // 第三级文本 - 中灰，微妙信息
        val textPlaceholder = Color(0xFF808080) // 占位符 - 中性灰，引导性

        // 图标层次 - 与文本保持一致的视觉层次
        val iconPrimary = Color(0xFFFFFFFF) // 主要图标 - 纯白
        val iconSecondary = Color(0xFFB0B0B0) // 次要图标 - 中灰
        val iconTertiary = Color(0xFF808080) // 装饰图标 - 中性灰

        // 边框和分割线 - 微妙而精致的分界
        val borderPrimary = Color(0xFF3A3A3A) // 主要边框 - 浅深灰
        val borderSecondary = Color(0xFF2A2A2A) // 次要边框 - 深灰
        val dividerPrimary = Color(0xFF2A2A2A) // 主分割线 - 深灰
        val dividerSecondary = Color(0xFF1A1A1A) // 次分割线 - 主背景色

        // 交互元素 - 芯片、按钮等
        val chipBackground = Color(0xFF2A2A2A) // 芯片背景 - 深灰
        val chipBackgroundHover = Color(0xFF3A3A3A) // 芯片悬停 - 浅深灰
        val chipBorder = Color(0xFF3A3A3A) // 芯片边框 - 浅深灰

        // 动态强调色 - 根据主题风格动态变化，与workout保持一致
        val accentPrimary = Color(0xFF3F6CF3) // 主要强调色 - 默认使用Grok蓝色
        val accentSecondary = Color(0xFF707070) // 次要强调色 - 中性灰
        val accentCTA = Color(0xFF3F6CF3) // CTA强调色 - 与主要强调色一致
    }

    /**
     * 浅色主题配色方案
     * 基于workout模块配色逻辑，确保与训练页面一致性
     */
    object Light {
        // 背景层次 - 使用清新的浅色配色
        val backgroundPrimary = Color(0xFFFFFFFF) // 主背景 - 纯白
        val backgroundSecondary = Color(0xFFF5F5F5) // 浅层背景 - 浅灰
        val backgroundElevated = Color(0xFFF0F0F0) // 卡片背景 - 微妙层次
        val backgroundInput = Color(0xFFF0F0F0) // 输入框背景 - 与卡片一致

        // 文本层次 - 深色文本确保可读性
        val textPrimary = Color(0xFF000000) // 主要文本 - 纯黑，最高对比度
        val textSecondary = Color(0xFF2E2E2E) // 次要文本 - 深灰，清晰层次
        val textTertiary = Color(0xFF666666) // 第三级文本 - 中灰，温和信息
        val textPlaceholder = Color(0xFF999999) // 占位符 - 浅灰，引导性

        // 图标层次 - 与文本保持一致
        val iconPrimary = Color(0xFF000000) // 主要图标 - 纯黑
        val iconSecondary = Color(0xFF666666) // 次要图标 - 中灰
        val iconTertiary = Color(0xFF999999) // 装饰图标 - 浅灰

        // 边框和分割线 - 清晰而不突兀的分界
        val borderPrimary = Color(0xFFE0E0E0) // 主要边框 - 浅灰
        val borderSecondary = Color(0xFFF0F0F0) // 次要边框 - 微妙灰
        val dividerPrimary = Color(0xFFF0F0F0) // 主分割线 - 微妙灰
        val dividerSecondary = Color(0xFFF5F5F5) // 次分割线 - 浅层背景色

        // 交互元素 - 芯片、按钮等
        val chipBackground = Color(0xFFF0F0F0) // 芯片背景 - 微妙灰
        val chipBackgroundHover = Color(0xFFE0E0E0) // 芯片悬停 - 浅灰
        val chipBorder = Color(0xFFE0E0E0) // 芯片边框 - 浅灰

        // 动态强调色 - 根据主题风格动态变化，与workout保持一致
        val accentPrimary = Color(0xFF3F6CF3) // 主要强调色 - 默认使用Grok蓝色
        val accentSecondary = Color(0xFF909090) // 次要强调色 - 中性灰
        val accentCTA = Color(0xFF3F6CF3) // CTA强调色 - 与主要强调色一致
    }

    /**
     * AI特效颜色（主题无关）
     * 用于流式文本、动画效果等特殊视觉元素
     */
    object AIEffects {
        val streamingGradientStart = BrandColors.PlatinumSilver
        val streamingGradientEnd = BrandColors.LightSilver
        val suggestionHighlight = BrandColors.PremiumOrange.copy(alpha = 0.12f)
        val loadingIndicator = BrandColors.PlatinumSilver
        val pulseEffect = BrandColors.PlatinumSilver.copy(alpha = 0.3f)
    }
}

// === 主题颜色配置已迁移到 ThemeColors.kt ===
// DeepSeek、Grok、ChatGPT 主题颜色配置现在统一在 ThemeColors.kt 文件中管理

// === 遗留颜色定义（将逐步迁移） ===
@Deprecated("使用 GrayScale 和 BrandColors 替代")
val Black = GrayScale.Gray000

@Deprecated("使用 BrandColors.PlatinumSilver 替代")
val SilverAccent = BrandColors.PlatinumSilver

@Deprecated("使用 GrayScale.Gray200 替代")
val DarkGray = GrayScale.Gray200

@Deprecated("使用 GrayScale.Gray400 替代")
val MediumGray = GrayScale.Gray400

@Deprecated("使用 GrayScale.Gray700 替代")
val LightGray = GrayScale.Gray700

@Deprecated("使用 GrayScale.Gray950 替代")
val White = GrayScale.Gray950

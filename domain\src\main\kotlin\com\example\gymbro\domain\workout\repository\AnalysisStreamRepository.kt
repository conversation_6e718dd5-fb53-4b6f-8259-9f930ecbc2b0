package com.example.gymbro.domain.workout.repository

import com.example.gymbro.core.ai.prompt.builder.CoreChatMessage
import kotlinx.coroutines.flow.Flow

/**
 * 训练分析流式Repository接口
 *
 * 🔥 【事件总线架构】升级版本：支持事件总线和传统Flow两种模式
 */
interface AnalysisStreamRepository {

    /**
     * 获取训练分析的流式响应 - 传统模式
     *
     * @param analysisId 分析ID，用于Token路由
     * @param messages 已构建的消息列表
     * @param model 指定使用的AI模型
     * @return 包含原始字符串token的Flow
     * @deprecated 使用 getStreamingResponseWithMessageId 以支持事件总线架构
     */
    @Deprecated("使用 getStreamingResponseWithMessageId 以支持事件总线架构")
    fun getStreamingResponse(
        analysisId: String,
        messages: List<CoreChatMessage>,
        model: String,
    ): Flow<String>

    /**
     * 获取训练分析的流式响应 - 事件总线版本
     *
     * 🔥 【事件总线架构】Token会自动发布到TokenBus，调用方无需处理返回值
     *
     * @param messageId 消息ID，用于事件总线路由
     * @param messages 已构建的消息列表
     * @param model 指定使用的AI模型
     */
    suspend fun getStreamingResponseWithMessageId(
        messageId: String,
        messages: List<CoreChatMessage>,
        model: String,
    )
}

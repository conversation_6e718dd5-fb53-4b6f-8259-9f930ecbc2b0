package com.example.gymbro.core.userdata.api.model

/**
 * 数据同步状态枚举
 *
 * 用于跟踪用户数据在不同模块间的同步状态，确保数据一致性。
 *
 * @property displayName 状态的显示名称
 * @property description 状态的详细描述
 */
enum class SyncStatus(
    val displayName: String,
    val description: String,
) {
    /**
     * 已同步状态
     * 数据在所有模块间保持一致
     */
    SYNCED(
        displayName = "已同步",
        description = "数据在所有模块间保持一致",
    ),

    /**
     * 等待同步状态
     * 数据已更新但尚未同步到其他模块
     */
    PENDING_SYNC(
        displayName = "等待同步",
        description = "数据已更新但尚未同步到其他模块",
    ),

    /**
     * 同步失败状态
     * 数据同步过程中发生错误
     */
    SYNC_FAILED(
        displayName = "同步失败",
        description = "数据同步过程中发生错误",
    ),

    /**
     * 部分同步状态
     * 部分数据已同步，但仍有数据等待同步
     */
    PARTIAL_SYNC(
        displayName = "部分同步",
        description = "部分数据已同步，但仍有数据等待同步",
    ),

    /**
     * 同步中状态
     * 数据正在同步过程中
     */
    SYNCING(
        displayName = "同步中",
        description = "数据正在同步过程中",
    ),
    ;

    /**
     * 检查是否为错误状态
     */
    val isError: Boolean
        get() = this == SYNC_FAILED

    /**
     * 检查是否为稳定状态（已完成同步）
     */
    val isStable: Boolean
        get() = this == SYNCED

    /**
     * 检查是否需要重试同步
     */
    val needsRetry: Boolean
        get() = this == SYNC_FAILED || this == PENDING_SYNC

    /**
     * 检查是否正在进行中
     */
    val isInProgress: Boolean
        get() = this == SYNCING || this == PARTIAL_SYNC

    companion object {
        /**
         * 从字符串解析同步状态
         * @param value 状态字符串
         * @return 对应的同步状态，如果无法解析则返回 PENDING_SYNC
         */
        fun fromString(value: String?): SyncStatus {
            return try {
                value?.let { valueOf(it.uppercase()) } ?: PENDING_SYNC
            } catch (e: IllegalArgumentException) {
                PENDING_SYNC
            }
        }

        /**
         * 获取所有可重试的状态
         */
        val retryableStatuses: List<SyncStatus>
            get() = listOf(SYNC_FAILED, PENDING_SYNC)

        /**
         * 获取所有进行中的状态
         */
        val inProgressStatuses: List<SyncStatus>
            get() = listOf(SYNCING, PARTIAL_SYNC)
    }
}

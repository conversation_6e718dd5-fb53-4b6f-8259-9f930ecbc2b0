package com.example.gymbro.domain.workout.mapper

import com.example.gymbro.domain.workout.model.template.TemplateExercise
import com.example.gymbro.domain.workout.model.template.TemplateSet
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.shared.models.workout.*

/**
 * WorkoutTemplate Domain模型 到 DTO 转换映射器
 *
 * 解决Repository返回Domain模型，UseCase返回DTO的类型不匹配问题
 * 按照template-todo完善.md文档要求实现
 */

/**
 * 将Domain模型转换为DTO
 * 针对Phase1版本控制功能的兼容性设计
 */
fun WorkoutTemplate.toDto(): WorkoutTemplateDto {
    val result = WorkoutTemplateDto(
        id = this.id,
        name = this.name,
        description = this.description ?: "",
        difficulty = mapDomainDifficultyToDto(this.difficulty),
        exercises =
        this.exercises.map { exercise ->
            TemplateExerciseDto(
                id = exercise.id,
                exerciseId = exercise.exerciseId,
                exerciseName = exercise.name,
                // 🔥 关键修复：保持动作库JSON数据（imageUrl, videoUrl等）
                imageUrl = exercise.imageUrl,
                videoUrl = exercise.videoUrl,
                sets = exercise.sets,
                reps = exercise.reps,
                targetWeight = exercise.weight,
                restTimeSeconds = exercise.restSeconds,
                notes = exercise.notes,
                // 🔥 关键修复：映射customSets数据，确保双向转换完整
                customSets = exercise.customSets.map { set ->
                    TemplateSetDto(
                        setNumber = set.setNumber,
                        targetReps = set.targetReps,
                        targetWeight = set.targetWeight,
                        restTimeSeconds = set.restTimeSeconds,
                        targetDuration = set.targetDuration,
                        rpe = set.rpe,
                    )
                },
            )
        },
        category = inferCategoryFromMuscleGroups(this.targetMuscleGroups ?: emptyList()),
        // Phase1 版本控制字段 - v30重构：直接映射
        isDraft = this.isDraft,
        currentVersion = this.currentVersion,
        isPublished = this.isPublished,
        lastPublishedAt = this.lastPublishedAt,
        metadata =
        TemplateMetadataDto(
            estimatedDuration = this.estimatedDuration ?: 30,
            targetMuscleGroups = this.targetMuscleGroups ?: emptyList(),
            equipment = emptyList(),
            tags = this.tags ?: emptyList(),
        ),
        source = TemplateSource.USER,
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
    )

    return result
}

/**
 * 将DTO转换为Domain模型
 */
fun WorkoutTemplateDto.toDomain(): WorkoutTemplate =
    WorkoutTemplate(
        id = this.id,
        name = this.name.takeIf { it.isNotBlank() } ?: "未命名训练", // 🔥 修复：防止空名称导致崩溃
        description = this.description.takeIf { it.isNotBlank() },
        exercises =
        this.exercises.map { exercise ->
            TemplateExercise(
                id =
                exercise.id ?: java.util.UUID
                    .randomUUID()
                    .toString(),
                exerciseId = exercise.exerciseId,
                name = exercise.exerciseName ?: "Unknown Exercise",
                order = 0,
                sets = exercise.sets,
                reps = exercise.reps,
                restSeconds = exercise.restTimeSeconds,
                weight = exercise.targetWeight,
                notes = exercise.notes?.takeIf { it.isNotBlank() },
                // 🔥 关键修复：保持动作库JSON数据（imageUrl, videoUrl等）
                imageUrl = exercise.imageUrl,
                videoUrl = exercise.videoUrl,
                // 🔥 关键修复：映射customSets数据，防止数据丢失
                customSets = exercise.customSets.map { set ->
                    TemplateSet(
                        setNumber = set.setNumber,
                        targetReps = set.targetReps,
                        targetWeight = set.targetWeight,
                        restTimeSeconds = set.restTimeSeconds,
                        targetDuration = set.targetDuration,
                        rpe = set.rpe,
                    )
                },
            )
        },
        difficulty = mapDtoDifficultyToDomain(this.difficulty),
        estimatedDuration = this.metadata?.estimatedDuration,
        targetMuscleGroups = this.metadata?.targetMuscleGroups ?: emptyList(),
        userId = "unknown",
        isPublic = this.actualIsPublished, // 保持原有逻辑用于向后兼容
        isFavorite = false,
        tags = this.metadata?.tags ?: emptyList(),
        createdAt = this.createdAt,
        updatedAt = this.updatedAt,
        // Phase1 版本控制字段 - 🔥 修复：添加新字段的映射
        usageCount = 0,
        isDraft = this.actualIsDraft,
        currentVersion = this.actualCurrentVersion,
        isPublished = this.actualIsPublished,
        lastPublishedAt = this.lastPublishedAt,
    )

/**
 * TemplateExercise Domain到DTO转换
 */
fun TemplateExercise.toDto(): TemplateExerciseDto =
    TemplateExerciseDto(
        id = this.id,
        exerciseId = this.exerciseId,
        exerciseName = this.name,
        // 🔥 关键修复：保持动作库JSON数据（imageUrl, videoUrl等）
        imageUrl = this.imageUrl,
        videoUrl = this.videoUrl,
        sets = this.sets,
        reps = this.reps,
        targetWeight = this.weight,
        restTimeSeconds = this.restSeconds,
        notes = this.notes,
    )

/**
 * TemplateExerciseDto到Domain转换
 */
fun TemplateExerciseDto.toDomain(): TemplateExercise =
    TemplateExercise(
        id =
        this.id ?: java.util.UUID
            .randomUUID()
            .toString(),
        exerciseId = this.exerciseId,
        name = this.exerciseName ?: "Unknown Exercise",
        order = 0,
        sets = this.sets,
        reps = this.reps,
        restSeconds = this.restTimeSeconds,
        weight = this.targetWeight,
        notes = this.notes?.takeIf { it.isNotBlank() },
        // 🔥 关键修复：保持动作库JSON数据（imageUrl, videoUrl等）
        imageUrl = this.imageUrl,
        videoUrl = this.videoUrl,
    )

// ==================== 辅助映射函数 ====================

/**
 * 根据肌群推断训练分类
 */
private fun inferCategoryFromMuscleGroups(muscleGroups: List<String>): TemplateCategory =
    when {
        muscleGroups.any { it.contains("心", ignoreCase = true) || it.contains("cardio", ignoreCase = true) } ->
            TemplateCategory.CARDIO
        muscleGroups.any { it.contains("灵活", ignoreCase = true) || it.contains("拉伸", ignoreCase = true) } ->
            TemplateCategory.FLEXIBILITY
        muscleGroups.any { it.contains("上肢", ignoreCase = true) || it.contains("上臂", ignoreCase = true) } ->
            TemplateCategory.UPPER_BODY
        muscleGroups.any { it.contains("下肢", ignoreCase = true) || it.contains("腿", ignoreCase = true) } ->
            TemplateCategory.LOWER_BODY
        muscleGroups.any { it.contains("核心", ignoreCase = true) || it.contains("腹", ignoreCase = true) } ->
            TemplateCategory.CORE
        muscleGroups.any { it.contains("全身", ignoreCase = true) } ->
            TemplateCategory.FULL_BODY
        else -> TemplateCategory.STRENGTH
    }

/**
 * 从分类提取肌群列表
 */
private fun extractMuscleGroupsFromCategory(category: TemplateCategory): List<String> =
    when (category) {
        TemplateCategory.STRENGTH -> listOf("全身力量")
        TemplateCategory.CARDIO -> listOf("心肺耐力")
        TemplateCategory.FLEXIBILITY -> listOf("柔韧性")
        TemplateCategory.MIXED -> listOf("混合训练")
        TemplateCategory.REHABILITATION -> listOf("康复训练")
        TemplateCategory.UPPER_BODY -> listOf("上肢训练")
        TemplateCategory.LOWER_BODY -> listOf("下肢训练")
        TemplateCategory.CORE -> listOf("核心训练")
        TemplateCategory.FULL_BODY -> listOf("全身训练")
        TemplateCategory.CUSTOM -> listOf("自定义训练")
    }

/**
 * 难度映射到整数
 */
private fun mapDifficultyToInt(difficulty: Difficulty): Int =
    when (difficulty) {
        Difficulty.EASY -> 2
        Difficulty.MEDIUM -> 3
        Difficulty.HARD -> 4
        Difficulty.EXPERT -> 5
    }

/**
 * 计算预估训练时长
 */
private fun calculateEstimatedDuration(exercises: List<TemplateExerciseDto>): Int {
    // 简单估算：每个动作 3-5分钟，包含热身和拉伸
    val baseTime = exercises.size * 4
    val restTime = exercises.sumOf { it.restTimeSeconds } / 60
    return (baseTime + restTime + 10).coerceAtLeast(20) // 最少20分钟
}

/**
 * 从模板ID提取用户ID
 */
private fun extractUserIdFromTemplateId(templateId: String): String {
    // 简化处理，实际应该有更复杂的逻辑
    return if (templateId.startsWith("tmpl_")) {
        templateId.split("_").getOrNull(1) ?: "unknown"
    } else {
        "unknown"
    }
}

/**
 * 解析次数字符串为整数
 */
private fun parseRepsFromString(repsPerSet: String): Int =
    try {
        // 处理范围格式如"8-12"，取中值
        if (repsPerSet.contains("-")) {
            val parts = repsPerSet.split("-")
            (parts[0].toInt() + parts[1].toInt()) / 2
        } else {
            repsPerSet.toInt()
        }
    } catch (e: Exception) {
        10 // 默认值
    }

/**
 * 解析重量建议为浮点数
 */
private fun parseWeightFromSuggestion(weightSuggestion: String?): Float? =
    try {
        weightSuggestion?.let {
            // 提取数字部分
            val number = it.filter { char -> char.isDigit() || char == '.' }
            if (number.isNotEmpty()) number.toFloat() else null
        }
    } catch (e: Exception) {
        null
    }

/**
 * Domain分类映射到DTO分类
 */
private fun mapDomainCategoryToDto(category: String?): TemplateCategory =
    when (category?.uppercase()) {
        "STRENGTH" -> TemplateCategory.STRENGTH
        "CARDIO" -> TemplateCategory.CARDIO
        "FLEXIBILITY" -> TemplateCategory.FLEXIBILITY
        "MIXED" -> TemplateCategory.MIXED
        "REHABILITATION" -> TemplateCategory.REHABILITATION
        "UPPER_BODY" -> TemplateCategory.UPPER_BODY
        "LOWER_BODY" -> TemplateCategory.LOWER_BODY
        "CORE" -> TemplateCategory.CORE
        "FULL_BODY" -> TemplateCategory.FULL_BODY
        "CUSTOM" -> TemplateCategory.CUSTOM
        else -> TemplateCategory.CUSTOM
    }

/**
 * DTO分类映射到Domain分类
 */
private fun mapDtoCategoryToDomain(category: TemplateCategory): String =
    when (category) {
        TemplateCategory.STRENGTH -> "strength"
        TemplateCategory.CARDIO -> "cardio"
        TemplateCategory.FLEXIBILITY -> "flexibility"
        TemplateCategory.MIXED -> "mixed"
        TemplateCategory.REHABILITATION -> "rehabilitation"
        TemplateCategory.UPPER_BODY -> "upper_body"
        TemplateCategory.LOWER_BODY -> "lower_body"
        TemplateCategory.CORE -> "core"
        TemplateCategory.FULL_BODY -> "full_body"
        TemplateCategory.CUSTOM -> "custom"
    }

/**
 * Domain难度映射到DTO难度
 */
private fun mapDomainDifficultyToDto(difficulty: Int?): Difficulty =
    when (difficulty) {
        1, 2 -> Difficulty.EASY
        3 -> Difficulty.MEDIUM
        4 -> Difficulty.HARD
        5 -> Difficulty.EXPERT
        else -> Difficulty.MEDIUM
    }

/**
 * DTO难度映射到Domain难度
 */
private fun mapDtoDifficultyToDomain(difficulty: Difficulty): Int =
    when (difficulty) {
        Difficulty.EASY -> 2
        Difficulty.MEDIUM -> 3
        Difficulty.HARD -> 4
        Difficulty.EXPERT -> 5
    }

/**
 * Phase1 兼容性扩展属性
 * 为Domain模型提供Version控制字段的默认值
 */
private val WorkoutTemplate.actualIsDraft: Boolean
    get() = true // 默认为草稿状态

private val WorkoutTemplate.actualCurrentVersion: Int
    get() = 1 // 默认版本号

private val WorkoutTemplate.actualIsPublished: Boolean
    get() = this.isPublic // 使用isPublic字段映射

private val WorkoutTemplate.actualLastPublishedAt: Long?
    get() = if (this.isPublic) this.updatedAt else null // 如果已发布，使用更新时间

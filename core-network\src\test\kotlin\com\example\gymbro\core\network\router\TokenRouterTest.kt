package com.example.gymbro.core.network.router

import kotlinx.coroutines.*
import kotlinx.coroutines.test.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import timber.log.Timber

/**
 * TokenRouter 单元测试
 *
 * 🔥 多轮对话架构升级测试套件：
 * - 验证ConversationScope的创建、获取和释放功能
 * - 确保并发安全性和资源管理
 * - 测试Idle-Timeout清理机制
 * - 验证token和事件路由的正确性
 */
@OptIn(ExperimentalCoroutinesApi::class)
class TokenRouterTest {

    private lateinit var testScope: TestScope
    private lateinit var tokenRouter: TokenRouter

    @BeforeEach
    fun setUp() {
        // 设置测试协程作用域
        testScope = TestScope()

        // 创建TokenRouter实例，使用测试作用域
        tokenRouter = TokenRouter(testScope)

        // 配置Timber用于测试（可选）
        Timber.plant(object : Timber.Tree() {
            override fun log(priority: Int, tag: String?, message: String, t: Throwable?) {
                println("[$tag] $message")
            }
        })
    }

    @AfterEach
    fun tearDown() {
        // 清理所有作用域
        tokenRouter.releaseAll()

        // 移除Timber树
        Timber.uprootAll()
    }

    @Nested
    @DisplayName("ConversationScope 生命周期管理")
    inner class ConversationScopeLifecycle {

        @Test
        @DisplayName("应该能够创建新的ConversationScope")
        fun shouldCreateNewConversationScope() {
            // Given
            val messageId = "test-message-1"

            // When
            val scope = tokenRouter.getOrCreateScope(messageId)

            // Then
            assertNotNull(scope)
            assertEquals(messageId, scope.messageId)
            assertTrue(scope.isActive())
            assertEquals(1, tokenRouter.getActiveScopeCount())
        }

        @Test
        @DisplayName("相同messageId应该返回同一个ConversationScope")
        fun shouldReturnSameConversationScopeForSameMessageId() {
            // Given
            val messageId = "test-message-1"

            // When
            val scope1 = tokenRouter.getOrCreateScope(messageId)
            val scope2 = tokenRouter.getOrCreateScope(messageId)

            // Then
            assertSame(scope1, scope2)
            assertEquals(1, tokenRouter.getActiveScopeCount())
        }

        @Test
        @DisplayName("不同messageId应该创建不同的ConversationScope")
        fun shouldCreateDifferentConversationScopesForDifferentMessageIds() {
            // Given
            val messageId1 = "test-message-1"
            val messageId2 = "test-message-2"

            // When
            val scope1 = tokenRouter.getOrCreateScope(messageId1)
            val scope2 = tokenRouter.getOrCreateScope(messageId2)

            // Then
            assertNotSame(scope1, scope2)
            assertEquals(messageId1, scope1.messageId)
            assertEquals(messageId2, scope2.messageId)
            assertEquals(2, tokenRouter.getActiveScopeCount())
        }

        @Test
        @DisplayName("应该能够正确释放ConversationScope")
        fun shouldReleaseConversationScopeCorrectly() {
            // Given
            val messageId = "test-message-1"
            val scope = tokenRouter.getOrCreateScope(messageId)
            assertTrue(scope.isActive())

            // When
            tokenRouter.releaseScope(messageId)

            // Then
            assertEquals(0, tokenRouter.getActiveScopeCount())
            assertFalse(scope.isActive())
            assertNull(tokenRouter.getScope(messageId))
        }

        @Test
        @DisplayName("释放不存在的scope应该安全处理")
        fun shouldSafelyHandleReleasingNonExistentScope() {
            // Given
            val messageId = "non-existent-message"

            // When & Then (不应该抛出异常)
            assertDoesNotThrow {
                tokenRouter.releaseScope(messageId)
            }
            assertEquals(0, tokenRouter.getActiveScopeCount())
        }

        @Test
        @DisplayName("应该能够释放所有ConversationScope")
        fun shouldReleaseAllConversationScopes() {
            // Given
            val messageIds = listOf("msg-1", "msg-2", "msg-3")
            val scopes = messageIds.map { tokenRouter.getOrCreateScope(it) }
            assertEquals(3, tokenRouter.getActiveScopeCount())

            // When
            tokenRouter.releaseAll()

            // Then
            assertEquals(0, tokenRouter.getActiveScopeCount())
            scopes.forEach { scope ->
                assertFalse(scope.isActive())
            }
        }
    }

    @Nested
    @DisplayName("Token 和事件路由")
    inner class TokenAndEventRouting {

        @Test
        @DisplayName("应该能够正确路由token到指定的ConversationScope")
        fun shouldRouteTokenToCorrectConversationScope() = testScope.runTest {
            // Given
            val messageId = "test-message-1"
            val token = "Hello, world!"
            val scope = tokenRouter.getOrCreateScope(messageId)

            val receivedTokens = mutableListOf<String>()
            val job = launch {
                scope.tokens.collect { receivedTokens.add(it) }
            }

            // When
            tokenRouter.routeToken(messageId, token)

            // 等待一小段时间让Flow处理
            advanceTimeBy(100)

            // Then
            assertEquals(1, receivedTokens.size)
            assertEquals(token, receivedTokens[0])

            job.cancel()
        }

        @Test
        @DisplayName("应该能够正确路由事件到指定的ConversationScope")
        fun shouldRouteEventToCorrectConversationScope() = testScope.runTest {
            // Given
            val messageId = "test-message-1"
            val event = "TestEvent"
            val scope = tokenRouter.getOrCreateScope(messageId)

            val receivedEvents = mutableListOf<Any>()
            val job = launch {
                scope.events.collect { receivedEvents.add(it) }
            }

            // When
            tokenRouter.routeEvent(messageId, event)

            // 等待一小段时间让Flow处理
            advanceTimeBy(100)

            // Then
            assertEquals(1, receivedEvents.size)
            assertEquals(event, receivedEvents[0])

            job.cancel()
        }

        @Test
        @DisplayName("不同ConversationScope的token流应该相互隔离")
        fun shouldIsolateTokenStreamsBetweenConversationScopes() = testScope.runTest {
            // Given
            val messageId1 = "test-message-1"
            val messageId2 = "test-message-2"
            val token1 = "Token for message 1"
            val token2 = "Token for message 2"

            val scope1 = tokenRouter.getOrCreateScope(messageId1)
            val scope2 = tokenRouter.getOrCreateScope(messageId2)

            val receivedTokens1 = mutableListOf<String>()
            val receivedTokens2 = mutableListOf<String>()

            val job1 = launch { scope1.tokens.collect { receivedTokens1.add(it) } }
            val job2 = launch { scope2.tokens.collect { receivedTokens2.add(it) } }

            // When
            tokenRouter.routeToken(messageId1, token1)
            tokenRouter.routeToken(messageId2, token2)

            advanceTimeBy(100)

            // Then
            assertEquals(1, receivedTokens1.size)
            assertEquals(token1, receivedTokens1[0])

            assertEquals(1, receivedTokens2.size)
            assertEquals(token2, receivedTokens2[0])

            job1.cancel()
            job2.cancel()
        }
    }

    @Nested
    @DisplayName("并发安全性")
    inner class ConcurrencySafety {

        @Test
        @DisplayName("并发创建相同messageId的scope应该返回同一个实例")
        fun shouldReturnSameInstanceWhenConcurrentlyCreatingSameMessageId() = testScope.runTest {
            // Given
            val messageId = "concurrent-test"
            val scopes = mutableListOf<ConversationScope>()

            // When - 并发创建多个相同messageId的scope
            val jobs = (1..10).map {
                launch {
                    val scope = tokenRouter.getOrCreateScope(messageId)
                    synchronized(scopes) {
                        scopes.add(scope)
                    }
                }
            }

            jobs.forEach { it.join() }

            // Then - 所有scope应该是同一个实例
            assertEquals(10, scopes.size)
            val firstScope = scopes[0]
            scopes.forEach { scope ->
                assertSame(firstScope, scope)
            }
            assertEquals(1, tokenRouter.getActiveScopeCount())
        }

        @Test
        @DisplayName("并发路由token应该安全处理")
        fun shouldSafelyConcurrentlyRouteTokens() = testScope.runTest {
            // Given
            val messageId = "concurrent-token-test"
            val scope = tokenRouter.getOrCreateScope(messageId)
            val tokenCount = 100

            val receivedTokens = mutableListOf<String>()
            val job = launch {
                scope.tokens.collect {
                    synchronized(receivedTokens) {
                        receivedTokens.add(it)
                    }
                }
            }

            // When - 并发发送多个token
            val routingJobs = (1..tokenCount).map { index ->
                launch {
                    tokenRouter.routeToken(messageId, "token-$index")
                }
            }

            routingJobs.forEach { it.join() }
            advanceTimeBy(1000)

            // Then
            assertEquals(tokenCount, receivedTokens.size)

            job.cancel()
        }
    }

    @Nested
    @DisplayName("调试和监控")
    inner class DebuggingAndMonitoring {

        @Test
        @DisplayName("应该正确报告活跃scope数量")
        fun shouldCorrectlyReportActiveScopeCount() {
            // Given & When & Then
            assertEquals(0, tokenRouter.getActiveScopeCount())

            tokenRouter.getOrCreateScope("msg-1")
            assertEquals(1, tokenRouter.getActiveScopeCount())

            tokenRouter.getOrCreateScope("msg-2")
            assertEquals(2, tokenRouter.getActiveScopeCount())

            tokenRouter.releaseScope("msg-1")
            assertEquals(1, tokenRouter.getActiveScopeCount())

            tokenRouter.releaseAll()
            assertEquals(0, tokenRouter.getActiveScopeCount())
        }

        @Test
        @DisplayName("应该正确报告活跃scope ID列表")
        fun shouldCorrectlyReportActiveScopeIds() {
            // Given
            val messageIds = listOf("msg-1", "msg-2", "msg-3")
            messageIds.forEach { tokenRouter.getOrCreateScope(it) }

            // When
            val activeScopeIds = tokenRouter.getActiveScopeIds()

            // Then
            assertEquals(3, activeScopeIds.size)
            messageIds.forEach { messageId ->
                assertTrue(activeScopeIds.contains(messageId))
            }
        }

        @Test
        @DisplayName("应该正确检查scope活跃状态")
        fun shouldCorrectlyCheckScopeActiveStatus() {
            // Given
            val messageId = "status-test"

            // When & Then
            assertFalse(tokenRouter.isScopeActive(messageId))

            tokenRouter.getOrCreateScope(messageId)
            assertTrue(tokenRouter.isScopeActive(messageId))

            tokenRouter.releaseScope(messageId)
            assertFalse(tokenRouter.isScopeActive(messageId))
        }

        @Test
        @DisplayName("应该提供有用的调试信息")
        fun shouldProvideUsefulDebugInfo() {
            // Given
            tokenRouter.getOrCreateScope("debug-test-1")
            tokenRouter.getOrCreateScope("debug-test-2")

            // When
            val debugInfo = tokenRouter.getDebugInfo()

            // Then
            assertNotNull(debugInfo)
            assertTrue(debugInfo.contains("Active Scopes: 2"))
            assertTrue(debugInfo.contains("debug-test-1"))
            assertTrue(debugInfo.contains("debug-test-2"))
        }
    }
}

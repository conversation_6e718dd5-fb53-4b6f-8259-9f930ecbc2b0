package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.domain.shared.base.modern.ModernUseCase
import com.example.gymbro.domain.workout.model.template.TemplateVersion
import com.example.gymbro.domain.workout.model.template.WorkoutTemplate
import com.example.gymbro.domain.workout.repository.SessionRepository
import com.example.gymbro.domain.workout.repository.TemplateRepository
import com.example.gymbro.domain.workout.usecase.template.TemplateVersionUseCase
import kotlinx.coroutines.CoroutineDispatcher
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 将 Session 中的更改应用到 Template UseCase
 *
 * 功能：
 * - 检测 Session 中的 exerciseOverrides（休息时间等修改）
 * - 将这些修改应用到原始 Template
 * - 创建新的 TemplateVersion 保存更改
 * - 支持版本控制和历史追溯
 *
 * 使用场景：
 * - Session 完成后，用户选择将修改保存到 Template
 * - 用户在 Session 中调整了休息时间等参数，希望更新模板
 *
 * 版本控制：
 * - 创建新的 TemplateVersion 而不是直接修改原模板
 * - 保持历史版本的完整性和可追溯性
 */
@Singleton
class ApplySessionChangesToTemplateUseCase
@Inject
constructor(
    private val sessionRepository: SessionRepository,
    private val templateRepository: TemplateRepository,
    private val templateVersionUseCase: TemplateVersionUseCase,
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    logger: Logger,
) : ModernUseCase<ApplySessionChangesToTemplateUseCase.Params, TemplateVersion>(dispatcher, logger) {
    /**
     * 参数定义
     */
    data class Params(
        val sessionId: String,
        val templateId: String,
        val exerciseOverrides: Map<String, Int>, // exerciseId -> restSecondsOverride
        val description: String? = null, // 版本描述
    )

    override suspend fun execute(params: Params): ModernResult<TemplateVersion> {
        logger.d("应用Session更改到Template: sessionId=${params.sessionId}, templateId=${params.templateId}")

        // 1. 验证参数
        if (params.sessionId.isBlank() || params.templateId.isBlank()) {
            return ModernResult.Error(
                ModernDataError(
                    operationName = "applySessionChangesToTemplate",
                    errorType = GlobalErrorType.Validation.InvalidInput,
                    uiMessage = UiText.DynamicString("Session ID 和 Template ID 不能为空"),
                ),
            )
        }

        if (params.exerciseOverrides.isEmpty()) {
            return ModernResult.Error(
                ModernDataError(
                    operationName = "applySessionChangesToTemplate",
                    errorType = GlobalErrorType.Validation.InvalidInput,
                    uiMessage = UiText.DynamicString("没有检测到需要应用的更改"),
                ),
            )
        }

        // 2. 获取原始 Template
        val templateResult = templateRepository.getTemplateById(params.templateId)
        if (templateResult !is ModernResult.Success) {
            logger.e("获取Template失败: $templateResult")
            return ModernResult.Error(
                ModernDataError(
                    operationName = "applySessionChangesToTemplate",
                    errorType = GlobalErrorType.Business.NotFound,
                    uiMessage = UiText.DynamicString("模板不存在"),
                ),
            )
        }

        val originalTemplate = templateResult.data
        if (originalTemplate == null) {
            logger.e("Template不存在: templateId=${params.templateId}")
            return ModernResult.Error(
                ModernDataError(
                    operationName = "applySessionChangesToTemplate",
                    errorType = GlobalErrorType.Business.NotFound,
                    uiMessage = UiText.DynamicString("模板不存在"),
                ),
            )
        }

        // 3. 应用 Session 中的更改到 Template
        val updatedTemplate = applyOverridesToTemplate(originalTemplate, params.exerciseOverrides)

        // 4. 创建新的 TemplateVersion
        val versionDescription =
            params.description
                ?: "从训练会话应用更改 (Session: ${params.sessionId.take(8)})"

        val createVersionResult =
            templateVersionUseCase.createVersion.invoke(
                TemplateVersionUseCase.CreateVersionParams(
                    templateId = params.templateId,
                    description = versionDescription,
                ),
            )

        if (createVersionResult !is ModernResult.Success) {
            logger.e("创建TemplateVersion失败: $createVersionResult")
            return ModernResult.Error(
                ModernDataError(
                    operationName = "applySessionChangesToTemplate",
                    errorType = GlobalErrorType.Business.InvalidOperation,
                    uiMessage = UiText.DynamicString("保存模板版本失败"),
                ),
            )
        }

        val newVersion = createVersionResult.data

        // 5. 更新当前 Template 为新版本内容
        val updateResult = templateRepository.updateTemplate(updatedTemplate)
        if (updateResult !is ModernResult.Success) {
            logger.w("更新Template失败，但版本已创建: $updateResult")
            // 即使更新失败，版本已经创建，可以继续
        }

        logger.d("成功应用Session更改到Template: 新版本=${newVersion.versionNumber}")
        return ModernResult.Success(newVersion)
    }

    /**
     * 将 exerciseOverrides 应用到 Template
     */
    private fun applyOverridesToTemplate(
        template: WorkoutTemplate,
        exerciseOverrides: Map<String, Int>,
    ): WorkoutTemplate {
        val updatedExercises =
            template.exercises.map { exercise ->
                val restSecondsOverride = exerciseOverrides[exercise.exerciseId]
                if (restSecondsOverride != null) {
                    // 应用休息时间覆盖
                    exercise.copy(
                        restSeconds = restSecondsOverride,
                        // 可以在这里添加其他字段的覆盖逻辑
                    )
                } else {
                    exercise
                }
            }

        return template.copy(
            exercises = updatedExercises,
            updatedAt = System.currentTimeMillis(),
        )
    }

    /**
     * 检测 Session 和 Template 之间的差异
     */
    data class TemplateChange(
        val exerciseId: String,
        val field: String, // "restSeconds", "sets", etc.
        val originalValue: Any,
        val newValue: Any,
    )

    /**
     * 检测更改的辅助方法
     */
    fun detectChanges(
        originalTemplate: WorkoutTemplate,
        exerciseOverrides: Map<String, Int>,
    ): List<TemplateChange> =
        exerciseOverrides.mapNotNull { (exerciseId, newRestSeconds) ->
            val originalExercise = originalTemplate.exercises.find { it.exerciseId == exerciseId }
            if (originalExercise != null && originalExercise.restSeconds != newRestSeconds) {
                TemplateChange(
                    exerciseId = exerciseId,
                    field = "restSeconds",
                    originalValue = originalExercise.restSeconds,
                    newValue = newRestSeconds,
                )
            } else {
                null
            }
        }
}

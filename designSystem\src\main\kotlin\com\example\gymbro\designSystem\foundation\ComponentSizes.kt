package com.example.gymbro.designSystem.foundation

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

/**
 * GymBro设计系统组件尺寸标准
 *
 * ⚠️ **已弃用**: 此文件正在迁移到统一的 Token 系统
 * 请使用 `Tokens.*` 替代这里的定义
 *
 * 迁移指南:
 * - ButtonHeight → Tokens.Button.HeightPrimary
 * - InputFieldHeight → Tokens.Input.HeightStandard
 * - IconSize → Tokens.Icon.Standard
 * - CardMinHeight → Tokens.Card.HeightMin
 *
 * @deprecated 使用 Tokens 系统替代
 */
@Deprecated(
    message = "使用 Tokens 系统替代。例如: Tokens.Button.HeightPrimary",
    replaceWith =
    ReplaceWith(
        "Tokens.Button.HeightPrimary",
        "com.example.gymbro.designSystem.theme.tokens.Tokens",
    ),
)
object GymBroComponentSizes {

    // === 按钮尺寸 ===
    @Deprecated("使用 Tokens.Button.HeightPrimary", ReplaceWith("Tokens.Button.HeightPrimary"))
    val ButtonHeight = 56.dp // 标准按钮高度

    @Deprecated("使用 Tokens.Button.HeightSmall", ReplaceWith("Tokens.Button.HeightSmall"))
    val SmallButtonHeight = 40.dp // 小按钮高度

    @Deprecated("使用 Tokens.Button.HeightLarge", ReplaceWith("Tokens.Button.HeightLarge"))
    val LargeButtonHeight = 64.dp // 大按钮高度

    @Deprecated("使用 Tokens.Button.HeightCompact", ReplaceWith("Tokens.Button.HeightCompact"))
    val CompactButtonHeight = 32.dp // 紧凑按钮高度（工具栏等）

    // === 输入框尺寸 ===
    @Deprecated("使用 Tokens.Input.HeightStandard", ReplaceWith("Tokens.Input.HeightStandard"))
    val InputFieldHeight = 56.dp // 标准输入框高度

    @Deprecated("使用 Tokens.Input.HeightSmall", ReplaceWith("Tokens.Input.HeightSmall"))
    val SmallInputFieldHeight = 40.dp // 小输入框高度

    @Deprecated("使用 Tokens.Input.HeightLarge", ReplaceWith("Tokens.Input.HeightLarge"))
    val LargeInputFieldHeight = 64.dp // 大输入框高度

    // === 图标尺寸 ===
    @Deprecated("使用 Tokens.Icon.Standard", ReplaceWith("Tokens.Icon.Standard"))
    val IconSize = 24.dp // 标准图标尺寸

    @Deprecated("使用 Tokens.Icon.Small", ReplaceWith("Tokens.Icon.Small"))
    val SmallIconSize = 16.dp // 小图标尺寸

    @Deprecated("使用 Tokens.Icon.Large", ReplaceWith("Tokens.Icon.Large"))
    val LargeIconSize = 32.dp // 大图标尺寸

    @Deprecated("使用 Tokens.Icon.XXLarge", ReplaceWith("Tokens.Icon.XXLarge"))
    val ExtraLargeIconSize = 48.dp // 特大图标尺寸

    @Deprecated("使用 Tokens.Icon.NavigationIcon", ReplaceWith("Tokens.Icon.NavigationIcon"))
    val NavigationIconSize = 24.dp // 导航图标尺寸

    @Deprecated("使用 Tokens.Icon.ActionIcon", ReplaceWith("Tokens.Icon.ActionIcon"))
    val ActionIconSize = 24.dp // 动作图标尺寸

    // === 卡片尺寸 ===
    @Deprecated("使用 Tokens.Card.HeightMin", ReplaceWith("Tokens.Card.HeightMin"))
    val CardMinHeight = 80.dp // 卡片最小高度

    @Deprecated("使用 Tokens.Card.HeightSmall", ReplaceWith("Tokens.Card.HeightSmall"))
    val SmallCardHeight = 120.dp // 小卡片高度

    @Deprecated("使用 Tokens.Card.HeightMedium", ReplaceWith("Tokens.Card.HeightMedium"))
    val MediumCardHeight = 160.dp // 中等卡片高度

    @Deprecated("使用 Tokens.Card.HeightLarge", ReplaceWith("Tokens.Card.HeightLarge"))
    val LargeCardHeight = 200.dp // 大卡片高度

    // === 列表项尺寸 ===
    val ListItemHeight = 72.dp // 标准列表项高度
    val SmallListItemHeight = 56.dp // 小列表项高度
    val LargeListItemHeight = 88.dp // 大列表项高度
    val ExpandedListItemHeight = 104.dp // 展开列表项高度

    // === 应用栏尺寸 ===
    val TopAppBarHeight = 64.dp // 顶部应用栏高度
    val CompactTopAppBarHeight = 48.dp // 紧凑顶部应用栏高度
    val BottomBarHeight = 80.dp // 底部导航栏高度
    val TabBarHeight = 48.dp // 标签栏高度

    // === FAB尺寸 ===
    val FABSize = 56.dp // 标准FAB尺寸
    val SmallFABSize = 40.dp // 小FAB尺寸
    val LargeFABSize = 96.dp // 大FAB尺寸
    val ExtendedFABHeight = 56.dp // 扩展FAB高度

    // === 进度指示器尺寸 ===
    val ProgressIndicatorSize = 48.dp // 标准进度指示器尺寸
    val SmallProgressIndicatorSize = 24.dp // 小进度指示器尺寸
    val LargeProgressIndicatorSize = 72.dp // 大进度指示器尺寸
    val StrokeWidth = 4.dp // 进度指示器线条宽度
    val SmallStrokeWidth = 2.dp // 小线条宽度
    val LargeStrokeWidth = 6.dp // 大线条宽度

    // === 头像尺寸 ===
    val AvatarSize = 40.dp // 标准头像尺寸
    val SmallAvatarSize = 24.dp // 小头像尺寸
    val LargeAvatarSize = 56.dp // 大头像尺寸
    val ExtraLargeAvatarSize = 96.dp // 特大头像尺寸

    // === 触摸目标 ===
    val MinTouchTarget = 48.dp // 最小触摸目标（无障碍要求）
    val ComfortableTouchTarget = 56.dp // 舒适触摸目标
    val LargeTouchTarget = 64.dp // 大触摸目标

    // === 分割线尺寸 ===
    val DividerThickness = 1.dp // 分割线厚度
    val ThickDividerThickness = 2.dp // 粗分割线厚度

    // === 角标尺寸 ===
    val BadgeSize = 16.dp // 角标尺寸
    val LargeBadgeSize = 20.dp // 大角标尺寸
    val DotBadgeSize = 8.dp // 点状角标尺寸

    // === 开关控件尺寸 ===
    val SwitchWidth = 52.dp // 开关宽度
    val SwitchHeight = 32.dp // 开关高度
    val SwitchThumbSize = 24.dp // 开关拇指尺寸

    // === 滑块尺寸 ===
    val SliderThumbSize = 24.dp // 滑块拇指尺寸
    val SliderTrackHeight = 4.dp // 滑块轨道高度
    val SliderActiveTrackHeight = 6.dp // 滑块激活轨道高度
}

/**
 * 语义化尺寸别名
 * 提供更具语义的尺寸命名，便于使用
 */
object GymBroSemanticSizes {
    // 按钮语义尺寸
    val PrimaryButtonHeight = GymBroComponentSizes.ButtonHeight
    val SecondaryButtonHeight = GymBroComponentSizes.SmallButtonHeight
    val CallToActionButtonHeight = GymBroComponentSizes.LargeButtonHeight

    // 输入语义尺寸
    val FormInputHeight = GymBroComponentSizes.InputFieldHeight
    val SearchInputHeight = GymBroComponentSizes.InputFieldHeight
    val FilterInputHeight = GymBroComponentSizes.SmallInputFieldHeight

    // 卡片语义尺寸
    val WorkoutCardHeight = GymBroComponentSizes.LargeCardHeight
    val ProfileCardHeight = GymBroComponentSizes.MediumCardHeight
    val StatisticsCardHeight = GymBroComponentSizes.SmallCardHeight

    // 列表语义尺寸
    val UserListItemHeight = GymBroComponentSizes.ListItemHeight
    val ExerciseListItemHeight = GymBroComponentSizes.LargeListItemHeight
    val SettingsListItemHeight = GymBroComponentSizes.SmallListItemHeight

    // 头像语义尺寸
    val ProfileAvatarSize = GymBroComponentSizes.ExtraLargeAvatarSize
    val MessageAvatarSize = GymBroComponentSizes.AvatarSize
    val ListItemAvatarSize = GymBroComponentSizes.LargeAvatarSize
}

/**
 * 尺寸验证工具
 * 确保组件尺寸符合无障碍要求
 */
object SizeValidator {
    /**
     * 验证触摸目标是否满足无障碍要求
     */
    fun validateTouchTarget(size: Dp): Boolean {
        return size >= GymBroComponentSizes.MinTouchTarget
    }

    /**
     * 获取符合无障碍要求的最小尺寸
     */
    fun ensureMinTouchTarget(size: Dp): Dp {
        return maxOf(size, GymBroComponentSizes.MinTouchTarget)
    }

    /**
     * 验证文本可读性尺寸
     */
    fun validateTextContainerHeight(height: Dp): Boolean {
        return height >= 32.dp // 确保足够的文本显示空间
    }
}

/**
 * 响应式尺寸工具
 * 根据设备类型调整组件尺寸
 */
object ResponsiveSizes {
    /**
     * 根据设备类型获取按钮高度
     */
    fun getButtonHeight(isCompact: Boolean = false): Dp {
        return if (isCompact) {
            GymBroComponentSizes.SmallButtonHeight
        } else {
            GymBroComponentSizes.ButtonHeight
        }
    }

    /**
     * 根据设备类型获取应用栏高度
     */
    fun getAppBarHeight(isCompact: Boolean = false): Dp {
        return if (isCompact) {
            GymBroComponentSizes.CompactTopAppBarHeight
        } else {
            GymBroComponentSizes.TopAppBarHeight
        }
    }

    /**
     * 根据重要性获取图标尺寸
     */
    fun getIconSize(importance: IconImportance): Dp {
        return when (importance) {
            IconImportance.Low -> GymBroComponentSizes.SmallIconSize
            IconImportance.Medium -> GymBroComponentSizes.IconSize
            IconImportance.High -> GymBroComponentSizes.LargeIconSize
            IconImportance.Critical -> GymBroComponentSizes.ExtraLargeIconSize
        }
    }
}

/**
 * 图标重要性枚举
 */
enum class IconImportance {
    Low, // 装饰性图标
    Medium, // 标准功能图标
    High, // 重要操作图标
    Critical, // 关键警告图标
}

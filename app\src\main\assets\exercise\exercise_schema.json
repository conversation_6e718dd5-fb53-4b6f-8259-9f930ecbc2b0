{"$schema": "http://json-schema.org/draft-07/schema#", "title": "GymBro Exercise Library Seed Data Schema", "description": "JSON Schema for validating GymBro exercise library seed data format", "type": "object", "required": ["schemaVersion", "entity", "entityVersion", "generatedAt", "totalCount", "payload"], "properties": {"schemaVersion": {"type": "string", "pattern": "^\\d+\\.\\d+\\.\\d+$", "description": "Schema version in semver format"}, "entity": {"type": "string", "enum": ["EXERCISE"], "description": "Entity type identifier"}, "entityVersion": {"type": "integer", "minimum": 1, "description": "Entity version number"}, "generatedAt": {"type": "integer", "minimum": 0, "description": "Unix timestamp when data was generated"}, "totalCount": {"type": "integer", "minimum": 0, "description": "Total number of exercises in payload"}, "description": {"type": "string", "description": "Optional description of the dataset"}, "payload": {"type": "array", "items": {"$ref": "#/definitions/Exercise"}, "description": "Array of exercise objects"}}, "definitions": {"Exercise": {"type": "object", "required": ["id", "name", "muscleGroup", "category", "equipment", "source", "isCustom", "isOfficial", "contentHash", "createdAt", "updatedAt"], "properties": {"id": {"type": "string", "pattern": "^(off_[a-f0-9]{8}|u_[a-zA-Z0-9_]+_[a-f0-9]{8})$", "description": "Unique exercise identifier"}, "name": {"$ref": "#/definitions/LocalizedString", "description": "Exercise name in multiple languages"}, "description": {"$ref": "#/definitions/LocalizedString", "description": "Exercise description in multiple languages"}, "muscleGroup": {"$ref": "#/definitions/MuscleGroup", "description": "Primary muscle group"}, "category": {"$ref": "#/definitions/Category", "description": "Exercise category"}, "equipment": {"type": "array", "items": {"$ref": "#/definitions/Equipment"}, "minItems": 1, "description": "Required equipment"}, "targetMuscles": {"type": "array", "items": {"$ref": "#/definitions/MuscleGroup"}, "description": "Target muscle groups"}, "secondaryMuscles": {"type": "array", "items": {"$ref": "#/definitions/MuscleGroup"}, "description": "Secondary muscle groups"}, "difficulty": {"$ref": "#/definitions/Difficulty", "description": "Exercise difficulty level"}, "defaultSets": {"type": "integer", "minimum": 1, "maximum": 10, "default": 3, "description": "Default number of sets"}, "defaultReps": {"type": "integer", "minimum": 1, "maximum": 100, "default": 10, "description": "Default number of repetitions"}, "defaultRestTime": {"type": "integer", "minimum": 30, "maximum": 600, "default": 60, "description": "De<PERSON>ult rest time in seconds"}, "steps": {"type": "array", "items": {"$ref": "#/definitions/LocalizedString"}, "description": "Exercise execution steps"}, "tips": {"type": "array", "items": {"$ref": "#/definitions/LocalizedString"}, "description": "Training tips and advice"}, "media": {"$ref": "#/definitions/Media", "description": "Media resources for the exercise"}, "source": {"$ref": "#/definitions/Source", "description": "Exercise source"}, "isCustom": {"type": "boolean", "description": "Whether this is a custom exercise"}, "isOfficial": {"type": "boolean", "description": "Whether this is an official exercise"}, "contentHash": {"type": "string", "pattern": "^[a-f0-9]{16}$", "description": "Content hash for change detection"}, "createdAt": {"type": "integer", "minimum": 0, "description": "Creation timestamp"}, "updatedAt": {"type": "integer", "minimum": 0, "description": "Last update timestamp"}}}, "LocalizedString": {"type": "object", "required": ["zh"], "properties": {"zh": {"type": "string", "minLength": 1, "description": "Chinese text"}, "en": {"type": "string", "minLength": 1, "description": "English text"}}, "additionalProperties": false}, "Media": {"type": "object", "properties": {"thumbnail": {"type": "string", "format": "uri", "description": "Thumbnail image URL"}, "demo": {"type": "string", "format": "uri", "description": "Demo video URL"}, "duration": {"type": "integer", "minimum": 1, "description": "Media duration in seconds"}}, "additionalProperties": false}, "MuscleGroup": {"type": "string", "enum": ["CHEST", "BACK", "SHOULDERS", "ARMS", "LEGS", "CORE", "FULL_BODY"]}, "Category": {"type": "string", "enum": ["UPPER_BODY", "LOWER_BODY", "CORE", "FULL_BODY", "CARDIO"]}, "Equipment": {"type": "string", "enum": ["BARBELL", "DUMBBELL", "KETTLEBELL", "CABLE", "MACHINE", "BODYWEIGHT", "NONE"]}, "Difficulty": {"type": "string", "enum": ["BEGINNER", "INTERMEDIATE", "ADVANCED"]}, "Source": {"type": "string", "enum": ["OFFICIAL", "CUSTOM", "COMMUNITY"]}}}
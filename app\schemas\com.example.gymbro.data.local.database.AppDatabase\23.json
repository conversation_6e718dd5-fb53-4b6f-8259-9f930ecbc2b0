{"formatVersion": 1, "database": {"version": 23, "identityHash": "8c361a1b1d08b2b870bc26abc5030954", "entities": [{"tableName": "users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`user_id` TEXT NOT NULL, `email` TEXT, `username` TEXT NOT NULL, `displayName` TEXT, `photoUrl` TEXT, `phoneNumber` TEXT, `isActive` INTEGER NOT NULL, `isEmailVerified` INTEGER NOT NULL, `wechatId` TEXT, `anonymousId` TEXT, `gender` TEXT, `weight` REAL, `weightUnit` TEXT, `fitnessLevel` INTEGER, `fitnessGoalsJson` TEXT, `preferredGym` TEXT, `preferredWorkoutTimesJson` TEXT, `preferredFoodsJson` TEXT, `avatar` TEXT, `bio` TEXT, `trainingDaysJson` TEXT, `allowPartnerMatching` INTEGER NOT NULL, `totalWorkoutCount` INTEGER NOT NULL, `weeklyActiveMinutes` INTEGER NOT NULL, `likesReceived` INTEGER NOT NULL, `createdAtSeconds` INTEGER, `createdAtNanos` INTEGER, `lastLoginAtSeconds` INTEGER, `lastLoginAtNanos` INTEGER, `themeMode` TEXT NOT NULL, `languageCode` TEXT NOT NULL, `measurementSystem` TEXT NOT NULL, `notificationsEnabled` INTEGER NOT NULL, `soundsEnabled` INTEGER NOT NULL, `locationSharingEnabled` INTEGER NOT NULL, `privacySettingsJson` TEXT NOT NULL, `notificationSettingsJson` TEXT NOT NULL, `soundSettingsJson` TEXT NOT NULL, `backupSettingsJson` TEXT NOT NULL, `partnerMatchPreferencesJson` TEXT NOT NULL, `blockedUsersJson` TEXT NOT NULL, `settingsJson` TEXT NOT NULL, `isSynced` INTEGER NOT NULL, `lastSynced` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, `serverUpdatedAt` INTEGER NOT NULL, `isAnonymous` INTEGER NOT NULL, `userType` TEXT NOT NULL, `subscriptionPlan` TEXT NOT NULL, `subscriptionExpiryDate` INTEGER, PRIMARY KEY(`user_id`))", "fields": [{"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "email", "columnName": "email", "affinity": "TEXT", "notNull": false}, {"fieldPath": "username", "columnName": "username", "affinity": "TEXT", "notNull": true}, {"fieldPath": "displayName", "columnName": "displayName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "photoUrl", "columnName": "photoUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "phoneNumber", "columnName": "phoneNumber", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isEmailVerified", "columnName": "isEmailVerified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "wechatId", "columnName": "wechatId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "anonymousId", "columnName": "anonymousId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "gender", "columnName": "gender", "affinity": "TEXT", "notNull": false}, {"fieldPath": "weight", "columnName": "weight", "affinity": "REAL", "notNull": false}, {"fieldPath": "weightUnit", "columnName": "weightUnit", "affinity": "TEXT", "notNull": false}, {"fieldPath": "fitnessLevel", "columnName": "fitnessLevel", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "fitnessGoals<PERSON>son", "columnName": "fitnessGoals<PERSON>son", "affinity": "TEXT", "notNull": false}, {"fieldPath": "preferredGym", "columnName": "preferredGym", "affinity": "TEXT", "notNull": false}, {"fieldPath": "preferredWorkoutTimesJson", "columnName": "preferredWorkoutTimesJson", "affinity": "TEXT", "notNull": false}, {"fieldPath": "preferred<PERSON><PERSON>s<PERSON><PERSON>", "columnName": "preferred<PERSON><PERSON>s<PERSON><PERSON>", "affinity": "TEXT", "notNull": false}, {"fieldPath": "avatar", "columnName": "avatar", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bio", "columnName": "bio", "affinity": "TEXT", "notNull": false}, {"fieldPath": "trainingDaysJson", "columnName": "trainingDaysJson", "affinity": "TEXT", "notNull": false}, {"fieldPath": "allowPartnerMatching", "columnName": "allowPartnerMatching", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "totalWorkoutCount", "columnName": "totalWorkoutCount", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "weeklyActiveMinutes", "columnName": "weeklyActiveMinutes", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "likesReceived", "columnName": "likesReceived", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAtSeconds", "columnName": "createdAtSeconds", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "createdAtNanos", "columnName": "createdAtNanos", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastLoginAtSeconds", "columnName": "lastLoginAtSeconds", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "lastLoginAtNanos", "columnName": "lastLoginAtNanos", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "themeMode", "columnName": "themeMode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "languageCode", "columnName": "languageCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "measurementSystem", "columnName": "measurementSystem", "affinity": "TEXT", "notNull": true}, {"fieldPath": "notificationsEnabled", "columnName": "notificationsEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "soundsEnabled", "columnName": "soundsEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "locationSharingEnabled", "columnName": "locationSharingEnabled", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "privacySettingsJson", "columnName": "privacySettingsJson", "affinity": "TEXT", "notNull": true}, {"fieldPath": "notificationSettingsJson", "columnName": "notificationSettingsJson", "affinity": "TEXT", "notNull": true}, {"fieldPath": "soundSettingsJson", "columnName": "soundSettingsJson", "affinity": "TEXT", "notNull": true}, {"fieldPath": "backupSettings<PERSON><PERSON>", "columnName": "backupSettings<PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "partnerMatchPreferencesJson", "columnName": "partnerMatchPreferencesJson", "affinity": "TEXT", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "<PERSON><PERSON><PERSON>", "columnName": "<PERSON><PERSON><PERSON>", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastSynced", "columnName": "lastSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "serverUpdatedAt", "columnName": "serverUpdatedAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isAnonymous", "columnName": "isAnonymous", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userType", "columnName": "userType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "subscriptionPlan", "columnName": "subscriptionPlan", "affinity": "TEXT", "notNull": true}, {"fieldPath": "subscriptionExpiryDate", "columnName": "subscriptionExpiryDate", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": false, "columnNames": ["user_id"]}, "indices": [], "foreignKeys": []}, {"tableName": "favorite_items", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`fav_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `user_id` TEXT NOT NULL, `item_id` TEXT NOT NULL, `item_type` TEXT NOT NULL, `favorited_at` INTEGER NOT NULL)", "fields": [{"fieldPath": "favId", "columnName": "fav_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itemId", "columnName": "item_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "itemType", "columnName": "item_type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "favoritedAt", "columnName": "favorited_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["fav_id"]}, "indices": [{"name": "index_favorite_items_user_id", "unique": false, "columnNames": ["user_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_favorite_items_user_id` ON `${TABLE_NAME}` (`user_id`)"}, {"name": "index_favorite_items_item_type", "unique": false, "columnNames": ["item_type"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_favorite_items_item_type` ON `${TABLE_NAME}` (`item_type`)"}], "foreignKeys": []}, {"tableName": "blocked_users", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`blocker_user_id` TEXT NOT NULL, `blocked_user_id` TEXT NOT NULL, `blocked_at` INTEGER NOT NULL, PRIMARY KEY(`blocker_user_id`, `blocked_user_id`))", "fields": [{"fieldPath": "blockerUserId", "columnName": "blocker_user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "blockedUserId", "columnName": "blocked_user_id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "blockedAt", "columnName": "blocked_at", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["blocker_user_id", "blocked_user_id"]}, "indices": [{"name": "index_blocked_users_blocker_user_id", "unique": false, "columnNames": ["blocker_user_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_blocked_users_blocker_user_id` ON `${TABLE_NAME}` (`blocker_user_id`)"}, {"name": "index_blocked_users_blocked_user_id", "unique": false, "columnNames": ["blocked_user_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_blocked_users_blocked_user_id` ON `${TABLE_NAME}` (`blocked_user_id`)"}], "foreignKeys": []}, {"tableName": "subscriptions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`subscriptionId` TEXT NOT NULL, `userId` TEXT NOT NULL, `planType` TEXT NOT NULL, `subscriptionStatus` TEXT NOT NULL, `startDate` INTEGER NOT NULL, `endDate` INTEGER, `autoRenew` INTEGER NOT NULL, `paymentMethod` TEXT, `pricePaid` TEXT NOT NULL, `currency` TEXT NOT NULL, `planName` TEXT, `planDescription` TEXT, `planId` TEXT, `createdAt` INTEGER NOT NULL, `syncStatus` INTEGER NOT NULL, `isSynced` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, PRIMARY KEY(`subscriptionId`))", "fields": [{"fieldPath": "subscriptionId", "columnName": "subscriptionId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "planType", "columnName": "planType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "subscriptionStatus", "columnName": "subscriptionStatus", "affinity": "TEXT", "notNull": true}, {"fieldPath": "startDate", "columnName": "startDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endDate", "columnName": "endDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "autoRenew", "columnName": "autoRenew", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paymentMethod", "columnName": "paymentMethod", "affinity": "TEXT", "notNull": false}, {"fieldPath": "pricePaid", "columnName": "pricePaid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currency", "columnName": "currency", "affinity": "TEXT", "notNull": true}, {"fieldPath": "planName", "columnName": "planName", "affinity": "TEXT", "notNull": false}, {"fieldPath": "planDescription", "columnName": "planDescription", "affinity": "TEXT", "notNull": false}, {"fieldPath": "planId", "columnName": "planId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "syncStatus", "columnName": "syncStatus", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["subscriptionId"]}, "indices": [], "foreignKeys": []}, {"tableName": "payments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`paymentId` TEXT NOT NULL, `userId` TEXT NOT NULL, `subscriptionId` TEXT, `amount` REAL NOT NULL, `currency` TEXT NOT NULL, `paymentDate` INTEGER NOT NULL, `paymentMethod` TEXT NOT NULL, `transactionId` TEXT, `status` TEXT NOT NULL, `errorMessage` TEXT, `isSynced` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, PRIMARY KEY(`paymentId`), FOREIGN KEY(`subscriptionId`) REFERENCES `subscriptions`(`subscriptionId`) ON UPDATE NO ACTION ON DELETE SET NULL )", "fields": [{"fieldPath": "paymentId", "columnName": "paymentId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "subscriptionId", "columnName": "subscriptionId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "amount", "columnName": "amount", "affinity": "REAL", "notNull": true}, {"fieldPath": "currency", "columnName": "currency", "affinity": "TEXT", "notNull": true}, {"fieldPath": "paymentDate", "columnName": "paymentDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "paymentMethod", "columnName": "paymentMethod", "affinity": "TEXT", "notNull": true}, {"fieldPath": "transactionId", "columnName": "transactionId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "errorMessage", "columnName": "errorMessage", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["paymentId"]}, "indices": [{"name": "index_payments_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_payments_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_payments_subscriptionId", "unique": false, "columnNames": ["subscriptionId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_payments_subscriptionId` ON `${TABLE_NAME}` (`subscriptionId`)"}], "foreignKeys": [{"table": "subscriptions", "onDelete": "SET NULL", "onUpdate": "NO ACTION", "columns": ["subscriptionId"], "referencedColumns": ["subscriptionId"]}]}, {"tableName": "regional_pricing", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `regionCode` TEXT NOT NULL, `regionName` TEXT NOT NULL, `planType` TEXT NOT NULL, `currencyCode` TEXT NOT NULL, `currencySymbol` TEXT NOT NULL, `monthlyPrice` REAL NOT NULL, `yearlyPrice` REAL NOT NULL, `discountPercentage` INTEGER NOT NULL, `billingPeriod` TEXT NOT NULL, `lastUpdated` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "regionCode", "columnName": "regionCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "regionName", "columnName": "regionName", "affinity": "TEXT", "notNull": true}, {"fieldPath": "planType", "columnName": "planType", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currencyCode", "columnName": "currencyCode", "affinity": "TEXT", "notNull": true}, {"fieldPath": "currencySymbol", "columnName": "currencySymbol", "affinity": "TEXT", "notNull": true}, {"fieldPath": "monthlyPrice", "columnName": "monthlyPrice", "affinity": "REAL", "notNull": true}, {"fieldPath": "yearlyPrice", "columnName": "yearlyPrice", "affinity": "REAL", "notNull": true}, {"fieldPath": "discountPercentage", "columnName": "discountPercentage", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "billingPeriod", "columnName": "billingPeriod", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastUpdated", "columnName": "lastUpdated", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_regional_pricing_regionCode_planType_billingPeriod", "unique": true, "columnNames": ["regionCode", "planType", "billingPeriod"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_regional_pricing_regionCode_planType_billingPeriod` ON `${TABLE_NAME}` (`regionCode`, `planType`, `billingPeriod`)"}], "foreignKeys": []}, {"tableName": "usage_limits", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `userId` TEXT NOT NULL, `maxTemplates` INTEGER NOT NULL, `maxRoutines` INTEGER NOT NULL, `maxExercisesPerRoutine` INTEGER NOT NULL, `maxAiRequests` INTEGER NOT NULL, `currentTemplates` INTEGER NOT NULL, `currentRoutines` INTEGER NOT NULL, `currentAiRequests` INTEGER NOT NULL, `resetDate` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "maxTemplates", "columnName": "maxTemplates", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxRoutines", "columnName": "maxRoutines", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxExercisesPerRoutine", "columnName": "maxExercisesPerRoutine", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "maxAiRequests", "columnName": "maxAiRequests", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentTemplates", "columnName": "currentTemplates", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentRoutines", "columnName": "currentRoutines", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "currentAiRequests", "columnName": "currentAiRequests", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "resetDate", "columnName": "resetDate", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_usage_limits_userId", "unique": true, "columnNames": ["userId"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_usage_limits_userId` ON `${TABLE_NAME}` (`userId`)"}], "foreignKeys": []}, {"tableName": "exercises", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`exerciseId` TEXT NOT NULL, `name` TEXT NOT NULL, `muscleGroup` TEXT NOT NULL, `equipment` TEXT NOT NULL, `description` TEXT NOT NULL, `imageUrl` TEXT, `defaultSets` INTEGER NOT NULL, `defaultReps` INTEGER NOT NULL, `defaultWeight` REAL, `steps` TEXT NOT NULL, `tips` TEXT NOT NULL, `isCustom` INTEGER NOT NULL, `userId` TEXT, `isSynced` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, PRIMARY KEY(`exerciseId`))", "fields": [{"fieldPath": "exerciseId", "columnName": "exerciseId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "muscleGroup", "columnName": "muscleGroup", "affinity": "TEXT", "notNull": true}, {"fieldPath": "equipment", "columnName": "equipment", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "imageUrl", "columnName": "imageUrl", "affinity": "TEXT", "notNull": false}, {"fieldPath": "defaultSets", "columnName": "defaultSets", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "defaultReps", "columnName": "defaultReps", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "defaultWeight", "columnName": "defaultWeight", "affinity": "REAL", "notNull": false}, {"fieldPath": "steps", "columnName": "steps", "affinity": "TEXT", "notNull": true}, {"fieldPath": "tips", "columnName": "tips", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isCustom", "columnName": "isCustom", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["exerciseId"]}, "indices": [{"name": "index_exercises_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercises_userId` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_exercises_name", "unique": false, "columnNames": ["name"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercises_name` ON `${TABLE_NAME}` (`name`)"}, {"name": "index_exercises_muscleGroup", "unique": false, "columnNames": ["muscleGroup"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercises_muscleGroup` ON `${TABLE_NAME}` (`muscleGroup`)"}, {"name": "index_exercises_equipment", "unique": false, "columnNames": ["equipment"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercises_equipment` ON `${TABLE_NAME}` (`equipment`)"}], "foreignKeys": []}, {"tableName": "workout_templates", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `name` TEXT NOT NULL, `description` TEXT NOT NULL, `userId` TEXT NOT NULL, `createdAt` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, `isSynced` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "description", "columnName": "description", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "workout_sessions", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`sessionId` TEXT NOT NULL, `date` INTEGER NOT NULL, `templateId` TEXT, `startTime` INTEGER, `endTime` INTEGER, `completionTimestamp` INTEGER, `isCompleted` INTEGER NOT NULL, `plannedDate` INTEGER, `plannedTemplateId` TEXT, `status` TEXT NOT NULL, `userId` TEXT NOT NULL, `planId` TEXT, `isSynced` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, PRIMARY KEY(`sessionId`))", "fields": [{"fieldPath": "sessionId", "columnName": "sessionId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "templateId", "columnName": "templateId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "startTime", "columnName": "startTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "endTime", "columnName": "endTime", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "completionTimestamp", "columnName": "completionTimestamp", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isCompleted", "columnName": "isCompleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "plannedDate", "columnName": "plannedDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "plannedTemplateId", "columnName": "plannedTemplateId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "status", "columnName": "status", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "planId", "columnName": "planId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["sessionId"]}, "indices": [{"name": "index_workout_sessions_userId_entity", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_userId_entity` ON `${TABLE_NAME}` (`userId`)"}, {"name": "index_workout_sessions_sessionId", "unique": false, "columnNames": ["sessionId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_sessionId` ON `${TABLE_NAME}` (`sessionId`)"}, {"name": "index_workout_sessions_date", "unique": false, "columnNames": ["date"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_date` ON `${TABLE_NAME}` (`date`)"}, {"name": "index_workout_sessions_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_sessions_userId` ON `${TABLE_NAME}` (`userId`)"}], "foreignKeys": []}, {"tableName": "exercise_sets", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `sessionId` TEXT NOT NULL, `exerciseId` TEXT NOT NULL, `setNumber` INTEGER NOT NULL, `weight` REAL, `reps` INTEGER NOT NULL, `durationSeconds` INTEGER, `restTimeSeconds` INTEGER, `isCompleted` INTEGER NOT NULL, `completionTimestamp` INTEGER, `notes` TEXT, `userId` TEXT NOT NULL, `isSynced` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, PRIMARY KEY(`id`), FOREIGN KEY(`sessionId`) REFERENCES `workout_sessions`(`sessionId`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`exerciseId`) REFERENCES `exercises`(`exerciseId`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "sessionId", "columnName": "sessionId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "exerciseId", "columnName": "exerciseId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "setNumber", "columnName": "setNumber", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "weight", "columnName": "weight", "affinity": "REAL", "notNull": false}, {"fieldPath": "reps", "columnName": "reps", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "durationSeconds", "columnName": "durationSeconds", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "restTimeSeconds", "columnName": "restTimeSeconds", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isCompleted", "columnName": "isCompleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "completionTimestamp", "columnName": "completionTimestamp", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_exercise_sets_sessionId", "unique": false, "columnNames": ["sessionId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_sets_sessionId` ON `${TABLE_NAME}` (`sessionId`)"}, {"name": "index_exercise_sets_exerciseId", "unique": false, "columnNames": ["exerciseId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_sets_exerciseId` ON `${TABLE_NAME}` (`exerciseId`)"}, {"name": "index_exercise_sets_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_exercise_sets_userId` ON `${TABLE_NAME}` (`userId`)"}], "foreignKeys": [{"table": "workout_sessions", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["sessionId"], "referencedColumns": ["sessionId"]}, {"table": "exercises", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["exerciseId"], "referencedColumns": ["exerciseId"]}]}, {"tableName": "training_plans", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`planId` TEXT NOT NULL, `name` TEXT NOT NULL, `userId` TEXT NOT NULL, `lastModified` INTEGER NOT NULL, `isSynced` INTEGER NOT NULL, PRIMARY KEY(`planId`))", "fields": [{"fieldPath": "planId", "columnName": "planId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "name", "columnName": "name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["planId"]}, "indices": [{"name": "index_training_plans_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_training_plans_userId` ON `${TABLE_NAME}` (`userId`)"}], "foreignKeys": []}, {"tableName": "workout_template_exercise_links", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `templateId` TEXT NOT NULL, `exerciseId` TEXT NOT NULL, `orderPosition` INTEGER NOT NULL, `targetSets` INTEGER NOT NULL, `targetReps` INTEGER NOT NULL, `targetWeight` REAL, `restSeconds` INTEGER NOT NULL, `notes` TEXT, FOREIGN KEY(`templateId`) REFERENCES `workout_templates`(`id`) ON UPDATE NO ACTION ON DELETE CASCADE , FOREIGN KEY(`exerciseId`) REFERENCES `exercises`(`exerciseId`) ON UPDATE NO ACTION ON DELETE NO ACTION )", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "templateId", "columnName": "templateId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "exerciseId", "columnName": "exerciseId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "orderPosition", "columnName": "orderPosition", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "targetSets", "columnName": "targetSets", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "targetReps", "columnName": "targetReps", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "targetWeight", "columnName": "targetWeight", "affinity": "REAL", "notNull": false}, {"fieldPath": "restSeconds", "columnName": "restSeconds", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [{"name": "index_workout_template_exercise_links_templateId", "unique": false, "columnNames": ["templateId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_template_exercise_links_templateId` ON `${TABLE_NAME}` (`templateId`)"}, {"name": "index_workout_template_exercise_links_exerciseId", "unique": false, "columnNames": ["exerciseId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_workout_template_exercise_links_exerciseId` ON `${TABLE_NAME}` (`exerciseId`)"}, {"name": "index_workout_template_exercise_links_templateId_orderPosition", "unique": true, "columnNames": ["templateId", "orderPosition"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_workout_template_exercise_links_templateId_orderPosition` ON `${TABLE_NAME}` (`templateId`, `orderPosition`)"}, {"name": "index_workout_template_exercise_links_templateId_exerciseId", "unique": true, "columnNames": ["templateId", "exerciseId"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_workout_template_exercise_links_templateId_exerciseId` ON `${TABLE_NAME}` (`templateId`, `exerciseId`)"}], "foreignKeys": [{"table": "workout_templates", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["templateId"], "referencedColumns": ["id"]}, {"table": "exercises", "onDelete": "NO ACTION", "onUpdate": "NO ACTION", "columns": ["exerciseId"], "referencedColumns": ["exerciseId"]}]}, {"tableName": "weekly_plan_assignments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`planId` TEXT NOT NULL, `userId` TEXT NOT NULL, `dayOfWeek` INTEGER NOT NULL, `workoutTemplateId` TEXT, `assignmentId` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `isSynced` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL)", "fields": [{"fieldPath": "planId", "columnName": "planId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "dayOfWeek", "columnName": "dayOfWeek", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "workoutTemplateId", "columnName": "workoutTemplateId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "assignmentId", "columnName": "assignmentId", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["assignmentId"]}, "indices": [{"name": "index_weekly_plan_assignments_userId_dayOfWeek", "unique": true, "columnNames": ["userId", "dayOfWeek"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_weekly_plan_assignments_userId_dayOfWeek` ON `${TABLE_NAME}` (`userId`, `dayOfWeek`)"}, {"name": "index_weekly_plan_assignments_workoutTemplateId", "unique": false, "columnNames": ["workoutTemplateId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_weekly_plan_assignments_workoutTemplateId` ON `${TABLE_NAME}` (`workoutTemplateId`)"}], "foreignKeys": []}, {"tableName": "plan_assignments", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`assignmentId` TEXT NOT NULL, `planId` TEXT NOT NULL, `userId` TEXT NOT NULL, `date` TEXT NOT NULL, `workoutTemplateId` TEXT, `title` TEXT, `notes` TEXT, `completed` INTEGER NOT NULL, `isSynced` INTEGER NOT NULL, `lastModified` INTEGER NOT NULL, PRIMARY KEY(`assignmentId`), FOREIGN KEY(`planId`) REFERENCES `training_plans`(`planId`) ON UPDATE NO ACTION ON DELETE CASCADE )", "fields": [{"fieldPath": "assignmentId", "columnName": "assignmentId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "planId", "columnName": "planId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "date", "columnName": "date", "affinity": "TEXT", "notNull": true}, {"fieldPath": "workoutTemplateId", "columnName": "workoutTemplateId", "affinity": "TEXT", "notNull": false}, {"fieldPath": "title", "columnName": "title", "affinity": "TEXT", "notNull": false}, {"fieldPath": "notes", "columnName": "notes", "affinity": "TEXT", "notNull": false}, {"fieldPath": "completed", "columnName": "completed", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isSynced", "columnName": "isSynced", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "lastModified", "columnName": "lastModified", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["assignmentId"]}, "indices": [{"name": "index_plan_assignments_planId", "unique": false, "columnNames": ["planId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_assignments_planId` ON `${TABLE_NAME}` (`planId`)"}, {"name": "index_plan_assignments_date", "unique": false, "columnNames": ["date"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_assignments_date` ON `${TABLE_NAME}` (`date`)"}, {"name": "index_plan_assignments_userId", "unique": false, "columnNames": ["userId"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_plan_assignments_userId` ON `${TABLE_NAME}` (`userId`)"}], "foreignKeys": [{"table": "training_plans", "onDelete": "CASCADE", "onUpdate": "NO ACTION", "columns": ["planId"], "referencedColumns": ["planId"]}]}, {"tableName": "active_trainings", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` TEXT NOT NULL, `userId` TEXT NOT NULL, `trainingPlanId` TEXT NOT NULL, `startDate` INTEGER NOT NULL, `endDate` INTEGER, `isActive` INTEGER NOT NULL, `createdAt` INTEGER NOT NULL, `updatedAt` INTEGER NOT NULL, PRIMARY KEY(`id`))", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "TEXT", "notNull": true}, {"fieldPath": "userId", "columnName": "userId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "trainingPlanId", "columnName": "trainingPlanId", "affinity": "TEXT", "notNull": true}, {"fieldPath": "startDate", "columnName": "startDate", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "endDate", "columnName": "endDate", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "isActive", "columnName": "isActive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "createdAt", "columnName": "createdAt", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "updatedAt", "columnName": "updatedAt", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": false, "columnNames": ["id"]}, "indices": [{"name": "index_active_trainings_userId", "unique": true, "columnNames": ["userId"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_active_trainings_userId` ON `${TABLE_NAME}` (`userId`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '8c361a1b1d08b2b870bc26abc5030954')"]}}
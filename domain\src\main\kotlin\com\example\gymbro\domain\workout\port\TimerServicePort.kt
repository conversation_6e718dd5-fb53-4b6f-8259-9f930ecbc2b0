package com.example.gymbro.domain.workout.port

import com.example.gymbro.core.error.types.ModernResult

/**
 * 计时器服务端口接口
 * 解决测试中Data层依赖Features层服务的问题
 *
 * 这个接口抽象了计时器UI服务的操作，让Data层通过接口与Features层交互，
 * 避免直接依赖，同时便于测试时提供Mock实现
 */
interface TimerServicePort {

    /**
     * 显示计时器覆盖层
     *
     * @param durationSeconds 计时器总时长（秒）
     * @param remainingSeconds 剩余时长（秒）
     * @param timerType 计时器类型标识
     * @return 操作结果
     */
    suspend fun showTimerOverlay(
        durationSeconds: Int,
        remainingSeconds: Int,
        timerType: String = "REST",
    ): ModernResult<Unit>

    /**
     * 隐藏计时器覆盖层
     *
     * @return 操作结果
     */
    suspend fun hideTimerOverlay(): ModernResult<Unit>

    /**
     * 更新计时器显示状态
     *
     * @param remainingSeconds 剩余时长（秒）
     * @param isRunning 是否正在运行
     * @return 操作结果
     */
    suspend fun updateTimerState(
        remainingSeconds: Int,
        isRunning: Boolean,
    ): ModernResult<Unit>

    /**
     * 检查是否有显示权限
     *
     * @return 是否有权限
     */
    suspend fun hasOverlayPermission(): Boolean

    /**
     * 获取推荐的显示模式
     *
     * @return 推荐的显示模式
     */
    suspend fun getRecommendedDisplayMode(): String
}
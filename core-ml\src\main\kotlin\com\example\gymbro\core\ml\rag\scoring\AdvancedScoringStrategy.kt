package com.example.gymbro.core.ml.rag.scoring

import javax.inject.Inject
import kotlin.math.*

/**
 * 高级评分策略实现
 *
 * 基于机器学习启发的多因素评分算法：
 * - 非线性混合权重
 * - 对数时间衰减
 * - 会话上下文感知
 * - 相关性增强
 *
 * 适用于需要更精确排序的高级搜索场景。
 */
class AdvancedScoringStrategy @Inject constructor() : ScoringStrategy {

    companion object {
        // 时间衰减参数
        private const val TIME_DECAY_LAMBDA = 0.693f / 7f // 7天半衰期
        private const val TIME_BOOST_THRESHOLD_HOURS = 24f // 24小时内的内容获得额外加分
        private const val RECENT_BOOST_FACTOR = 1.2f

        // 分数调节参数
        private const val MIN_SCORE_THRESHOLD = 0.05f
        private const val MAX_SCORE_SATURATION = 0.95f
        private const val NONLINEAR_POWER = 1.5f

        // 会话上下文参数
        private const val CURRENT_SESSION_PENALTY = 0.3f
        private const val CROSS_SESSION_BOOST = 1.1f

        // 相关性增强参数
        private const val HIGH_CONFIDENCE_THRESHOLD = 0.8f
        private const val HIGH_CONFIDENCE_BOOST = 1.3f
    }

    override fun calculateCombinedScore(
        vectorScore: Float,
        keywordScore: Float,
        hybridWeight: Float,
        timestamp: Long,
        isCurrentSession: Boolean,
    ): Float {
        // 1. 非线性混合权重计算
        val enhancedVectorScore = enhanceVectorScore(vectorScore)
        val enhancedKeywordScore = enhanceKeywordScore(keywordScore)

        val nonlinearWeight = calculateNonlinearWeight(hybridWeight, vectorScore, keywordScore)
        val baseScore = enhancedVectorScore * nonlinearWeight + enhancedKeywordScore * (1 - nonlinearWeight)

        // 2. 时间衰减和近期加成
        val timeDecayFactor = calculateAdvancedTimeDecay(timestamp)

        // 3. 会话上下文调节
        val sessionContextFactor = calculateSessionContextFactor(isCurrentSession, baseScore)

        // 4. 相关性置信度加成
        val confidenceBoost = calculateConfidenceBoost(vectorScore, keywordScore)

        // 5. 最终分数计算和归一化
        val finalScore = baseScore * timeDecayFactor * sessionContextFactor * confidenceBoost

        return finalScore.coerceIn(MIN_SCORE_THRESHOLD, MAX_SCORE_SATURATION)
    }

    /**
     * 增强向量分数（使用幂函数突出高相似度）
     */
    private fun enhanceVectorScore(vectorScore: Float): Float {
        return vectorScore.pow(NONLINEAR_POWER).coerceIn(0f, 1f)
    }

    /**
     * 增强关键词分数（使用对数函数平滑低分）
     */
    private fun enhanceKeywordScore(keywordScore: Float): Float {
        return if (keywordScore > 0f) {
            ln(1 + keywordScore) / ln(2f) // 使用log2归一化
        } else {
            0f
        }
    }

    /**
     * 计算非线性混合权重
     * 根据两种分数的质量动态调整权重
     */
    private fun calculateNonlinearWeight(
        hybridWeight: Float,
        vectorScore: Float,
        keywordScore: Float,
    ): Float {
        // 如果某种搜索结果明显更好，增加其权重
        val scoreDifference = abs(vectorScore - keywordScore)
        val adaptiveAdjustment = scoreDifference * 0.2f

        return when {
            vectorScore > keywordScore -> (hybridWeight + adaptiveAdjustment).coerceIn(0f, 1f)
            keywordScore > vectorScore -> (hybridWeight - adaptiveAdjustment).coerceIn(0f, 1f)
            else -> hybridWeight
        }
    }

    /**
     * 高级时间衰减算法
     * 结合指数衰减和近期加成
     */
    private fun calculateAdvancedTimeDecay(timestamp: Long): Float {
        val currentTime = System.currentTimeMillis()
        val ageHours = (currentTime - timestamp) / (1000f * 60f * 60f)

        // 基础指数衰减
        val baseDecay = exp(-ageHours * TIME_DECAY_LAMBDA)

        // 近期内容加成
        val recentBoost = if (ageHours <= TIME_BOOST_THRESHOLD_HOURS) {
            RECENT_BOOST_FACTOR
        } else {
            1f
        }

        return (baseDecay * recentBoost).coerceIn(0.1f, 1f)
    }

    /**
     * 会话上下文因子计算
     * 考虑当前会话惩罚和跨会话增强
     */
    private fun calculateSessionContextFactor(isCurrentSession: Boolean, baseScore: Float): Float {
        return if (isCurrentSession) {
            // 当前会话内容降权，但高质量内容降权幅度较小
            val qualityAdjustment = baseScore * 0.5f // 高分内容惩罚更轻
            CURRENT_SESSION_PENALTY + qualityAdjustment
        } else {
            // 跨会话内容轻微加成
            CROSS_SESSION_BOOST
        }
    }

    /**
     * 相关性置信度加成
     * 当向量和关键词都表现良好时给予额外加分
     */
    private fun calculateConfidenceBoost(vectorScore: Float, keywordScore: Float): Float {
        val averageScore = (vectorScore + keywordScore) / 2f
        return if (averageScore > HIGH_CONFIDENCE_THRESHOLD) {
            HIGH_CONFIDENCE_BOOST
        } else {
            1f + (averageScore - 0.5f) * 0.2f // 渐进式加成
        }
    }
}

/**
 * 实验性评分策略
 *
 * 用于A/B测试的激进算法：
 * - 极度重视新鲜度
 * - 强化向量相似度
 * - 简化计算逻辑
 */
class ExperimentalScoringStrategy @Inject constructor() : ScoringStrategy {

    companion object {
        private const val FRESHNESS_POWER = 2.0f
        private const val VECTOR_BOOST = 1.5f
        private const val KEYWORD_DECAY = 0.8f
        private const val SESSION_PENALTY = 0.1f
    }

    override fun calculateCombinedScore(
        vectorScore: Float,
        keywordScore: Float,
        hybridWeight: Float,
        timestamp: Long,
        isCurrentSession: Boolean,
    ): Float {
        // 极度重视向量相似度
        val boostedVectorScore = (vectorScore * VECTOR_BOOST).coerceAtMost(1f)
        val dampedKeywordScore = keywordScore * KEYWORD_DECAY

        // 简单混合
        val baseScore = boostedVectorScore * hybridWeight + dampedKeywordScore * (1 - hybridWeight)

        // 极强的新鲜度权重
        val hoursAge = (System.currentTimeMillis() - timestamp) / (1000f * 60f * 60f)
        val freshnessScore = exp(-hoursAge / 12f).pow(FRESHNESS_POWER) // 12小时半衰期

        // 最小会话惩罚
        val sessionFactor = if (isCurrentSession) SESSION_PENALTY else 1f

        return (baseScore * freshnessScore * sessionFactor).coerceIn(0f, 1f)
    }
}

/**
 * 保守评分策略
 *
 * 用于对比测试的保守算法：
 * - 均衡考虑所有因素
 * - 温和的时间衰减
 * - 稳定的权重分配
 */
class ConservativeScoringStrategy @Inject constructor() : ScoringStrategy {

    companion object {
        private const val GENTLE_TIME_DECAY = 0.02f // 每天2%衰减
        private const val BALANCED_SESSION_PENALTY = 0.7f
        private const val STABILITY_FACTOR = 0.9f
    }

    override fun calculateCombinedScore(
        vectorScore: Float,
        keywordScore: Float,
        hybridWeight: Float,
        timestamp: Long,
        isCurrentSession: Boolean,
    ): Float {
        // 标准线性混合
        val baseScore = vectorScore * hybridWeight + keywordScore * (1 - hybridWeight)

        // 温和的时间衰减
        val daysAge = (System.currentTimeMillis() - timestamp) / (1000f * 60f * 60f * 24f)
        val timeDecay = (1f - GENTLE_TIME_DECAY * daysAge).coerceAtLeast(0.3f)

        // 温和的会话惩罚
        val sessionFactor = if (isCurrentSession) BALANCED_SESSION_PENALTY else 1f

        // 稳定性调节
        return (baseScore * timeDecay * sessionFactor * STABILITY_FACTOR).coerceIn(0.1f, 0.9f)
    }
}

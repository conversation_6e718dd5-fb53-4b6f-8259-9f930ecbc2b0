package com.example.gymbro.core.userdata.internal.di

import com.example.gymbro.core.userdata.internal.datasource.UserLocalDataSource
import dagger.hilt.android.scopes.ActivityRetainedScoped
import javax.inject.Inject
import javax.inject.Provider

/**
 * Provider for accessing different UserLocalDataSource implementations
 *
 * This provider allows components to access specific implementations of UserLocalDataSource
 * when needed, particularly useful for testing scenarios or when specific behavior is required.
 *
 * Usage examples:
 * - Tests can inject this provider to access the simple implementation
 * - Components can switch between implementations based on configuration
 * - Debugging tools can access different implementations for comparison
 */
@ActivityRetainedScoped
class UserDataSourceProvider @Inject constructor(
    @DefaultUserDataSource private val defaultDataSource: Provider<UserLocalDataSource>,
    @SimpleUserDataSource private val simpleDataSource: Provider<UserLocalDataSource>,
    @RoomUserDataSource private val roomDataSource: Provider<UserLocalDataSource>,
) {

    /**
     * Get the default UserLocalDataSource implementation
     *
     * This is the implementation that should be used in production.
     * Currently points to the Room-based implementation.
     *
     * @return UserLocalDataSource default implementation
     */
    fun getDefault(): UserLocalDataSource = defaultDataSource.get()

    /**
     * Get the simple in-memory UserLocalDataSource implementation
     *
     * This implementation is useful for:
     * - Unit tests that need fast, isolated data source
     * - Testing scenarios where Room database is not needed
     * - Development scenarios where database setup is complex
     *
     * @return UserLocalDataSource simple implementation
     */
    fun getSimple(): UserLocalDataSource = simpleDataSource.get()

    /**
     * Get the Room-based UserLocalDataSource implementation
     *
     * This implementation provides real database persistence and is used
     * in production and integration tests.
     *
     * @return UserLocalDataSource Room implementation
     */
    fun getRoom(): UserLocalDataSource = roomDataSource.get()

    /**
     * Get a specific implementation by type
     *
     * @param type The type of implementation to retrieve
     * @return UserLocalDataSource implementation of the specified type
     */
    fun getByType(type: UserDataSourceType): UserLocalDataSource {
        return when (type) {
            UserDataSourceType.DEFAULT -> getDefault()
            UserDataSourceType.SIMPLE -> getSimple()
            UserDataSourceType.ROOM -> getRoom()
        }
    }
}

/**
 * Enum representing different types of UserLocalDataSource implementations
 */
enum class UserDataSourceType {
    /**
     * Default implementation (currently Room-based)
     */
    DEFAULT,

    /**
     * Simple in-memory implementation
     */
    SIMPLE,

    /**
     * Room database implementation
     */
    ROOM,
}

package com.example.gymbro.features.coach.aicoach.internal.components.input

import androidx.compose.foundation.background
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.isSystemInDarkTheme // 🔥 【阴影优化】添加主题检测导入
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow // 🔥 【阴影优化】添加自定义阴影导入
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex // 🔥 【编译修复】添加 zIndex 导入
import com.example.gymbro.designSystem.components.extras.ThemeState
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.coachTheme // 🔥 【颜色修复】添加 Coach 主题导入
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.aicoach.internal.components.SuggestionMatcher

/**
 * 🎯 【架构修复】ChatGPT风格输入组装容器 - 承担输入相关组装职责
 *
 * 📋 组装职责：
 * - ChatGPT风格Input组装（输入框本身）
 * - ActionToolbar组装（工具栏）
 *
 * 🔥 【架构修复】ExpandedSuggestionPanel已移至AiCoachScreen主布局，与输入容器平级
 *
 * 📍 WelcomeScreen由ChatInterface.kt在主内容区域负责显示
 * 🚫 其他文件（AiCoachScreen.kt）只负责布局呈现，不做组装包裹
 */
@Composable
internal fun ChatGPTFloatingInput(
    state: AiCoachContract.State,
    onIntent: (AiCoachContract.Intent) -> Unit,
    modifier: Modifier = Modifier,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    themeState: ThemeState? = null,
) {
    val inputText = state.inputState.text
    // 🔥 使用统一的建议匹配逻辑，消除重复代码
    val showSuggestions = remember(state.suggestionConfig, inputText) {
        SuggestionMatcher.shouldShowSuggestions(inputText, state.suggestionConfig)
    }

    // 🔥 键盘状态检测 - 用于动态调整布局
    val density = LocalDensity.current
    val imeInsets = WindowInsets.ime
    val isKeyboardVisible = remember {
        derivedStateOf {
            imeInsets.getBottom(density) > 0
        }
    }.value

    // 🔥 修复：根据内容动态计算高度，避免压缩
    val dynamicMaxHeight = remember(inputText, isKeyboardVisible) {
        derivedStateOf {
            val baseHeight = if (isKeyboardVisible) 120.dp else 200.dp
            val contentLines = inputText.count { it == '\n' } + 1
            val extraHeight = if (contentLines > 2) ((contentLines - 2) * 20).dp else 0.dp
            baseHeight + extraHeight
        }
    }.value

    // 🔥 【阴影优化】根据主题自动调整阴影强度和颜色
    val isDarkTheme = isSystemInDarkTheme()
    val shadowElevation = if (isDarkTheme) 16.dp else 12.dp // 深色主题使用更大阴影确保可见性
    val shadowColor =
        if (isDarkTheme) {
            androidx.compose.ui.graphics.Color.White
                .copy(alpha = 0.15f) // 深色主题使用白色半透明阴影
        } else {
            androidx.compose.ui.graphics.Color.Black
                .copy(alpha = 0.1f) // 浅色主题使用黑色半透明阴影
        }

    // 🔥 【层级修复】最外层添加阴影容器，确保在最顶层
    Surface(
        modifier =
        modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .zIndex(10f) // 🔥 【关键修复】确保输入框在最顶层
            .shadow( // 🔥 【阴影优化】添加自定义阴影确保在黑色主题下可见
                elevation = shadowElevation,
                shape =
                RoundedCornerShape(
                    topStart = 12.dp,
                    topEnd = 12.dp,
                    bottomStart = 0.dp,
                    bottomEnd = 0.dp,
                ),
                ambientColor = shadowColor,
                spotColor = shadowColor,
            ),
        shape =
        RoundedCornerShape(
            topStart = 12.dp, // 🔥 左上角圆角
            topEnd = 12.dp, // 🔥 右上角圆角
            bottomStart = 0.dp, // 🔥 左下角贴合屏幕
            bottomEnd = 0.dp, // 🔥 右下角贴合屏幕
        ),
        color = MaterialTheme.coachTheme.backgroundElevated, // 🔥 【颜色统一】与TopBar使用相同的背景色
        shadowElevation = 0.dp, // 🔥 【阴影优化】使用自定义阴影，关闭默认阴影
        tonalElevation = 2.dp, // 🔥 【层级修复】添加tonal elevation增强层级感
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight(), // 🔥 自适应内容高度，移除imePadding
            verticalArrangement = Arrangement.Bottom, // 🔥 从底部开始排列
        ) {
            // 🔥 【架构修复】ExpandedSuggestionPanel已移至AiCoachScreen主布局，此处不再包含

            // 🎯 【组装职责】ChatGPT风格输入容器 - 带左右圆角，内层无阴影
            Surface(
                modifier = Modifier
                    .fillMaxWidth() // 🔥 完全填满宽度，贴合屏幕边缘
                    .wrapContentHeight(),
                shape = RoundedCornerShape(
                    topStart = 12.dp, // 🔥 左上角圆角
                    topEnd = 12.dp, // 🔥 右上角圆角
                    bottomStart = 0.dp, // 🔥 左下角贴合屏幕
                    bottomEnd = 0.dp, // 🔥 右下角贴合屏幕
                ),
                color = MaterialTheme.coachTheme.backgroundElevated, // 🔥 【颜色统一】内层也与TopBar使用相同背景色
                shadowElevation = 0.dp, // 🔥 内层无阴影，外层已添加
                tonalElevation = 0.dp, // 保持无tonal elevation
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            horizontal = Tokens.Spacing.Medium, // 🔥 使用Token系统
                            vertical = Tokens.Spacing.Small, // 🔥 ChatGPT风格：紧凑的垂直间距
                        ),
                ) {
                    // 🎯 无边框文本输入区域 - 🔥 根据键盘状态动态调整高度
                    ChatGPTTransparentInput(
                        value = state.inputState.text,
                        onValueChange = { onIntent(AiCoachContract.Intent.UpdateInput(it)) },
                        enabled = !state.isLoading,
                        placeholder = state.inputState.placeholder,
                        interactionSource = interactionSource,
                        modifier = Modifier
                            .fillMaxWidth()
                            .wrapContentHeight()
                            .heightIn(
                                min = 60.dp, // 🔥 修复：增加最小高度，确保有内容时不被压缩
                                max = dynamicMaxHeight, // 🔥 使用动态高度，根据内容自适应
                            ),
                    )

                    // 🎯 底部工具栏区域 - 🔥 确保始终可见
                    ActionToolbar(
                        state = state,
                        onIntent = onIntent,
                        themeState = themeState,
                        modifier = Modifier
                            .fillMaxWidth()
                            .wrapContentHeight(), // 🔥 确保按钮区域自适应高度
                    )
                }
            }
        } // 🔥 外层阴影容器闭合
    } // 🔥 最外层Surface闭合
}

@Composable
private fun ChatGPTTransparentInput(
    value: String,
    onValueChange: (String) -> Unit,
    enabled: Boolean,
    placeholder: String,
    interactionSource: MutableInteractionSource,
    modifier: Modifier = Modifier,
) {
    // 🔥 【主题修复】保持边框透明但使用Coach主题颜色确保文本可见
    val transparentColors =
        TextFieldDefaults.colors(
            focusedContainerColor = androidx.compose.ui.graphics.Color.Transparent,
            unfocusedContainerColor = androidx.compose.ui.graphics.Color.Transparent,
            disabledContainerColor = androidx.compose.ui.graphics.Color.Transparent,
            errorContainerColor = androidx.compose.ui.graphics.Color.Transparent,
            focusedIndicatorColor = androidx.compose.ui.graphics.Color.Transparent,
            unfocusedIndicatorColor = androidx.compose.ui.graphics.Color.Transparent,
            disabledIndicatorColor = androidx.compose.ui.graphics.Color.Transparent,
            errorIndicatorColor = androidx.compose.ui.graphics.Color.Transparent,
            // 🔥 【主题修复】使用Coach主题颜色确保文本和光标可见
            cursorColor = MaterialTheme.coachTheme.iconPrimary, // 🔥 光标使用主题主色
            focusedTextColor = MaterialTheme.coachTheme.textPrimary, // 🔥 文本使用主题主文本色
            unfocusedTextColor = MaterialTheme.coachTheme.textPrimary, // 🔥 文本使用主题主文本色
            disabledTextColor = MaterialTheme.coachTheme.textPrimary.copy(alpha = 0.5f), // 🔥 禁用文本使用主题色
            // 🔥 【主题修复】占位符使用Coach主题占位符颜色
            focusedPlaceholderColor = MaterialTheme.coachTheme.textPlaceholder.copy(alpha = 0.6f),
            unfocusedPlaceholderColor = MaterialTheme.coachTheme.textPlaceholder.copy(alpha = 0.6f),
            disabledPlaceholderColor = MaterialTheme.coachTheme.textPlaceholder.copy(alpha = 0.4f),
        )

    TextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier,
        enabled = enabled,
        placeholder = {
            Text(
                text = placeholder,
                style =
                MaterialTheme.typography.bodyLarge.copy(
                    fontSize = Tokens.Typography.InputPlaceholder,
                ),
                color = MaterialTheme.coachTheme.textPlaceholder.copy(alpha = 0.6f), // 🔥 【主题修复】使用Coach主题占位符色
            )
        },
        textStyle = MaterialTheme.typography.bodyLarge.copy(
            fontSize = Tokens.Typography.InputText,
            color = MaterialTheme.coachTheme.textPrimary, // 🔥 【主题修复】使用Coach主题文本色
        ),
        colors = transparentColors,
        interactionSource = interactionSource,
        maxLines = 6, // 🔥 修复：增加最大行数，为更多内容留出空间
        singleLine = false,
    )
}

// 向后兼容别名
@Composable
internal fun ChatInputContainer(
    state: AiCoachContract.State,
    onIntent: (AiCoachContract.Intent) -> Unit,
    modifier: Modifier = Modifier,
    backgroundConfig: Any? = null,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    isFocused: Boolean = false,
    themeState: ThemeState? = null,
) {
    ChatGPTFloatingInput(
        state = state,
        onIntent = onIntent,
        modifier = modifier,
        interactionSource = interactionSource,
        themeState = themeState,
    )
}

// 预览组件
@GymBroPreview
@Composable
private fun ChatGPTFloatingInputPreview() {
    GymBroTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background),
        ) {
            // 🎯 模拟聊天内容背景
            Text(
                "ChatGPT风格聊天界面\n\n这是一个模拟的聊天内容区域，\n展示输入框的浮动效果。\n\n输入框采用24dp圆角、8dp阴影，\n与Portal工具面板完全一致的设计规格。",
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(24.dp),
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onBackground.copy(alpha = 0.7f),
                textAlign = androidx.compose.ui.text.style.TextAlign.Center,
            )

            // 🎯 ChatGPT风格输入框 - 完全贴合底部（零间距）
            ChatGPTFloatingInput(
                state = AiCoachContract.State(
                    inputState = AiCoachContract.InputState(
                        text = "",
                        placeholder = "询问任何问题", // 🔥 更符合ChatGPT风格的占位符
                    ),
                    isLoading = false,
                ),
                onIntent = { },
                modifier = Modifier
                    .align(Alignment.BottomCenter), // 🔥 完全贴合底部，零间距
            )
        }
    }
}

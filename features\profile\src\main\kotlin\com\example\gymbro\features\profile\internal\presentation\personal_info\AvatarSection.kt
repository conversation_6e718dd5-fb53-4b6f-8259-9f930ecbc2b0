package com.example.gymbro.features.profile.internal.presentation.personal_info

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.example.gymbro.designSystem.components.GymBroSettingsGroup
import com.example.gymbro.designSystem.theme.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens

/**
 * 头像编辑区块组件 - internal实现细节
 *
 * 此组件被标记为internal，外部模块无法直接访问
 * 只能通过PersonalInfoScreen的公共接口使用
 */
@Composable
internal fun AvatarSection(
    avatarUrl: String?,
    onEditAvatar: () -> Unit,
) {
    val context = LocalContext.current

    GymBroSettingsGroup {
        Column(
            modifier = Modifier.padding(Tokens.Spacing.Medium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        ) {
            Text(
                text = "头像",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface,
            )

            Box(
                modifier =
                Modifier
                    .size(Tokens.Spacing.Massive)
                    .clip(CircleShape)
                    .clickable { onEditAvatar() },
                contentAlignment = Alignment.Center,
            ) {
                if (!avatarUrl.isNullOrBlank()) {
                    AsyncImage(
                        model =
                        ImageRequest
                            .Builder(context)
                            .data(avatarUrl)
                            .crossfade(true)
                            .build(),
                        contentDescription = "用户头像",
                        contentScale = ContentScale.Crop,
                        modifier = Modifier.fillMaxSize().clip(CircleShape),
                    )
                } else {
                    Box(
                        modifier =
                        Modifier
                            .fillMaxSize()
                            .background(MaterialTheme.colorScheme.surfaceVariant, CircleShape),
                        contentAlignment = Alignment.Center,
                    ) {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = "默认头像",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant,
                            modifier = Modifier.size(Tokens.Icon.AvatarIcon),
                        )
                    }
                }

                Box(
                    modifier =
                    Modifier
                        .size(Tokens.Icon.Large)
                        .align(Alignment.BottomEnd)
                        .background(MaterialTheme.colorScheme.primary, CircleShape),
                    contentAlignment = Alignment.Center,
                ) {
                    Icon(
                        imageVector = Icons.Default.Edit,
                        contentDescription = "编辑头像",
                        tint = MaterialTheme.colorScheme.onPrimary,
                        modifier = Modifier.size(Tokens.Icon.Small),
                    )
                }
            }

            Text(
                text = "点击更换头像",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
            )
        }
    }
}

@Composable
@GymBroPreview
internal fun AvatarSectionPreview() {
    GymBroTheme {
        AvatarSection(
            avatarUrl = null,
            onEditAvatar = {},
        )
    }
}

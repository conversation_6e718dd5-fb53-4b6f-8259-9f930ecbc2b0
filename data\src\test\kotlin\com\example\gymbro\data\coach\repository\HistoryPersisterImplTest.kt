package com.example.gymbro.data.coach.repository

import com.example.gymbro.core.error.ModernResult
import com.example.gymbro.data.coach.dao.ConversationMetaDao
import com.example.gymbro.data.coach.dao.MessageEventDao
import com.example.gymbro.data.coach.entity.ConversationMetaEntity
import com.example.gymbro.data.coach.entity.MessageEventEntity
import com.example.gymbro.domain.coach.model.CoachMessage
import io.mockk.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.*
import org.junit.After
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

/**
 * HistoryPersisterImpl 单元测试
 *
 * 测试706任务保存.md规格中的历史持久化功能：
 * - 两阶段提交机制
 * - ConversationMeta和MessageEvent的正确保存
 * - 错误处理和恢复机制
 * - 批量操作性能
 */
@OptIn(ExperimentalCoroutinesApi::class)
class HistoryPersisterImplTest {

    private lateinit var historyPersister: HistoryPersisterImpl
    private lateinit var conversationMetaDao: ConversationMetaDao
    private lateinit var messageEventDao: MessageEventDao
    private lateinit var testDispatcher: TestDispatcher

    @Before
    fun setup() {
        testDispatcher = StandardTestDispatcher()
        Dispatchers.setMain(testDispatcher)

        conversationMetaDao = mockk()
        messageEventDao = mockk()

        historyPersister = HistoryPersisterImpl(
            conversationMetaDao = conversationMetaDao,
            messageEventDao = messageEventDao,
            ioDispatcher = testDispatcher,
        )
    }

    @After
    fun tearDown() {
        Dispatchers.resetMain()
        clearAllMocks()
    }

    @Test
    fun `persistMessage should create conversation meta and save message event`() = runTest {
        // Given
        val conversationId = "test_conversation_id"
        val userId = "test_user_id"
        val message = CoachMessage.UserMessage(
            id = "test_message_id",
            content = "Hello, AI!",
            timestamp = System.currentTimeMillis(),
        )

        // Mock DAO responses
        coEvery { conversationMetaDao.getConversationMeta(conversationId) } returns null
        coEvery { conversationMetaDao.insertConversationMeta(any()) } returns 1L
        coEvery { messageEventDao.insertMessageEvent(any()) } returns 1L
        coEvery { conversationMetaDao.updateLastActiveTime(any(), any()) } returns 1
        coEvery { conversationMetaDao.incrementMessageCount(any()) } returns 1

        // When
        val result = historyPersister.persistMessage(conversationId, message, userId)

        // Then
        assertTrue(result is ModernResult.Success)

        // Verify conversation meta creation
        coVerify { conversationMetaDao.insertConversationMeta(any()) }

        // Verify message event insertion
        coVerify { messageEventDao.insertMessageEvent(any()) }

        // Verify conversation meta updates
        coVerify { conversationMetaDao.updateLastActiveTime(conversationId, any()) }
        coVerify { conversationMetaDao.incrementMessageCount(conversationId) }
    }

    @Test
    fun `persistMessage should not create conversation meta if it already exists`() = runTest {
        // Given
        val conversationId = "existing_conversation_id"
        val userId = "test_user_id"
        val message = CoachMessage.AiMessage(
            id = "ai_message_id",
            content = "Hello, human!",
            timestamp = System.currentTimeMillis(),
            tokenCount = 50,
            finalMarkdown = "**Hello, human!**",
            thinkingNodes = null,
        )

        val existingMeta = ConversationMetaEntity(
            conversationId = conversationId,
            createdAt = System.currentTimeMillis() - 10000,
            title = "Existing Conversation",
            participants = "user,assistant",
            userId = userId,
            lastActiveAt = System.currentTimeMillis() - 5000,
            messageCount = 5,
        )

        // Mock DAO responses
        coEvery { conversationMetaDao.getConversationMeta(conversationId) } returns existingMeta
        coEvery { messageEventDao.insertMessageEvent(any()) } returns 1L
        coEvery { conversationMetaDao.updateLastActiveTime(any(), any()) } returns 1
        coEvery { conversationMetaDao.incrementMessageCount(any()) } returns 1

        // When
        val result = historyPersister.persistMessage(conversationId, message, userId)

        // Then
        assertTrue(result is ModernResult.Success)

        // Verify conversation meta was NOT created
        coVerify(exactly = 0) { conversationMetaDao.insertConversationMeta(any()) }

        // Verify message event was inserted
        coVerify { messageEventDao.insertMessageEvent(any()) }
    }

    @Test
    fun `persistMessages should handle batch operations correctly`() = runTest {
        // Given
        val conversationId = "batch_conversation_id"
        val userId = "test_user_id"
        val messages = listOf(
            CoachMessage.UserMessage("msg1", "Message 1", System.currentTimeMillis()),
            CoachMessage.AiMessage(
                "msg2",
                "Response 1",
                System.currentTimeMillis(),
                30,
                "**Response 1**",
                null,
            ),
            CoachMessage.UserMessage("msg3", "Message 2", System.currentTimeMillis()),
        )

        // Mock DAO responses
        coEvery { conversationMetaDao.getConversationMeta(conversationId) } returns null
        coEvery { conversationMetaDao.insertConversationMeta(any()) } returns 1L
        coEvery { messageEventDao.insertMessageEvents(any()) } returns listOf(1L, 2L, 3L)
        coEvery { conversationMetaDao.updateLastActiveTime(any(), any()) } returns 1
        coEvery { conversationMetaDao.incrementMessageCount(any()) } returns 1

        // When
        val result = historyPersister.persistMessages(conversationId, messages, userId)

        // Then
        assertTrue(result is ModernResult.Success)

        // Verify batch insertion
        coVerify { messageEventDao.insertMessageEvents(match { it.size == 3 }) }

        // Verify message count updates (3 times for 3 messages)
        coVerify(exactly = 3) { conversationMetaDao.incrementMessageCount(conversationId) }
    }

    @Test
    fun `markMessagesSynced should update sync status correctly`() = runTest {
        // Given
        val eventIds = listOf("event1", "event2", "event3")

        coEvery { messageEventDao.markSyncedBatch(eventIds, any()) } returns 3

        // When
        val result = historyPersister.markMessagesSynced(eventIds)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify { messageEventDao.markSyncedBatch(eventIds, any()) }
    }

    @Test
    fun `getUnsyncedMessages should return unsynced messages`() = runTest {
        // Given
        val limit = 50
        val unsyncedEntities = listOf(
            MessageEventEntity(
                eventId = "unsynced1",
                conversationId = "conv1",
                sequence = 1,
                role = "user",
                content = "Test message",
                createdAt = System.currentTimeMillis(),
                userId = "user1",
                syncedAt = null,
            ),
        )

        coEvery { messageEventDao.getUnsyncedMessages(limit) } returns unsyncedEntities

        // When
        val result = historyPersister.getUnsyncedMessages(limit)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(1, result.data.size)
        assertEquals("unsynced1", result.data[0].id)
    }

    @Test
    fun `updateConversationTitle should update title correctly`() = runTest {
        // Given
        val conversationId = "test_conversation"
        val newTitle = "Updated Title"

        coEvery { conversationMetaDao.updateTitle(conversationId, newTitle) } returns 1

        // When
        val result = historyPersister.updateConversationTitle(conversationId, newTitle)

        // Then
        assertTrue(result is ModernResult.Success)
        coVerify { conversationMetaDao.updateTitle(conversationId, newTitle) }
    }

    @Test
    fun `cleanupExpiredData should remove old deleted conversations`() = runTest {
        // Given
        val olderThanDays = 30
        val deletedCount = 5

        coEvery { conversationMetaDao.cleanupDeletedConversations(any()) } returns deletedCount

        // When
        val result = historyPersister.cleanupExpiredData(olderThanDays)

        // Then
        assertTrue(result is ModernResult.Success)
        assertEquals(deletedCount, result.data)

        // Verify correct timestamp calculation (approximately)
        val expectedCutoff = System.currentTimeMillis() - (olderThanDays * 24 * 60 * 60 * 1000L)
        coVerify {
            conversationMetaDao.cleanupDeletedConversations(
                match { timestamp ->
                    kotlin.math.abs(timestamp - expectedCutoff) < 1000 // Allow 1 second tolerance
                },
            )
        }
    }

    @Test
    fun `getPersistenceStats should return correct statistics`() = runTest {
        // Given
        val userId = "test_user"
        val conversationStats = mapOf("total" to 10, "active" to 8)
        val messageStats = mapOf("total" to 100, "user_messages" to 60)

        coEvery { conversationMetaDao.getConversationStats(userId) } returns conversationStats
        coEvery { messageEventDao.getMessageStats(userId) } returns messageStats
        coEvery { messageEventDao.getUnsyncedMessages(Int.MAX_VALUE) } returns emptyList()
        coEvery { conversationMetaDao.getUnsyncedConversations(Int.MAX_VALUE) } returns emptyList()

        // When
        val result = historyPersister.getPersistenceStats(userId)

        // Then
        assertTrue(result is ModernResult.Success)
        val stats = result.data
        assertEquals(10, stats.totalConversations)
        assertEquals(100, stats.totalMessages)
        assertEquals(0, stats.unsyncedMessages)
        assertEquals(0, stats.unsyncedConversations)
    }

    @Test
    fun `flushPending should complete without errors`() = runTest {
        // Given - no specific setup needed for current implementation

        // When
        val result = historyPersister.flushPending("test_conversation")

        // Then
        assertTrue(result is ModernResult.Success)
        // Current implementation doesn't do anything, but should not fail
    }
}

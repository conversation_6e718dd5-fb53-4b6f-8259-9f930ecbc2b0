package com.example.gymbro.data.coach.service

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.GlobalErrorType
import com.example.gymbro.core.error.types.ModernDataError
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ml.service.BgeEngineManager
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.coach.dao.ChatRawDao
import com.example.gymbro.data.coach.dao.MessageEmbeddingDao
import com.example.gymbro.data.coach.entity.MessageEmbeddingEntity
import com.example.gymbro.data.coach.entity.toByteArray
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 消息嵌入服务
 *
 * 基于 history补充.md 规范设计，负责：
 * - 集成现有BGE引擎生成消息嵌入向量
 * - 管理嵌入向量的生命周期
 * - 提供批量处理和错误恢复机制
 * - 支持性能监控和统计
 */
@Singleton
class MessageEmbeddingService @Inject constructor(
    private val bgeEngineManager: BgeEngineManager,
    private val messageEmbeddingDao: MessageEmbeddingDao,
    private val chatRawDao: ChatRawDao,
    private val logger: Logger,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {

    /**
     * 为单条消息生成嵌入向量
     *
     * @param messageId 消息ID
     * @return 生成结果
     */
    suspend fun generateEmbeddingForMessage(messageId: Long): ModernResult<MessageEmbeddingEntity> =
        withContext(ioDispatcher) {
            try {
                logger.d("MessageEmbeddingService", "开始为消息生成嵌入向量: messageId=$messageId")

                // 检查是否已存在嵌入向量
                val existingEmbedding = messageEmbeddingDao.getEmbeddingByMessageId(messageId)
                if (existingEmbedding?.embeddingStatus == MessageEmbeddingEntity.STATUS_COMPLETED) {
                    logger.d("MessageEmbeddingService", "消息已有嵌入向量，跳过: messageId=$messageId")
                    return@withContext ModernResult.Success(existingEmbedding)
                }

                // 获取消息内容
                val chatMessage = chatRawDao.getChatMessageById(messageId)
                if (chatMessage == null) {
                    logger.w("MessageEmbeddingService", "消息不存在: messageId=$messageId")
                    return@withContext ModernResult.Error(
                        ModernDataError(
                            operationName = "generateEmbeddingForMessage",
                            errorType = GlobalErrorType.Data.NotFound,
                            uiMessage = UiText.DynamicString("消息不存在"),
                            cause = null,
                        ),
                    )
                }

                // 🔥 修复：检查BGE引擎状态，避免重复初始化
                if (!bgeEngineManager.isReady()) {
                    logger.w("MessageEmbeddingService", "BGE引擎未就绪，跳过嵌入向量生成")
                    return@withContext ModernResult.Error(
                        ModernDataError(
                            operationName = "generateEmbeddingForMessage",
                            errorType = GlobalErrorType.System.Resource,
                            uiMessage = UiText.DynamicString("BGE引擎未就绪"),
                            cause = null,
                        ),
                    )
                }

                // 更新状态为处理中
                updateEmbeddingStatus(messageId, MessageEmbeddingEntity.STATUS_PROCESSING)

                val startTime = System.currentTimeMillis()

                // 生成嵌入向量
                val bgeEngine = bgeEngineManager.getEmbeddingEngine()
                val embeddingResult = bgeEngine.embed(chatMessage.content)

                val generationTime = System.currentTimeMillis() - startTime

                val result: ModernResult<MessageEmbeddingEntity> = when (embeddingResult) {
                    is ModernResult.Success<*> -> {
                        // 🔥 【逻辑修复】安全的类型转换，避免"Check for instance is always 'false'"警告
                        val vector = embeddingResult.data
                        if (vector !is FloatArray) {
                            logger.e(
                                "MessageEmbeddingService",
                                "嵌入向量类型错误: messageId=$messageId, 期望FloatArray，实际${vector?.javaClass?.simpleName}",
                            )
                            updateEmbeddingStatus(messageId, MessageEmbeddingEntity.STATUS_FAILED)
                            ModernResult.Error(
                                DataErrors.DataError.unknown(
                                    operationName = "generateEmbedding.typeError",
                                    message = UiText.DynamicString("嵌入向量类型错误"),
                                    cause = Exception(
                                        "Expected FloatArray but got ${vector?.javaClass?.simpleName}",
                                    ),
                                ),
                            )
                        } else {
                            logger.d(
                                "MessageEmbeddingService",
                                "嵌入向量生成成功: messageId=$messageId, dim=${vector.size}, time=${generationTime}ms",
                            )

                            // 创建嵌入实体
                            val embeddingEntity = MessageEmbeddingEntity(
                                messageId = messageId,
                                vector = vector.toByteArray(),
                                vectorDim = vector.size,
                                embeddingStatus = MessageEmbeddingEntity.STATUS_COMPLETED,
                                createdAt = System.currentTimeMillis(),
                                modelVersion = MessageEmbeddingEntity.BGE_MODEL_VERSION,
                                generationTimeMs = generationTime,
                                textLength = chatMessage.content.length,
                            )

                            // 保存到数据库
                            val embeddingId = messageEmbeddingDao.insertEmbedding(embeddingEntity)
                            val savedEmbedding = embeddingEntity.copy(id = embeddingId)

                            logger.d("MessageEmbeddingService", "嵌入向量保存成功: embeddingId=$embeddingId")
                            ModernResult.Success(savedEmbedding)
                        }
                    }
                    is ModernResult.Error -> {
                        logger.e(
                            "MessageEmbeddingService",
                            "嵌入向量生成失败: messageId=$messageId, error=${embeddingResult.error}",
                        )
                        updateEmbeddingStatus(messageId, MessageEmbeddingEntity.STATUS_FAILED)
                        ModernResult.Error(embeddingResult.error)
                    }
                    is ModernResult.Loading -> {
                        // 🔥 【逻辑修复】Loading状态在suspend函数中不应该出现，但提供更好的错误处理
                        logger.w("MessageEmbeddingService", "嵌入向量生成返回Loading状态，这不应该发生")
                        updateEmbeddingStatus(messageId, MessageEmbeddingEntity.STATUS_FAILED)
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "generateEmbeddingForMessage",
                                errorType = GlobalErrorType.System.Internal,
                                uiMessage = UiText.DynamicString("嵌入向量生成状态异常"),
                                cause = null,
                            ),
                        )
                    }
                    else -> {
                        logger.w("MessageEmbeddingService", "未知的嵌入向量生成结果类型")
                        updateEmbeddingStatus(messageId, MessageEmbeddingEntity.STATUS_FAILED)
                        ModernResult.Error(
                            ModernDataError(
                                operationName = "generateEmbeddingForMessage",
                                errorType = GlobalErrorType.System.Internal,
                                uiMessage = UiText.DynamicString("未知的嵌入向量生成结果类型"),
                                cause = null,
                            ),
                        )
                    }
                }
                result
            } catch (e: Exception) {
                logger.e("MessageEmbeddingService", "嵌入向量生成过程异常: messageId=$messageId", e)
                updateEmbeddingStatus(messageId, MessageEmbeddingEntity.STATUS_FAILED)
                ModernResult.Error(
                    ModernDataError(
                        operationName = "generateEmbeddingForMessage",
                        errorType = GlobalErrorType.System.Internal,
                        uiMessage = UiText.DynamicString("嵌入向量生成过程异常"),
                        cause = e,
                    ),
                )
            }
        }

    /**
     * 批量生成嵌入向量
     *
     * @param messageIds 消息ID列表
     * @param batchSize 批处理大小
     * @return 生成结果统计
     */
    suspend fun generateEmbeddingsBatch(
        messageIds: List<Long>,
        batchSize: Int = 5, // 🔥 减少批处理大小，降低内存压力
    ): ModernResult<BatchEmbeddingResult> = withContext(ioDispatcher) {
        try {
            logger.d("MessageEmbeddingService", "开始批量生成嵌入向量: count=${messageIds.size}, batchSize=$batchSize")

            val results = mutableListOf<ModernResult<MessageEmbeddingEntity>>()
            val startTime = System.currentTimeMillis()

            // 分批处理
            messageIds.chunked(batchSize).forEach { batch ->
                batch.forEach { messageId ->
                    val result = generateEmbeddingForMessage(messageId)
                    results.add(result)
                }
            }

            val totalTime = System.currentTimeMillis() - startTime
            val successCount = results.count { it is ModernResult.Success }
            val failureCount = results.count { it is ModernResult.Error }

            val batchResult = BatchEmbeddingResult(
                totalCount = messageIds.size,
                successCount = successCount,
                failureCount = failureCount,
                totalTimeMs = totalTime,
                averageTimeMs = if (messageIds.isNotEmpty()) totalTime / messageIds.size else 0L,
            )

            logger.d("MessageEmbeddingService", "批量生成完成: $batchResult")
            ModernResult.Success(batchResult)
        } catch (e: Exception) {
            logger.e("MessageEmbeddingService", "批量生成嵌入向量失败", e)
            ModernResult.Error(
                ModernDataError(
                    operationName = "generateEmbeddingsBatch",
                    errorType = GlobalErrorType.System.Internal,
                    uiMessage = UiText.DynamicString("批量生成嵌入向量失败"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * 处理待处理的消息
     *
     * @param limit 处理数量限制
     * @return 处理结果
     */
    suspend fun processPendingMessages(limit: Int = 50): ModernResult<BatchEmbeddingResult> =
        withContext(ioDispatcher) {
            try {
                logger.d("MessageEmbeddingService", "开始处理待处理消息: limit=$limit")

                val pendingMessageIds = messageEmbeddingDao.getPendingMessageIds(limit)
                if (pendingMessageIds.isEmpty()) {
                    logger.d("MessageEmbeddingService", "没有待处理的消息")
                    return@withContext ModernResult.Success(
                        BatchEmbeddingResult(0, 0, 0, 0L, 0L),
                    )
                }

                generateEmbeddingsBatch(pendingMessageIds)
            } catch (e: Exception) {
                logger.e("MessageEmbeddingService", "处理待处理消息失败", e)
                ModernResult.Error(
                    ModernDataError(
                        operationName = "processPendingMessages",
                        errorType = GlobalErrorType.System.Internal,
                        uiMessage = UiText.DynamicString("处理待处理消息失败"),
                        cause = e,
                    ),
                )
            }
        }

    /**
     * 获取嵌入统计信息
     */
    suspend fun getEmbeddingStats(): ModernResult<EmbeddingServiceStats> = withContext(ioDispatcher) {
        try {
            val stats = messageEmbeddingDao.getEmbeddingStats()
            val progress = messageEmbeddingDao.getEmbeddingProgress()

            val serviceStats = EmbeddingServiceStats(
                totalMessages = progress.totalMessages,
                embeddedMessages = progress.embeddedMessages,
                completionRate = progress.progressPercentage,
                averageGenerationTime = stats.avgGenerationTime ?: 0.0,
                averageTextLength = stats.avgTextLength ?: 0.0,
                failureRate = stats.failureRate,
            )

            ModernResult.Success(serviceStats)
        } catch (e: Exception) {
            logger.e("MessageEmbeddingService", "获取嵌入统计信息失败", e)
            ModernResult.Error(
                ModernDataError(
                    operationName = "getEmbeddingStats",
                    errorType = GlobalErrorType.Data.Access,
                    uiMessage = UiText.DynamicString("获取嵌入统计信息失败"),
                    cause = e,
                ),
            )
        }
    }

    /**
     * 清理失败的嵌入记录
     */
    suspend fun cleanupFailedEmbeddings(olderThanHours: Int = 24): ModernResult<Int> =
        withContext(ioDispatcher) {
            try {
                val cutoffTime = System.currentTimeMillis() - (olderThanHours * 60 * 60 * 1000L)
                val deletedCount = messageEmbeddingDao.cleanupFailedEmbeddings(cutoffTime)
                logger.d("MessageEmbeddingService", "清理失败嵌入记录完成: 删除了${deletedCount}条记录")
                ModernResult.Success(deletedCount)
            } catch (e: Exception) {
                logger.e("MessageEmbeddingService", "清理失败嵌入记录失败", e)
                ModernResult.Error(
                    ModernDataError(
                        operationName = "cleanupFailedEmbeddings",
                        errorType = GlobalErrorType.Data.Access,
                        uiMessage = UiText.DynamicString("清理失败嵌入记录失败"),
                        cause = e,
                    ),
                )
            }
        }

    /**
     * 更新嵌入状态
     */
    private suspend fun updateEmbeddingStatus(messageId: Long, status: String) {
        try {
            messageEmbeddingDao.updateEmbeddingStatus(messageId, status)
        } catch (e: Exception) {
            logger.w("MessageEmbeddingService", "更新嵌入状态失败: messageId=$messageId, status=$status", e)
        }
    }
}

/**
 * 批量嵌入结果
 */
data class BatchEmbeddingResult(
    val totalCount: Int,
    val successCount: Int,
    val failureCount: Int,
    val totalTimeMs: Long,
    val averageTimeMs: Long,
) {
    val successRate: Float
        get() = if (totalCount > 0) successCount.toFloat() / totalCount else 0f
}

/**
 * 嵌入服务统计信息
 */
data class EmbeddingServiceStats(
    val totalMessages: Int,
    val embeddedMessages: Int,
    val completionRate: Float,
    val averageGenerationTime: Double,
    val averageTextLength: Double,
    val failureRate: Float,
)

package com.example.gymbro.data.service.integration

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.common.CommonFeatureErrors
import com.example.gymbro.core.ml.service.BgeEmbeddingService
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.data.exercise.fromName
import com.example.gymbro.domain.exercise.model.Exercise
import com.example.gymbro.domain.exercise.model.ExerciseDraft
import com.example.gymbro.domain.exercise.model.getDisplayName
import com.example.gymbro.domain.exercise.model.integration.AlternativeExercise
import com.example.gymbro.domain.exercise.model.integration.ExerciseMatch
import com.example.gymbro.domain.exercise.model.integration.MatchReason
import com.example.gymbro.domain.exercise.repository.ExerciseRepository
import com.example.gymbro.domain.service.integration.SmartExerciseMatcher
import com.example.gymbro.domain.workout.model.context.WorkoutContext
import com.example.gymbro.shared.models.exercise.Equipment
import com.example.gymbro.shared.models.exercise.MuscleGroup
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 智能运动匹配器实现
 * 使用FTS召回 + BGE语义重排的两阶段检索策略
 */
@Singleton
class SmartExerciseMatcherImpl
@Inject
constructor(
    private val exerciseRepository: ExerciseRepository,
    private val bgeEmbeddingService: BgeEmbeddingService,
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
) : SmartExerciseMatcher {
    companion object {
        private const val FTS_RECALL_LIMIT = 20 // FTS召回数量
        private const val BGE_RERANK_LIMIT = 5 // BGE重排输出数量
        private const val HIGH_CONFIDENCE_THRESHOLD = 0.9f
        private const val GOOD_CONFIDENCE_THRESHOLD = 0.7f
        private const val MIN_CONFIDENCE_THRESHOLD = 0.3f
    }

    /**
     * 查找最佳匹配
     * 实现两阶段检索：FTS召回 -> BGE重排
     */
    override suspend fun findBestMatch(
        exerciseName: String,
        muscleGroups: List<String>,
        equipment: List<String>,
        userContext: WorkoutContext?,
    ): ModernResult<ExerciseMatch> =
        withContext(dispatcher) {
            val startTime = System.currentTimeMillis()

            try {
                Timber.d("SmartExerciseMatcher: 开始匹配 exerciseName=$exerciseName")

                // 阶段1: 精确匹配检查
                val exactMatch = checkExactMatch(exerciseName)
                if (exactMatch != null) {
                    val duration = System.currentTimeMillis() - startTime
                    Timber.d("SmartExerciseMatcher: 精确匹配成功 duration=${duration}ms")
                    return@withContext ModernResult.Success(exactMatch)
                }

                // 阶段2: FTS召回
                val ftsResults = performFtsRecall(exerciseName, muscleGroups, equipment)
                if (ftsResults.isEmpty()) {
                    Timber.w("SmartExerciseMatcher: FTS召回无结果")
                    return@withContext ModernResult.Success(createFallbackMatch(exerciseName))
                }

                // 阶段3: BGE语义重排
                val bgeResults = performBgeRerank(exerciseName, ftsResults)
                val bestMatch = selectBestMatch(bgeResults, userContext)

                val duration = System.currentTimeMillis() - startTime
                Timber.d(
                    "SmartExerciseMatcher: 匹配完成 confidence=${bestMatch.confidence} duration=${duration}ms",
                )

                // 检查性能指标
                if (duration > 50) {
                    Timber.w("SmartExerciseMatcher: 匹配耗时超标 duration=${duration}ms")
                }

                ModernResult.Success(bestMatch)
            } catch (e: Exception) {
                Timber.e(e, "SmartExerciseMatcher: 匹配异常")
                ModernResult.Error(
                    CommonFeatureErrors.CoachError.processingFailed(
                        operationName = "SmartExerciseMatcher.findBestMatch",
                        message = UiText.DynamicString("智能匹配失败"),
                        processType = "exercise_matching",
                        reason = "matching_failed",
                        cause = e,
                        metadataMap =
                        mapOf(
                            "exercise_name" to exerciseName,
                        ),
                    ),
                )
            }
        }

    /**
     * 批量匹配
     */
    override suspend fun batchMatch(
        exercises: List<ExerciseDraft>,
    ): ModernResult<List<ExerciseMatch>> =
        withContext(dispatcher) {
            val startTime = System.currentTimeMillis()

            try {
                Timber.d("SmartExerciseMatcher: 开始批量匹配 count=${exercises.size}")

                // 并行处理所有匹配
                val matches =
                    exercises
                        .map { exercise ->
                            async {
                                findBestMatch(
                                    exerciseName =
                                    when (val name = exercise.name) {
                                        is UiText.DynamicString -> name.value
                                        else -> "Unknown Exercise"
                                    },
                                    muscleGroups = exercise.targetMuscles,
                                    equipment = exercise.equipment,
                                ).getOrNull() ?: return@async ExerciseMatch(
                                    exercise =
                                    Exercise(
                                        id = "unknown",
                                        name = exercise.name,
                                        muscleGroup = MuscleGroup.OTHER,
                                        equipment = exercise.equipment.mapNotNull { Equipment.fromName(it) },
                                        description = UiText.DynamicString("未知运动"),
                                        targetMuscles =
                                        exercise.targetMuscles
                                            .mapNotNull { MuscleGroup.fromName(it) }
                                            .ifEmpty { listOf(MuscleGroup.OTHER) },
                                    ),
                                    confidence = 0.1f,
                                    matchReason = MatchReason.FALLBACK,
                                    alternatives = emptyList(),
                                )
                            }
                        }.awaitAll()

                val duration = System.currentTimeMillis() - startTime
                val avgConfidence = matches.map { it.confidence }.average()

                Timber.d(
                    "SmartExerciseMatcher: 批量匹配完成 count=${matches.size} avgConfidence=$avgConfidence duration=${duration}ms",
                )

                ModernResult.Success(matches)
            } catch (e: Exception) {
                Timber.e(e, "SmartExerciseMatcher: 批量匹配异常")
                ModernResult.Error(
                    CommonFeatureErrors.CoachError.processingFailed(
                        operationName = "SmartExerciseMatcher.batchMatch",
                        message = UiText.DynamicString("批量匹配失败"),
                        processType = "batch_matching",
                        reason = "batch_failed",
                        cause = e,
                        metadataMap =
                        mapOf(
                            "exercise_count" to exercises.size.toString(),
                        ),
                    ),
                )
            }
        }

    /**
     * 精确匹配检查
     */
    private suspend fun checkExactMatch(exerciseName: String): ExerciseMatch? =
        try {
            exerciseRepository
                .findByExactName(exerciseName)
                .getOrNull()
                ?.let { exercise ->
                    ExerciseMatch(
                        exercise = exercise,
                        confidence = 1.0f,
                        matchReason = MatchReason.EXACT_NAME,
                        alternatives = emptyList(),
                    )
                }
        } catch (e: Exception) {
            Timber.w(e, "SmartExerciseMatcher: 精确匹配检查失败")
            null
        }

    /**
     * FTS召回阶段
     */
    private suspend fun performFtsRecall(
        exerciseName: String,
        muscleGroups: List<String>,
        equipment: List<String>,
    ): List<Exercise> =
        try {
            // 构建FTS查询
            val query = buildFtsQuery(exerciseName, muscleGroups, equipment)

            // 执行FTS搜索
            exerciseRepository
                .searchExercises(query, FTS_RECALL_LIMIT)
                .getOrNull()
                ?: emptyList()
        } catch (e: Exception) {
            Timber.w(e, "SmartExerciseMatcher: FTS召回失败")
            emptyList()
        }

    /**
     * BGE语义重排阶段
     */
    private suspend fun performBgeRerank(
        queryText: String,
        candidates: List<Exercise>,
    ): List<ExerciseMatch> {
        if (candidates.isEmpty()) {
            return emptyList()
        }

        return try {
            // 构建候选文本列表
            val candidateTexts =
                candidates.map { exercise ->
                    buildExerciseSearchText(exercise)
                }

            // 计算语义相似度
            val similarities =
                bgeEmbeddingService
                    .calculateBatchSimilarity(
                        queryText = queryText,
                        candidates = candidateTexts,
                    ).getOrNull() ?: return candidates.map {
                    createLowConfidenceMatch(it)
                }

            // 组合FTS分数和BGE分数
            candidates
                .zip(similarities)
                .map { (exercise, similarity) ->
                    val combinedScore = calculateCombinedScore(exercise, similarity)

                    ExerciseMatch(
                        exercise = exercise,
                        confidence = combinedScore,
                        matchReason = if (similarity > 0.8f) MatchReason.SEMANTIC else MatchReason.MUSCLE_GROUP,
                        alternatives = emptyList(),
                    )
                }.sortedByDescending { it.confidence }
                .take(BGE_RERANK_LIMIT)
        } catch (e: Exception) {
            Timber.w(e, "SmartExerciseMatcher: BGE重排失败")
            // 降级到基础匹配
            candidates.map { createLowConfidenceMatch(it) }
        }
    }

    /**
     * 构建FTS查询
     */
    private fun buildFtsQuery(
        exerciseName: String,
        muscleGroups: List<String>,
        equipment: List<String>,
    ): String {
        val terms = mutableListOf<String>()

        // 主要名称
        terms.add(exerciseName)

        // 肌肉群
        muscleGroups.forEach { muscle ->
            terms.add(muscle)
        }

        // 器械
        equipment.forEach { equip ->
            terms.add(equip)
        }

        return terms.joinToString(" OR ")
    }

    /**
     * 构建运动搜索文本
     */
    private fun buildExerciseSearchText(exercise: Exercise): String =
        buildString {
            append(
                when (val name = exercise.name) {
                    is UiText.DynamicString -> name.value
                    else -> "Unknown Exercise"
                },
            )
            append(" ")
            append(exercise.targetMuscles.joinToString(" ") { it.getDisplayName() })
            append(" ")
            append(exercise.equipment.joinToString(" "))
            val description =
                when (val desc = exercise.description) {
                    is UiText.DynamicString -> desc.value
                    else -> ""
                }
            if (description.isNotBlank()) {
                append(" ")
                append(description)
            }
        }

    /**
     * 计算组合分数 (FTS + BGE)
     */
    private fun calculateCombinedScore(
        exercise: Exercise,
        bgeSimilarity: Float,
    ): Float {
        // 权重: BGE语义 70%, FTS基础 30%
        val bgeWeight = 0.7f
        val ftsWeight = 0.3f

        // FTS基础分数(简化版，实际应从数据库获取)
        val ftsScore = 0.5f

        return (bgeSimilarity * bgeWeight + ftsScore * ftsWeight).coerceIn(0f, 1f)
    }

    /**
     * 选择最佳匹配
     */
    private fun selectBestMatch(
        results: List<ExerciseMatch>,
        userContext: WorkoutContext?,
    ): ExerciseMatch {
        if (results.isEmpty()) {
            return createFallbackMatch("未知运动")
        }

        var bestMatch = results.first()

        // 用户偏好加权
        userContext?.favoriteExercises?.let { favorites ->
            val favoriteMatch =
                results.find { match ->
                    favorites.contains(match.exercise.id)
                }
            if (favoriteMatch != null && favoriteMatch.confidence > 0.5f) {
                bestMatch =
                    favoriteMatch.copy(
                        confidence = (favoriteMatch.confidence + 0.1f).coerceAtMost(1.0f),
                    )
            }
        }

        // 添加替代选项
        val alternatives =
            results.drop(1).take(3).map { match ->
                AlternativeExercise(
                    exercise = match.exercise,
                    similarity = match.confidence,
                    reason = "语义相似",
                )
            }

        return bestMatch.copy(alternatives = alternatives)
    }

    /**
     * 创建低置信度匹配
     */
    private fun createLowConfidenceMatch(exercise: Exercise): ExerciseMatch =
        ExerciseMatch(
            exercise = exercise,
            confidence = 0.3f,
            matchReason = MatchReason.EQUIPMENT,
            alternatives = emptyList(),
        )

    /**
     * 创建降级匹配
     */
    private fun createFallbackMatch(exerciseName: String): ExerciseMatch {
        // 创建一个通用的未知运动
        val fallbackExercise =
            Exercise(
                id = "unknown",
                name = UiText.DynamicString(exerciseName),
                muscleGroup = MuscleGroup.OTHER,
                equipment = emptyList(),
                description = UiText.DynamicString("未识别的运动，请手动选择"),
                targetMuscles = emptyList(),
                instructions = emptyList(),
            )

        return ExerciseMatch(
            exercise = fallbackExercise,
            confidence = 0.0f,
            matchReason = MatchReason.FALLBACK,
            alternatives = emptyList(),
        )
    }
}

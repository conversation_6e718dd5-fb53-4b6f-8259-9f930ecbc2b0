{"account": "", "credential": "", "remoteDir": "/obsin/docs", "remoteCacheDir": "", "useGitStyle": false, "conflictStrategy": "diff-match-patch", "oauthResponseText": "h76vu0quW831Aok6imEmQS9DlsCNea0_U0dtWAoWBH2M0cxlfiB5ecUt9837xnD_EMtqZXHRtXI09VeSWkmA2tJn5vA7s2ppIiM_Hwvzt4KRAxghNlSCf0u4y3LkI-LHuXGZYCx8fHzP-5hxHeQ4PaldHbzma2JYqKSmfB_bnbq4joW6knC7", "loginMode": "sso", "confirmBeforeSync": true, "syncMode": "loose", "filters": [".git", ".DS_Store", ".Trash"], "skipLargeFiles": {"maxSize": "30 MB"}}
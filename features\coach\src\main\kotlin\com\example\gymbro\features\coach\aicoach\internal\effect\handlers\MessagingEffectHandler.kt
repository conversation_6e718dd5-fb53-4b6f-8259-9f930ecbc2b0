package com.example.gymbro.features.coach.aicoach.internal.effect.handlers

import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.domain.coach.usecase.GetQuickActionByIdUseCase
import com.example.gymbro.domain.shared.common.GetQuickActionCategoriesUseCase
import com.example.gymbro.domain.shared.common.model.QuickAction
import com.example.gymbro.domain.shared.common.model.QuickActionCategoryGroup
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.shared.services.SmartSearchService
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * MessagingEffectHandler - 处理消息、搜索、快速操作相关的Effect
 *
 * 负责：
 * 1. 搜索功能
 * 2. 快速操作加载
 * 3. 输入预填充
 * 4. 错误显示
 */
internal class MessagingEffectHandler @Inject constructor(
    private val getQuickActionCategoriesUseCase: GetQuickActionCategoriesUseCase,
    private val getQuickActionByIdUseCase: GetQuickActionByIdUseCase,
    private val smartSearchService: SmartSearchService,
    @IoDispatcher private val ioDispatcher: CoroutineDispatcher,
) {

    private lateinit var handlerScope: CoroutineScope
    private lateinit var sendIntent: (AiCoachContract.Intent) -> Unit

    fun initialize(
        scope: CoroutineScope,
        intentSender: (AiCoachContract.Intent) -> Unit,
    ) {
        this.handlerScope = scope
        this.sendIntent = intentSender
    }

    fun handleLoadQuickActionCategories() {
        Timber.d("📋 加载快速操作分类")

        getQuickActionCategoriesUseCase()
            .flowOn(ioDispatcher)
            .onEach { result: ModernResult<List<QuickActionCategoryGroup>> ->
                when (result) {
                    is ModernResult.Success<List<QuickActionCategoryGroup>> -> {
                        Timber.d("✅ 成功加载${result.data.size}个快速操作分类")
                        sendIntent(
                            AiCoachContract.Intent.QuickActionCategoriesLoadedResult(
                                result.data.toImmutableList(),
                            ),
                        )
                    }
                    is ModernResult.Error -> {
                        Timber.e("❌ 加载快速操作分类失败: ${result.error}")
                        sendIntent(
                            AiCoachContract.Intent.OperationFailed(AiCoachContract.ErrorCode.UNKNOWN_ERROR),
                        )
                    }
                    is ModernResult.Loading -> {
                        Timber.d("⏳ 正在加载快速操作分类...")
                    }
                }
            }
    }

    fun handleLoadQuickActionById(effect: AiCoachContract.Effect.LoadQuickActionById) {
        handlerScope.launch {
            try {
                Timber.d("🎯 加载快速操作: ${effect.actionId}")

                val result = getQuickActionByIdUseCase(effect.actionId)

                when (result) {
                    is ModernResult.Success<QuickAction> -> {
                        Timber.d("✅ 成功加载快速操作: ${result.data.displayText}")
                        sendIntent(AiCoachContract.Intent.ActionContextLoadedResult(result.data))
                    }
                    is ModernResult.Error -> {
                        Timber.e("❌ 加载快速操作失败: ${result.error}")
                        sendIntent(
                            AiCoachContract.Intent.OperationFailed(AiCoachContract.ErrorCode.UNKNOWN_ERROR),
                        )
                    }
                    is ModernResult.Loading -> {
                        Timber.d("⏳ 正在加载快速操作...")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "❌ 加载快速操作异常")
                sendIntent(AiCoachContract.Intent.OperationFailed(AiCoachContract.ErrorCode.UNKNOWN_ERROR))
            }
        }
    }

    fun handleLoadQuickActions() {
        Timber.d("🎯 加载快速操作")
        // 加载默认的快速操作建议
        val defaultActions = listOf(
            QuickAction(
                id = "workout_plan",
                displayText = "训练计划",
                prefillText = "请帮我制定一个适合的训练计划：",
                iconName = "fitness_center",
            ),
            QuickAction(
                id = "nutrition_guide",
                displayText = "营养指导",
                prefillText = "请为我提供营养建议：",
                iconName = "restaurant",
            ),
            QuickAction(
                id = "weight_loss",
                displayText = "减脂方案",
                prefillText = "请帮我设计减脂训练方案：",
                iconName = "monitor_weight",
            ),
            QuickAction(
                id = "muscle_gain",
                displayText = "增肌计划",
                prefillText = "请制定增肌训练计划：",
                iconName = "sports_gymnastics",
            ),
        )

        // TODO: 实际应该从UseCase加载，这里先使用默认值
        Timber.d("✅ 已设置${defaultActions.size}个默认快速操作")
    }

    fun handlePerformSearch(effect: AiCoachContract.Effect.PerformSearch) {
        handlerScope.launch {
            try {
                Timber.d("🔍 执行搜索: query=${effect.query}")

                val result = smartSearchService.smartSearch(
                    query = effect.query,
                    sessionId = effect.sessionId,
                )

                when (result) {
                    is ModernResult.Success -> {
                        Timber.d("✅ 搜索成功: 找到${result.data.size}条结果")
                        // 将CoachMessage转换为MessageUi
                        val messageUiList = result.data.map { coachMessage ->
                            when (coachMessage) {
                                is com.example.gymbro.domain.coach.model.CoachMessage.UserMessage -> {
                                    AiCoachContract.MessageUi(
                                        id = coachMessage.id,
                                        content = coachMessage.content,
                                        isFromUser = true,
                                        timestamp = kotlinx.datetime.Instant.fromEpochMilliseconds(
                                            coachMessage.timestamp,
                                        ),
                                    )
                                }
                                is com.example.gymbro.domain.coach.model.CoachMessage.AiMessage -> {
                                    AiCoachContract.MessageUi(
                                        id = coachMessage.id,
                                        content = coachMessage.content,
                                        isFromUser = false,
                                        timestamp = kotlinx.datetime.Instant.fromEpochMilliseconds(
                                            coachMessage.timestamp,
                                        ),
                                    )
                                }
                            }
                        }
                        sendIntent(
                            AiCoachContract.Intent.SearchResultsLoadedResult(
                                query = effect.query,
                                results = messageUiList.toImmutableList(),
                                sessionId = effect.sessionId,
                            ),
                        )
                    }
                    is ModernResult.Error -> {
                        Timber.e("❌ 搜索失败: ${result.error}")
                        sendIntent(
                            AiCoachContract.Intent.SearchFailedResult(
                                query = effect.query,
                                error = result.error.toString(),
                            ),
                        )
                    }
                    is ModernResult.Loading -> {
                        Timber.d("⏳ 正在搜索...")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "❌ 搜索异常")
                sendIntent(
                    AiCoachContract.Intent.SearchFailedResult(
                        query = effect.query,
                        error = "搜索出现异常: ${e.message}",
                    ),
                )
            }
        }
    }

    fun handlePrefillInputAndNavigate(effect: AiCoachContract.Effect.PrefillInputAndNavigate) {
        Timber.d("📝 预填充输入并导航: ${effect.prefillText}")
        sendIntent(AiCoachContract.Intent.PreFillInput(effect.prefillText))
    }

    fun handleShowError(effect: AiCoachContract.Effect.ShowError) {
        Timber.e("❌ 显示错误: ${effect.error}")
        // UI层会处理错误显示，这里只记录日志
    }

    fun handleGenerateSummary(effect: AiCoachContract.Effect.GenerateSummary) {
        Timber.d("📄 生成摘要: sessionId=${effect.sessionId}")
        // TODO: 实现摘要生成逻辑
        Timber.d("⚠️ 摘要生成功能待实现")
    }
}

package com.example.gymbro.domain.coach.usecase

import com.example.gymbro.core.ai.prompt.builder.ConversationTurn
import com.example.gymbro.core.ai.prompt.builder.LayeredPromptBuilder
import com.example.gymbro.core.di.qualifiers.IoDispatcher
import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ml.embedding.EngineStatus
import com.example.gymbro.core.ml.service.BgeEmbeddingService
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.util.Constants
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.coach.model.CoachMessage
import com.example.gymbro.domain.exercise.repository.HybridSearchRepository
import com.example.gymbro.domain.exercise.repository.RelevantContent
import com.example.gymbro.domain.shared.base.modern.ModernFlowUseCase
import com.example.gymbro.domain.user.repository.UserDataProvider
import com.example.gymbro.domain.workout.model.json.RawWorkoutState
import com.example.gymbro.domain.workout.model.json.VectorizedWorkoutState
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * @param aiCoachUseCase AI教练用例
 * @param chatSessionManagementUseCase 聊天会话管理用例
 * @param bgeEmbeddingService BGE向量化服务
 * @param hybridSearchRepository 混合搜索仓库
 * @param initializeBgeEngineUseCase 初始化BGE引擎用例
 * @param layeredPromptBuilder LayeredPromptBuilder
 * @param getCurrentUserIdUseCase 获取当前用户ID用例
 * @param userDataCenterApi 用户数据中心API，用于获取统一的用户上下文数据
 * @param dispatcher IO调度器
 * @param logger 日志记录器
 */
@Singleton
class SendChatMessageAndGetResponseUseCase
@Inject
constructor(
    private val aiCoachUseCase: AiCoachUseCase,
    private val chatSessionManagementUseCase: ChatSessionManagementUseCase,
    private val bgeEmbeddingService: BgeEmbeddingService,
    private val hybridSearchRepository: HybridSearchRepository,
    private val initializeBgeEngineUseCase: InitializeBgeEngineUseCase,
    private val layeredPromptBuilder: LayeredPromptBuilder,
    private val getCurrentUserIdUseCase: GetCurrentUserIdUseCase,
    private val userDataProvider: UserDataProvider,
    @IoDispatcher private val dispatcher: CoroutineDispatcher,
    logger: Logger,
) : ModernFlowUseCase<SendChatMessageAndGetResponseUseCase.Params, String>(dispatcher, logger) {

    /**
     * 参数数据类
     *
     * @property sessionId 会话ID
     * @property userMessageContent 用户消息内容
     * @property workoutContext 训练上下文（可选）
     * @property messageCountInSession 当前会话的消息数
     * @property currentModel 当前选择的模型名称（用于DeepSeek专用prompt切换）🆕
     */
    data class Params(
        val sessionId: String,
        val userMessageContent: String,
        val workoutContext: WorkoutContext? = null,
        val messageCountInSession: Int = 0,
        val currentModel: String? = null, // 🆕 新增模型参数
    )

    /**
     * 训练上下文数据
     * 用于增强AI响应的上下文信息
     */
    data class WorkoutContext(
        val currentExerciseId: String?,
        val completedExercises: Int,
        val totalExercises: Int,
        val currentWeight: Float?,
        val currentReps: Int?,
        val sessionDurationMin: Int,
        val exerciseDescription: String? = null,
    ) {
        fun toRawWorkoutState(sessionId: String): RawWorkoutState {
            return RawWorkoutState(
                sessionId = sessionId,
                description = exerciseDescription ?: "",
                exerciseId = currentExerciseId,
                completedExercises = completedExercises,
                totalExercises = totalExercises,
                currentWeight = currentWeight,
                currentReps = currentReps,
                sessionDurationMin = sessionDurationMin,
            )
        }
    }

    /**
     * 创建发送消息并获取响应的流
     *
     * @param parameters 包含会话ID和消息内容的参数
     * @return 包含AI响应消息的流
     */
    override fun createFlow(parameters: Params): Flow<ModernResult<String>> =
        flow {
            logger.d("发送消息并获取AI响应: sessionId=${parameters.sessionId}")

            try {
                // 验证参数
                if (parameters.sessionId.isBlank()) {
                    throw IllegalArgumentException("Session ID cannot be blank")
                }

                if (parameters.userMessageContent.isBlank()) {
                    throw IllegalArgumentException("User message content cannot be blank")
                }

                // 🔥 修复1：获取真实用户ID
                val userIdResult = getCurrentUserIdUseCase().first()
                val currentUserId = when (userIdResult) {
                    is ModernResult.Success -> {
                        val userId = userIdResult.data
                        if (userId.isNullOrBlank()) {
                            logger.w("用户ID为空，使用anonymous_user作为fallback")
                            "anonymous_user"
                        } else {
                            logger.d("获取到真实用户ID: $userId")
                            userId
                        }
                    }
                    is ModernResult.Error -> {
                        logger.w("获取用户ID失败，使用anonymous_user作为fallback: ${userIdResult.error}")
                        "anonymous_user"
                    }
                    is ModernResult.Loading -> {
                        logger.w("获取用户ID处于Loading状态，使用anonymous_user作为fallback")
                        "anonymous_user"
                    }
                }

                // 🔥 修复2：构建真实对话历史
                val conversationHistory = buildConversationHistory(
                    sessionId = parameters.sessionId,
                    maxHistoryCount = 8, // 最近8轮对话，平衡上下文和token预算
                )

                // 🔥 确保BGE引擎已初始化 - 双重保障机制
                ensureBgeEngineReady()

                // === 增强AI功能：向量化和内容检索 ===
                val vectorizedState = parameters.workoutContext?.let { context ->
                    vectorizeWorkoutContext(context, parameters.sessionId)
                }

                val relevantContent = retrieveRelevantContent(
                    userQuery = parameters.userMessageContent,
                    vectorizedState = vectorizedState,
                )

                // 🆕 获取用户数据用于个性化prompt - 使用UserDataProvider适配器
                val userAiContext = try {
                    logger.d("🔍 开始获取用户AI上下文...")
                    // 🔥 关键修复：使用 UserDataProvider 获取用户AI上下文
                    when (val result = userDataProvider.getCurrentUserAiContext()) {
                        is ModernResult.Success -> {
                            logger.d("✅ 用户AI上下文获取成功: userId=${result.data?.userId ?: "null"}")
                            result.data
                        }
                        is ModernResult.Error -> {
                            logger.w("⚠️ [UserDataProvider] 用户AI上下文获取失败，将使用默认上下文: ${result.error}")
                            null
                        }
                        is ModernResult.Loading -> {
                            logger.w("⏳ 用户AI上下文正在加载中，将使用默认上下文")
                            null
                        }
                    }
                } catch (e: Exception) {
                    logger.e(e, "❌ [UserDataProvider] 获取用户AI上下文时发生异常，将使用默认上下文继续执行")
                    null
                }

                // 🔥 修复3：使用LayeredPromptBuilder构建完美提示词
                // 🔥 临时修复：始终包含系统提示词，确保AI行为一致
                val forceOmitSystemPrompt = false // 暂时禁用智能省略，确保系统提示词生效

                // 🔥 Priority 2 修复：使用 PromptRegistry 的当前激活配置构建 SystemLayer
                // 通过 LayeredPromptBuilder 的公共方法获取当前 SystemLayer
                val systemLayer = layeredPromptBuilder.getCurrentSystemLayer()

                // 🆕 构建包含用户AI上下文的MemoryContext
                val memoryContext = userAiContext?.let { context ->
                    com.example.gymbro.core.ai.prompt.memory.MemoryContext(
                        userId = currentUserId,
                        query = parameters.userMessageContent,
                        contextType = com.example.gymbro.core.ai.prompt.memory.MemoryContextType.PROFILE,
                        metadata = mapOf<String, Any>(
                            "user_ai_context" to context,
                            "has_user_context" to true,
                            "user_id" to context.userId,
                            "display_name" to (context.displayName ?: "用户"),
                            "fitness_level" to (context.fitnessLevel?.displayName ?: "未设置"),
                            "fitness_goals" to context.getFormattedGoals(),
                            "workout_days" to context.getFormattedWorkoutDays(),
                            "height" to (context.height ?: 0.0f),
                            "weight" to (context.weight ?: 0.0f),
                            "gender" to (context.gender?.name ?: "未设置"),
                            "is_complete_profile" to context.isComplete,
                            "has_basic_info" to context.hasBasicInfo,
                            "has_fitness_info" to context.hasFitnessInfo,
                            "has_body_data" to context.hasBodyData,
                            "total_activity_count" to context.totalActivityCount,
                            "weekly_active_minutes" to context.weeklyActiveMinutes,
                            "bio" to (context.bio ?: ""),
                            "bmi" to (context.getBmi() ?: 0.0f),
                            "context_version" to context.contextVersion,
                        ),
                    )
                }

                val messages = layeredPromptBuilder.buildChatMessagesWithMemory(
                    systemLayer = systemLayer, // 🔥 使用当前激活的 SystemLayer 而不是 null
                    userInput = parameters.userMessageContent,
                    history = conversationHistory, // 🔥 修复：传递真实历史记录
                    memoryContext = memoryContext, // 🆕 传递包含统一用户数据的Memory上下文
                    forceOmitSystemPrompt = forceOmitSystemPrompt,
                    model = parameters.currentModel, // 🆕 传递模型参数用于DeepSeek专用prompt切换
                )

                logger.d(
                    "🎯 Prompt构建完成: 用户ID=$currentUserId, 历史轮次=${conversationHistory.size}, 消息总数=${messages.size}, 省略系统提示词=$forceOmitSystemPrompt",
                )

                // 🔥 修复：直接传递消息列表，避免重复prompt构建
                // 不再序列化，让AiStreamRepositoryImpl直接使用构建好的消息
                logger.d("🚀 开始调用 aiCoachUseCase.streamMessage() - sessionId=${parameters.sessionId}")
                logger.d("📝 消息列表: ${messages.size}条消息，首条=${messages.firstOrNull()?.role}")

                // 🔥 【Token流修复】添加Flow启动和collect日志
                val flow = aiCoachUseCase.streamMessage(parameters.sessionId, messages)
                // 🔥 【关闭TOKEN-FLOW日志】Flow 准备开始
                logger.d("🔄 获取到Flow，准备collect")

                flow.collect { token ->
                    // 🔥 【关闭TOKEN-FLOW日志】收到 token
                    logger.d("🔥 collect收到token: length=${token.length}")
                    emit(ModernResult.Success(token)) // 🔥 将token包裹在ModernResult.Success中
                    // 🔥 【关闭TOKEN-FLOW日志】token 已发送
                    logger.d("🔥 token已emit为ModernResult.Success")
                }
            } catch (e: Exception) {
                logger.e(e, "发送消息失败")
                // 在Flow中，通过emit一个Error来传播错误
                emit(
                    ModernResult.error(
                        BusinessErrors.BusinessError.rule(
                            operationName = "sendChatMessageAndGetResponse",
                            message = UiText.DynamicString("发送消息时发生未知错误: ${e.message}"),
                            metadataMap = mapOf("sessionId" to parameters.sessionId),
                        ),
                    ),
                )
            }
        }

    /**
     * 🔥 新增：构建对话历史
     *
     * 从会话中提取最近的对话轮次，转换为LayeredPromptBuilder期望的格式
     *
     * @param sessionId 会话ID
     * @param maxHistoryCount 最大历史轮次数，默认8轮
     * @return 对话历史列表
     */
    private suspend fun buildConversationHistory(
        sessionId: String,
        maxHistoryCount: Int = 8,
    ): List<ConversationTurn> {
        return try {
            logger.d("🔍 开始构建对话历史: sessionId=$sessionId, maxCount=$maxHistoryCount")

            // 使用ChatSessionManagementUseCase获取会话的最近消息
            val sessionResult = chatSessionManagementUseCase.loadSessionWithRecentMessages(
                sessionId = sessionId,
                messageCount = maxHistoryCount * 2, // 每轮对话包含user和assistant两条消息
            )

            when (sessionResult) {
                is ModernResult.Success -> {
                    val session = sessionResult.data
                    if (session != null && session.messages.isNotEmpty()) {
                        // 将消息按时间排序，然后配对为对话轮次
                        val sortedMessages = session.messages.sortedBy { it.timestamp }
                        val conversationTurns = mutableListOf<ConversationTurn>()

                        // 将连续的用户-助手消息配对
                        var i = 0
                        while (i < sortedMessages.size - 1) {
                            val currentMessage = sortedMessages[i]
                            val nextMessage = sortedMessages[i + 1]

                            // 检查是否为用户消息后跟助手消息的模式
                            if (currentMessage is CoachMessage.UserMessage &&
                                nextMessage is CoachMessage.AiMessage
                            ) {
                                conversationTurns.add(
                                    ConversationTurn(
                                        user = currentMessage.content,
                                        assistant = nextMessage.content,
                                    ),
                                )
                                i += 2 // 跳过已配对的两条消息
                            } else {
                                i++ // 如果不匹配，继续寻找
                            }

                            // 限制历史轮次数量
                            if (conversationTurns.size >= maxHistoryCount) {
                                break
                            }
                        }

                        logger.d("✅ 对话历史构建完成: 找到${conversationTurns.size}轮对话")
                        conversationTurns
                    } else {
                        logger.d("📝 会话为空或无消息，返回空历史")
                        emptyList()
                    }
                }
                is ModernResult.Error -> {
                    logger.w("⚠️ 获取会话消息失败，返回空历史: ${sessionResult.error}")
                    emptyList()
                }
                is ModernResult.Loading -> {
                    logger.w("⚠️ 获取会话消息处于Loading状态，返回空历史")
                    emptyList()
                }
            }
        } catch (e: Exception) {
            logger.e(e, "❌ 构建对话历史异常，返回空历史")
            emptyList()
        }
    }

    /**
     * 向量化训练上下文
     * 实现增强1.md中的TimelineVectorizationEngine功能集成
     */
    private suspend fun vectorizeWorkoutContext(
        context: WorkoutContext,
        sessionId: String,
    ): VectorizedWorkoutState? {
        return try {
            val rawState = context.toRawWorkoutState(sessionId)
            // 直接使用BGE服务进行向量化
            val result = bgeEmbeddingService.embedText(rawState.description)

            when (result) {
                is ModernResult.Success -> {
                    logger.d("训练上下文向量化成功: sessionId=$sessionId")
                    // 构建完整的向量化状态对象
                    VectorizedWorkoutState(
                        sessionId = sessionId,
                        originalText = rawState.description,
                        vector = result.data,
                        vectorDim = result.data.size,
                        timestamp = rawState.timestamp,
                        exerciseId = rawState.exerciseId,
                        metadata = mapOf(
                            "completed_exercises" to rawState.completedExercises,
                            "total_exercises" to rawState.totalExercises,
                            "current_weight" to (rawState.currentWeight ?: 0f),
                            "current_reps" to (rawState.currentReps ?: 0),
                            "session_duration_min" to rawState.sessionDurationMin,
                        ),
                    )
                }
                is ModernResult.Error -> {
                    logger.w("训练上下文向量化失败: ${result.error}")
                    null
                }
                is ModernResult.Loading -> {
                    logger.d("训练上下文向量化中...")
                    null
                }
            }
        } catch (e: Exception) {
            logger.e(e, "训练上下文向量化异常")
            null
        }
    }

    /**
     * 检索相关内容
     * 实现增强1.md中的IntelligentVectorRetriever功能集成
     */
    private suspend fun retrieveRelevantContent(
        userQuery: String,
        vectorizedState: VectorizedWorkoutState?,
    ): RelevantContent {
        return try {
            val content = hybridSearchRepository.retrieveRelevantContent(userQuery, vectorizedState)
            logger.d(
                "相关内容检索完成: 查询相关${content.queryRelevantExercises.size}个，状态相关${content.stateRelevantExercises.size}个",
            )
            content
        } catch (e: Exception) {
            logger.e(e, "相关内容检索失败")
            RelevantContent.Empty
        }
    }

    /**
     * 确保BGE引擎已初始化 - 双重保障机制
     *
     * 🎯 设计说明：
     * 虽然应用启动时有BGE预热，但为了确保在低端机或特殊情况下BGE引擎可用，
     * 在首次发送AI消息时进行二次检查和懒加载初始化。
     *
     * 这种双重保障机制确保：
     * 1. 正常情况下使用启动时预热的BGE引擎（快速响应）
     * 2. 异常情况下自动触发懒加载初始化（保证功能可用）
     */
    private suspend fun ensureBgeEngineReady() {
        try {
            // 快速检查BGE引擎当前状态
            val currentStatus = initializeBgeEngineUseCase.getCurrentStatus()

            when (currentStatus) {
                EngineStatus.READY -> {
                    // 引擎已就绪，直接使用
                    logger.d("BGE引擎已就绪，可直接使用")
                    return
                }
                EngineStatus.UNINITIALIZED, EngineStatus.ERROR -> {
                    // 需要初始化或重新初始化
                    logger.d("BGE引擎未初始化，开始懒加载...")

                    // 触发初始化，但不等待完成（避免阻塞）
                    // 这里使用Fire-and-Forget模式，让BGE在后台初始化
                    // 如果初始化失败，向量化功能会降级，但不影响基础AI对话
                    initializeBgeEngineUseCase.invoke().collect { status ->
                        when (status) {
                            EngineStatus.READY -> {
                                logger.d("BGE引擎懒加载成功")
                                return@collect
                            }
                            EngineStatus.ERROR -> {
                                logger.w("BGE引擎懒加载失败，将使用降级模式")
                                return@collect
                            }
                            else -> {
                                logger.d("BGE引擎初始化中: $status")
                            }
                        }
                    }
                }
                EngineStatus.INITIALIZING -> {
                    // 引擎正在初始化中，等待一下
                    logger.d("BGE引擎正在初始化中，等待完成...")
                    // 这里可以选择等待或直接继续，我们选择直接继续以避免阻塞
                }
            }
        } catch (e: Exception) {
            // BGE初始化检查失败，记录警告但不影响主要功能
            logger.w(e, "BGE引擎状态检查失败，将降级使用基础AI功能")
        }
    }

    /**
     * 生成统一的消息ID
     * P0-1修复：确保Stream ID一致性
     *
     * @return 唯一的消息ID
     */
    private fun generateMessageId(): String = Constants.MessageId.generate()
}

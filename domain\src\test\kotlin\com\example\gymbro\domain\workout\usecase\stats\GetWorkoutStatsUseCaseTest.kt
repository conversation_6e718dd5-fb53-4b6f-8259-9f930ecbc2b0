package com.example.gymbro.domain.workout.usecase.stats

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.error.types.data.DataErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.auth.usecase.GetCurrentUserIdUseCase
import com.example.gymbro.domain.shared.common.model.TimeRangeSpan
import com.example.gymbro.domain.workout.model.stats.UserWorkoutStatistics
import com.example.gymbro.domain.workout.repository.SessionRepository
import io.mockk.MockKAnnotations
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockk
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.LocalDate
import org.junit.Before
import org.junit.Test
import kotlin.test.assertTrue

/**
 * GetWorkoutStatsUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试UseCase的核心业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证ModernResult返回类型
 * ✔️ 测试用户认证和参数验证
 */
class GetWorkoutStatsUseCaseTest {

    @MockK
    private lateinit var sessionRepository: SessionRepository

    @MockK
    private lateinit var getCurrentUserIdUseCase: GetCurrentUserIdUseCase

    @MockK
    private lateinit var logger: Logger

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: GetWorkoutStatsUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = GetWorkoutStatsUseCase(
            sessionRepository = sessionRepository,
            getCurrentUserIdUseCase = getCurrentUserIdUseCase,
            dispatcher = testDispatcher,
            logger = logger,
        )
    }

    @Test
    fun `execute should return error when getCurrentUserId returns null`() = runTest(testDispatcher) {
        // Given
        val timeRange = createSampleTimeRange()
        every { getCurrentUserIdUseCase() } returns flowOf()

        // When
        val result = useCase.execute(timeRange)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `execute should return error when getCurrentUserId returns Success with null`() = runTest(
        testDispatcher,
    ) {
        // Given
        val timeRange = createSampleTimeRange()
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(null))

        // When
        val result = useCase.execute(timeRange)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is DataErrors.DataError)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `execute should return error when getCurrentUserId returns Success with blank string`() = runTest(
        testDispatcher,
    ) {
        // Given
        val timeRange = createSampleTimeRange()
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(""))

        // When
        val result = useCase.execute(timeRange)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is DataErrors.DataError)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `execute should return error when getCurrentUserId returns Success with blank whitespace`() = runTest(
        testDispatcher,
    ) {
        // Given
        val timeRange = createSampleTimeRange()
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success("   "))

        // When
        val result = useCase.execute(timeRange)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is DataErrors.DataError)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `execute should return not implemented error when userId is valid`() = runTest(testDispatcher) {
        // Given
        val timeRange = createSampleTimeRange()
        val userId = "user-123"
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))

        // When
        val result = useCase.execute(timeRange)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is DataErrors.DataError)
        val errorMessage = (result.error as DataErrors.DataError).uiMessage?.toString()
        assertTrue(errorMessage?.contains("功能暂未实现") == true)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `execute should propagate error when getCurrentUserId fails`() = runTest(testDispatcher) {
        // Given
        val timeRange = createSampleTimeRange()
        val authError = mockk<BusinessErrors.BusinessError>()
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Error(authError))

        // When
        val result = useCase.execute(timeRange)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error === authError)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `execute should return loading when getCurrentUserId returns Loading`() = runTest(testDispatcher) {
        // Given
        val timeRange = createSampleTimeRange()
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Loading)

        // When
        val result = useCase.execute(timeRange)

        // Then
        assertTrue(result is ModernResult.Loading)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `execute should handle different TimeRangeSpan types correctly`() = runTest(testDispatcher) {
        // Given
        val timeRanges = listOf(
            createTimeRangeSpan("LAST_7_DAYS"),
            createTimeRangeSpan("LAST_30_DAYS"),
            createTimeRangeSpan("LAST_90_DAYS"),
            createTimeRangeSpan("CUSTOM"),
        )

        // When & Then
        timeRanges.forEach { timeRange ->
            every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success("user-123"))

            val result = useCase.execute(timeRange)

            // 所有情况都应该返回"功能暂未实现"错误
            assertTrue(result is ModernResult.Error)
            assertTrue((result as ModernResult.Error).error is DataErrors.DataError)
            val errorMessage = (result.error as DataErrors.DataError).uiMessage?.toString()
            assertTrue(errorMessage?.contains("功能暂未实现") == true)
        }
    }

    @Test
    fun `execute should validate userId type correctly`() = runTest(testDispatcher) {
        // Given
        val timeRange = createSampleTimeRange()

        // Test with non-String data
        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(123)) // Integer instead of String

        // When
        val result = useCase.execute(timeRange)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is DataErrors.DataError)
        coVerify(exactly = 0) { sessionRepository.getUserSessions(any()) }
    }

    @Test
    fun `execute should handle edge case with valid userId`() = runTest(testDispatcher) {
        // Given
        val timeRange = createSampleTimeRange()
        val validUserIds = listOf(
            "user-123",
            "<EMAIL>",
            "user_with_underscores",
            "user.with.dots",
            "123456789",
        )

        // When & Then
        validUserIds.forEach { userId ->
            every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))

            val result = useCase.execute(timeRange)

            // 所有有效用户ID都应该返回"功能暂未实现"错误，而不是参数验证错误
            assertTrue(result is ModernResult.Error)
            assertTrue((result as ModernResult.Error).error is DataErrors.DataError)
            val errorMessage = (result.error as DataErrors.DataError).uiMessage?.toString()
            assertTrue(errorMessage?.contains("功能暂未实现") == true)
            assertTrue(!errorMessage!!.contains("用户ID为空"))
        }
    }

    @Test
    fun `execute should handle multiple consecutive calls correctly`() = runTest(testDispatcher) {
        // Given
        val timeRange1 = createTimeRangeSpan("LAST_7_DAYS")
        val timeRange2 = createTimeRangeSpan("LAST_30_DAYS")
        val userId = "user-123"

        every { getCurrentUserIdUseCase() } returns flowOf(ModernResult.Success(userId))

        // When
        val result1 = useCase.execute(timeRange1)
        val result2 = useCase.execute(timeRange2)

        // Then
        assertTrue(result1 is ModernResult.Error)
        assertTrue(result2 is ModernResult.Error)
        assertTrue((result1 as ModernResult.Error).error is DataErrors.DataError)
        assertTrue((result2 as ModernResult.Error).error is DataErrors.DataError)

        // 两次调用都应该返回相同的"功能暂未实现"错误
        val errorMessage1 = (result1.error as DataErrors.DataError).uiMessage?.toString()
        val errorMessage2 = (result2.error as DataErrors.DataError).uiMessage?.toString()
        assertTrue(errorMessage1?.contains("功能暂未实现") == true)
        assertTrue(errorMessage2?.contains("功能暂未实现") == true)
    }

    // === 辅助方法 ===

    private fun createSampleTimeRange(): TimeRangeSpan {
        return createTimeRangeSpan("LAST_7_DAYS")
    }

    private fun createTimeRangeSpan(type: String): TimeRangeSpan {
        return when (type) {
            "LAST_7_DAYS" -> TimeRangeSpan.LastDays(7)
            "LAST_30_DAYS" -> TimeRangeSpan.LastDays(30)
            "LAST_90_DAYS" -> TimeRangeSpan.LastDays(90)
            "CUSTOM" -> TimeRangeSpan.Custom(
                startDate = LocalDate(2024, 1, 1),
                endDate = LocalDate(2024, 1, 31),
            )
            else -> TimeRangeSpan.LastDays(7)
        }
    }

    private fun createSampleUserWorkoutStatistics(): UserWorkoutStatistics {
        return UserWorkoutStatistics(
            userId = "user-123",
            totalWorkouts = 10,
            totalExercises = 50,
            totalSets = 200,
            totalReps = 2000,
            totalWeight = 10000.0,
            avgRpe = 7.5f,
            totalDurationSec = 3600,
            timeRange = createSampleTimeRange(),
        )
    }
}
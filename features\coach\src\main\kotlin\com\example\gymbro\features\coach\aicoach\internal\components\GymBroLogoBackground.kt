package com.example.gymbro.features.coach.aicoach.internal.components

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.CoachMotionValues

// === 背景Logo常量定义 ===
private object LogoBackgroundConstants {
    const val DefaultFontSize = 240f // 背景Logo字体大小
}

/**
 * GymBro背景G字母组件
 *
 * 实现UI布局总方案中要求的"背景G字母"效果
 * 提供低调的视觉背景，不干扰主要内容的显示
 *
 * 设计特点：
 * - 大号G字母作为视觉背景
 * - 极低透明度，不干扰前景内容
 * - 居中显示，与品牌风格一致
 * - 响应主题变化
 *
 * @param modifier 修饰符
 * @param alpha 背景透明度，默认使用token值
 * @param fontSize 字体大小，默认240sp
 */
@Composable
internal fun GymBroLogoBackground(
    modifier: Modifier = Modifier,
    alpha: Float = CoachMotionValues.Alpha.PANEL_BACKGROUND, // 使用设计系统token
    fontSize: Float = LogoBackgroundConstants.DefaultFontSize,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = "G",
            fontSize = fontSize.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .alpha(alpha)
                .offset(y = (-20).dp), // 稍微向上偏移，优化视觉平衡
        )
    }
}

/**
 * 带动画效果的GymBroLogoBackground
 *
 * 支持透明度动画变化，可以在页面加载时产生淡入效果
 *
 * @param modifier 修饰符
 * @param isVisible 是否可见，控制动画状态
 * @param fontSize 字体大小
 */
@Composable
internal fun AnimatedGymBroLogoBackground(
    modifier: Modifier = Modifier,
    isVisible: Boolean = true,
    fontSize: Float = LogoBackgroundConstants.DefaultFontSize,
) {
    val animatedAlpha by animateFloatAsState(
        targetValue = if (isVisible) CoachMotionValues.Alpha.PANEL_BACKGROUND else 0f,
        animationSpec = androidx.compose.animation.core.tween(
            durationMillis = 1000, // 较慢的淡入效果
            easing = androidx.compose.animation.core.FastOutSlowInEasing,
        ),
        label = "logo_background_alpha",
    )

    GymBroLogoBackground(
        modifier = modifier,
        alpha = animatedAlpha,
        fontSize = fontSize,
    )
}

/**
 * 多层次G字母背景
 *
 * 创建更丰富的背景层次效果，支持多个G字母的组合
 *
 * @param modifier 修饰符
 * @param layerCount 层次数量，默认3层
 */
@Composable
internal fun LayeredGymBroLogoBackground(
    modifier: Modifier = Modifier,
    layerCount: Int = 3,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        repeat(layerCount) { index ->
            val scale = 1f - (index * 0.15f) // 每层缩小15%
            val alpha = CoachMotionValues.Alpha.PANEL_BACKGROUND * (1f - index * 0.3f) // 每层透明度递减

            Text(
                text = "G",
                fontSize = (LogoBackgroundConstants.DefaultFontSize * scale).sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .alpha(alpha.coerceAtLeast(0.05f)) // 确保最小透明度
                    .offset(
                        x = (index * 4).dp, // 每层稍微偏移
                        y = (-20 + index * 2).dp,
                    ),
            )
        }
    }
}

/**
 * 可自定义的GymBro背景组件
 *
 * 支持自定义字符、颜色和样式
 *
 * @param modifier 修饰符
 * @param character 显示的字符，默认"G"
 * @param color 文字颜色，默认使用主题色
 * @param alpha 透明度
 * @param fontSize 字体大小
 * @param fontWeight 字体粗细
 */
@Composable
internal fun CustomGymBroBackground(
    modifier: Modifier = Modifier,
    character: String = "G",
    color: Color = MaterialTheme.colorScheme.onSurface,
    alpha: Float = CoachMotionValues.Alpha.PANEL_BACKGROUND,
    fontSize: Float = LogoBackgroundConstants.DefaultFontSize,
    fontWeight: FontWeight = FontWeight.Bold,
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {
        Text(
            text = character,
            fontSize = fontSize.sp,
            fontWeight = fontWeight,
            color = color,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .alpha(alpha)
                .offset(y = (-20).dp),
        )
    }
}

// === 预览组件 ===

@GymBroPreview
@Composable
private fun GymBroLogoBackgroundPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.size(300.dp),
            color = MaterialTheme.colorScheme.background,
        ) {
            GymBroLogoBackground()
        }
    }
}

@GymBroPreview
@Composable
private fun AnimatedGymBroLogoBackgroundPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.size(300.dp),
            color = MaterialTheme.colorScheme.background,
        ) {
            AnimatedGymBroLogoBackground(
                isVisible = true,
            )
        }
    }
}

@GymBroPreview
@Composable
private fun LayeredGymBroLogoBackgroundPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.size(300.dp),
            color = MaterialTheme.colorScheme.background,
        ) {
            LayeredGymBroLogoBackground()
        }
    }
}

@GymBroPreview
@Composable
private fun CustomGymBroBackgroundPreview() {
    GymBroTheme {
        Surface(
            modifier = Modifier.size(300.dp),
            color = MaterialTheme.colorScheme.background,
        ) {
            CustomGymBroBackground(
                character = "💪",
                fontSize = 180f,
                alpha = 0.3f,
            )
        }
    }
}

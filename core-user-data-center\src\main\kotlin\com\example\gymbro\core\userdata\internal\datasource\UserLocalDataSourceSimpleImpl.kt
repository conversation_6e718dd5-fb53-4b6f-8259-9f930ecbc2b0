package com.example.gymbro.core.userdata.internal.datasource

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.core.ui.text.UiText
import com.example.gymbro.core.userdata.api.model.SyncStatus
import com.example.gymbro.core.userdata.api.model.UnifiedUserData
import com.example.gymbro.core.userdata.internal.mapper.UserDataMapper
import com.example.gymbro.domain.model.auth.AuthUser
import com.example.gymbro.domain.profile.model.user.UserProfile
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 用户本地数据源简化实现
 *
 * 基于内存缓存的轻量级实现，主要用于：
 * - 单元测试场景
 * - 开发调试环境
 * - 不需要持久化的临时数据存储
 *
 * 生产环境使用 UserLocalDataSourceRoomImpl (通过 @RoomUserDataSource 注入)
 * 测试环境使用此实现 (通过 @SimpleUserDataSource 注入)
 */
@Singleton
class UserLocalDataSourceSimpleImpl @Inject constructor(
    private val userDataMapper: UserDataMapper,
    private val logger: Logger,
) : UserLocalDataSource {

    companion object {
        private const val TAG = "UserLocalDataSourceSimpleImpl"
    }

    // 内存缓存存储
    private val _userData = MutableStateFlow<UnifiedUserData?>(null)
    private val operationMutex = Mutex()

    override fun observeUserData(): Flow<UnifiedUserData?> {
        logger.d(TAG, "开始观察统一用户数据（内存缓存版本）")
        return _userData.asStateFlow()
    }

    override fun observeUserData(userId: String): Flow<UnifiedUserData?> {
        logger.d(TAG, "开始观察指定用户数据: userId=$userId（内存缓存版本）")
        return _userData.asStateFlow()
    }

    override suspend fun getCurrentUserData(): ModernResult<UnifiedUserData?> {
        return try {
            logger.d(TAG, "获取当前用户数据（内存缓存版本）")
            ModernResult.Success(_userData.value)
        } catch (e: Exception) {
            logger.e(e, TAG, "获取当前用户数据时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "getCurrentUserData",
                    message = UiText.DynamicString("获取用户数据失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    override suspend fun getUserData(userId: String): ModernResult<UnifiedUserData?> {
        return try {
            logger.d(TAG, "获取指定用户数据: userId=$userId（内存缓存版本）")
            val currentData = _userData.value
            if (currentData?.userId == userId) {
                ModernResult.Success(currentData)
            } else {
                ModernResult.Success(null)
            }
        } catch (e: Exception) {
            logger.e(e, TAG, "获取指定用户数据时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "getUserData",
                    message = UiText.DynamicString("获取用户数据失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    override suspend fun saveAuthData(authUser: AuthUser): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "保存认证数据: userId=${authUser.uid}（内存缓存版本）")

                val currentData = _userData.value
                val updatedData = if (currentData != null && currentData.userId == authUser.uid) {
                    userDataMapper.updateAuthData(currentData, authUser)
                } else {
                    userDataMapper.combineAuthAndProfile(
                        authUser = authUser,
                        userProfile = null,
                        syncStatus = SyncStatus.SYNCED,
                    )
                }

                _userData.value = updatedData
                logger.d(TAG, "认证数据保存成功")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "保存认证数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "saveAuthData",
                        message = UiText.DynamicString("保存认证数据失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun saveProfileData(userProfile: UserProfile): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "保存用户资料数据: userId=${userProfile.userId}（内存缓存版本）")

                val currentData = _userData.value
                val updatedData = if (currentData != null && currentData.userId == userProfile.userId) {
                    userDataMapper.updateProfileData(currentData, userProfile)
                } else {
                    userDataMapper.combineAuthAndProfile(
                        authUser = userDataMapper.toAuthUser(
                            UnifiedUserData(
                                userId = userProfile.userId,
                                displayName = userProfile.displayName,
                            ),
                        ),
                        userProfile = userProfile,
                        syncStatus = SyncStatus.SYNCED,
                    )
                }

                _userData.value = updatedData
                logger.d(TAG, "用户资料数据保存成功")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "保存用户资料数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "saveProfileData",
                        message = UiText.DynamicString("保存用户资料失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun updateAuthData(authUser: AuthUser): ModernResult<Unit> {
        return saveAuthData(authUser) // 简化实现，直接调用保存方法
    }

    override suspend fun updateProfileData(userProfile: UserProfile): ModernResult<Unit> {
        return saveProfileData(userProfile) // 简化实现，直接调用保存方法
    }

    override suspend fun userExists(userId: String): ModernResult<Boolean> {
        return try {
            val exists = _userData.value?.userId == userId
            logger.d(TAG, "检查用户是否存在: userId=$userId, exists=$exists（内存缓存版本）")
            ModernResult.Success(exists)
        } catch (e: Exception) {
            logger.e(e, TAG, "检查用户存在性时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "userExists",
                    message = UiText.DynamicString("检查用户存在性失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    override suspend fun clearAllUserData(): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "清除所有用户数据（内存缓存版本）")
                _userData.value = null
                logger.d(TAG, "所有用户数据清除成功")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "清除所有用户数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "clearAllUserData",
                        message = UiText.DynamicString("清除用户数据失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun clearUserData(userId: String): ModernResult<Unit> {
        return operationMutex.withLock {
            try {
                logger.d(TAG, "清除指定用户数据: userId=$userId（内存缓存版本）")
                if (_userData.value?.userId == userId) {
                    _userData.value = null
                }
                logger.d(TAG, "指定用户数据清除成功")
                ModernResult.Success(Unit)
            } catch (e: Exception) {
                logger.e(e, TAG, "清除指定用户数据时发生异常")
                ModernResult.Error(
                    BusinessErrors.BusinessError.rule(
                        operationName = "clearUserData",
                        message = UiText.DynamicString("清除用户数据失败: ${e.message}"),
                        recoverable = true,
                    ),
                )
            }
        }
    }

    override suspend fun getAllUserIds(): ModernResult<List<String>> {
        return try {
            val userIds = _userData.value?.let { listOf(it.userId) } ?: emptyList()
            logger.d(TAG, "获取用户ID列表成功: count=${userIds.size}（内存缓存版本）")
            ModernResult.Success(userIds)
        } catch (e: Exception) {
            logger.e(e, TAG, "获取用户ID列表时发生异常")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "getAllUserIds",
                    message = UiText.DynamicString("获取用户列表失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }

    override suspend fun healthCheck(): ModernResult<Boolean> {
        return try {
            logger.d(TAG, "执行数据库健康检查（内存缓存版本）")
            ModernResult.Success(true)
        } catch (e: Exception) {
            logger.e(e, TAG, "数据库健康检查失败")
            ModernResult.Error(
                BusinessErrors.BusinessError.rule(
                    operationName = "healthCheck",
                    message = UiText.DynamicString("数据库健康检查失败: ${e.message}"),
                    recoverable = true,
                ),
            )
        }
    }
}

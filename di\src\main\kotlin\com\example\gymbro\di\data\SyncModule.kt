package com.example.gymbro.di.data

import android.content.Context
import androidx.hilt.work.HiltWorkerFactory
import androidx.work.Configuration
import androidx.work.WorkManager
import androidx.work.WorkerFactory
import javax.inject.Singleton
// Removed imports for deleted sync classes after data layer refactoring
// TODO: 在重制workout模块后重新添加workout相关同步功能
// Removed: SyncableUserRepositoryImpl, NetworkStateMonitor, CoachWorkoutSyncCoordinator, DataTypeSyncCoordinator, SyncStatusTracker
import com.example.gymbro.data.shared.sync.SyncCoordinatorImpl
// TODO: SyncScheduler 不存在，如需要请创建类
// import com.example.gymbro.data.sync.SyncScheduler

import com.example.gymbro.domain.shared.sync.SyncCoordinator
// 暂时移除，等实现同步功能时再启用
// import com.example.gymbro.data.repository.base.SyncableUserRepository
// import com.example.gymbro.data.repository.user.UserRepositoryImpl
import dagger.Binds
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent

/**
 * 同步模块
 * 提供同步相关的依赖注入，包括WorkManager、SyncManager和新的同步组件
 */
@Module
@InstallIn(SingletonComponent::class)
object SyncProviderModule {
    /**
     * 提供WorkManager实例
     * 手动初始化WorkManager避免循环依赖
     */
    @Provides
    @Singleton
    fun provideWorkManager(
        @ApplicationContext context: Context,
        workerFactory: HiltWorkerFactory,
    ): WorkManager {
        // 手动初始化WorkManager配置，避免依赖Application.Configuration.Provider
        val config =
            Configuration
                .Builder()
                .setWorkerFactory(workerFactory)
                .build()

        // 手动初始化WorkManager
        WorkManager.initialize(context, config)
        return WorkManager.getInstance(context)
    }

    /**
     * 提供WorkerFactory实例
     * 用于支持Hilt工作器的依赖注入
     */
    @Provides
    @Singleton
    fun provideWorkerFactory(workerFactory: HiltWorkerFactory): WorkerFactory = workerFactory

    /**
     * 🔥 706任务保存：提供HistorySyncScheduler实例
     * 负责历史数据同步的WorkManager任务调度管理
     */
    @Provides
    @Singleton
    fun provideHistorySyncScheduler(
        workManager: WorkManager,
    ): com.example.gymbro.data.coach.sync.HistorySyncScheduler {
        return com.example.gymbro.data.coach.sync.HistorySyncScheduler(workManager)
    }

    /**
     * 提供SyncStatusTracker实例
     * 负责同步状态跟踪和管理
     * TODO: SyncStatusTracker 不存在，如需要请创建类
     */
    // @Provides
    // @Singleton
    // internal fun provideSyncStatusTracker(): SyncStatusTracker {
    //     return SyncStatusTracker()
    // }

    /**
     * 提供DataTypeSyncCoordinator实例
     * 负责数据类型同步协调
     * TODO: 依赖SyncScheduler，暂时注释
     */
    // @Provides
    // @Singleton
    // internal fun provideDataTypeSyncCoordinator(
    //     syncScheduler: SyncScheduler,
    //     syncStatusTracker: SyncStatusTracker,
    //     authDataSource: AuthDataSource
    // ): DataTypeSyncCoordinator {
    //     return DataTypeSyncCoordinator(syncScheduler, syncStatusTracker, authDataSource)
    // }

    /**
     * 提供SyncManager实例 (门面模式)
     * 整合所有同步组件，保持向后兼容
     * TODO: 依赖SyncScheduler和DataTypeSyncCoordinator，暂时注释
     */
    // @Provides
    // @Singleton
    // internal fun provideSyncManager(
    //     @ApplicationContext context: Context,
    //     syncScheduler: SyncScheduler,
    //     syncStatusTracker: SyncStatusTracker,
    //     dataTypeSyncCoordinator: DataTypeSyncCoordinator,
    //     authDataSource: AuthDataSource
    // ): SyncManager {
    //     return SyncManager(
    //         context,
    //         syncScheduler,
    //         syncStatusTracker,
    //         dataTypeSyncCoordinator,
    //         authDataSource
    //     )
    // }

    /**
     * 提供NetworkStateMonitor实例
     * 负责网络状态监听和管理
     * TODO: NetworkStateMonitor 不存在，如需要请创建类
     */
    // @Provides
    // @Singleton
    // fun provideNetworkStateMonitor(@ApplicationContext context: Context): NetworkStateMonitor {
    //     return NetworkStateMonitor(context)
    // }

    // TODO: 在重制workout模块后重新添加CoachWorkoutSyncCoordinator
}

/**
 * 提供同步相关依赖的Hilt模块
 */
@Module
@InstallIn(SingletonComponent::class)
abstract class SyncBindingModule {
    /**
     * 将SyncCoordinatorImpl绑定到SyncCoordinator接口
     */
    @Binds
    @Singleton
    internal abstract fun bindSyncCoordinator(impl: SyncCoordinatorImpl): SyncCoordinator

    // TODO: 在重制workout模块后重新添加SyncableExerciseRepository绑定
}

/**
 * 统一同步模块，整合SyncProviderModule和SyncBindingModule
 */
@Module(
    includes = [
        SyncProviderModule::class,
        SyncBindingModule::class,
    ],
)
@InstallIn(SingletonComponent::class)
object SyncModule

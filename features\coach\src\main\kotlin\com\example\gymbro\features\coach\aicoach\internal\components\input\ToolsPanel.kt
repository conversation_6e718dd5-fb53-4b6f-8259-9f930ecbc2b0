package com.example.gymbro.features.coach.aicoach.internal.components.input

import androidx.compose.animation.*
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.motion.MotionDurations
import com.example.gymbro.designSystem.theme.tokens.Tokens

// 示例工具列表，用于预览
private val sampleTools = listOf(
    GymBroTool(
        id = "training_plan",
        icon = Icons.Default.FitnessCenter,
        title = "制定训练计划",
        subtitle = "个性化方案",
        functionCallName = "create_training_plan",
    ),
    GymBroTool(
        id = "nutrition_advice",
        icon = Icons.Default.Restaurant,
        title = "营养建议",
        subtitle = "健康饮食",
        functionCallName = "nutrition_guidance",
    ),
    GymBroTool(
        id = "exercise_guide",
        icon = Icons.Default.School,
        title = "动作指导",
        subtitle = "正确姿势",
        functionCallName = "exercise_instruction",
    ),
    GymBroTool(
        id = "recovery_tips",
        icon = Icons.Default.Healing,
        title = "恢复建议",
        subtitle = "训练后护理",
        functionCallName = "recovery_advice",
    ),
)

/**
 * 🎨 ChatGPT底部工具面板 - 精确还原版
 *
 * 🎯 设计哲学：
 * - 完全模仿ChatGPT的底部弹出工具列表
 * - 垂直列表布局，从底部slide up覆盖输入区域
 * - 图标+标题的Row布局，支持subtitle显示
 * - 标准滑动动画，FastOutSlowInEasing缓动
 * - 黑色半透明遮罩，点击外部关闭
 *
 * 🔧 实现要点：
 * - LazyColumn垂直列表，不是网格
 * - 每个工具项16dp水平+12dp垂直padding
 * - 支持ripple点击效果
 * - 圆角顶部bottom sheet样式
 * - Token化设计规格
 *
 * 🚨 性能策略：
 * - @Stable注解确保重组优化
 * - remember缓存交互源
 * - 固定工具列表避免重组
 * - 简化动画避免掉帧
 */

@Stable
internal data class GymBroTool(
    val id: String,
    val icon: ImageVector,
    val title: String,
    val subtitle: String? = null, // 支持subtitle如"15分钟"
    val functionCallName: String,
)

// 🔥 【预设数据清理】移除硬编码工具列表
// 工具列表现在应该从ViewModel/UseCase动态获取，支持配置化管理

/**
 * 🎨 ChatGPT风格底部工具面板主组件
 *
 * 🔥 【预设数据清理】工具列表现在通过参数传入，支持动态配置
 */
@Composable
internal fun ChatGPTStyleToolsPanel(
    isVisible: Boolean,
    tools: List<GymBroTool>,
    onToolClick: (String) -> Unit,
    modifier: Modifier = Modifier,
    animationDuration: Int = MotionDurations.Short, // 标准动画时长
) {
    // 🎯 精确还原动画 - slide up from bottom
    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { it }, // 从底部滑入
            animationSpec = tween(
                durationMillis = animationDuration,
                easing = FastOutSlowInEasing,
            ),
        ) + fadeIn(tween(animationDuration)),
        exit = slideOutVertically(
            targetOffsetY = { it }, // 向底部滑出
            animationSpec = tween(
                durationMillis = animationDuration - 50,
                easing = FastOutSlowInEasing,
            ),
        ) + fadeOut(tween(animationDuration - 50)),
        modifier = modifier,
    ) {
        ChatGPTBottomSheet(
            tools = tools,
            onToolClick = onToolClick,
        )
    }
}

/**
 * 🎨 ChatGPT底部工具表单 - 核心实现
 */
@Composable
private fun ChatGPTBottomSheet(
    tools: List<GymBroTool>,
    onToolClick: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    Surface(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(), // 自适应内容高度
        shape = RoundedCornerShape(
            topStart = Tokens.Radius.XLarge, // 24dp顶部圆角
            topEnd = Tokens.Radius.XLarge,
            bottomStart = 0.dp, // 底部无圆角
            bottomEnd = 0.dp,
        ),
        color = MaterialTheme.colorScheme.surface,
        shadowElevation = Tokens.Elevation.Card, // 8dp阴影 - 使用正确的Elevation Token
        tonalElevation = 0.dp,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(
                    top = Tokens.Spacing.Medium, // 16dp顶部间距
                    bottom = Tokens.Spacing.Large, // 24dp底部间距（安全区域）
                ),
        ) {
            // 🎯 面板标题 - JSON规范：large + bold + 16dp padding
            Text(
                text = "工具", // TODO: 移至strings.xml
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold,
                ),
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(
                    horizontal = Tokens.Spacing.Medium, // 16dp水平padding
                    vertical = Tokens.Spacing.Small, // 8dp垂直间距
                ),
            )

            // 🎯 工具列表 - LazyColumn垂直布局
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = Tokens.Card.HeightLarge), // 🔥 减少最大高度，适配4个工具
                verticalArrangement = Arrangement.spacedBy(0.dp), // 无额外间距
            ) {
                items(tools) { tool ->
                    ChatGPTToolItem(
                        tool = tool,
                        onClick = { onToolClick(tool.functionCallName) },
                    )
                }
            }
        }
    }
}

/**
 * 🎨 ChatGPT工具项 - 精确还原Row布局
 */
@Composable
private fun ChatGPTToolItem(
    tool: GymBroTool,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null, // 使用默认系统Ripple替代已废弃的rememberRipple
                onClick = onClick,
            )
            .padding(
                horizontal = Tokens.Spacing.Medium, // 16dp水平padding
                vertical = 12.dp, // 12dp垂直padding (JSON规范)
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(12.dp), // 图标与文字间距
    ) {
        // 🎯 左侧图标 - 无背景容器
        Icon(
            imageVector = tool.icon,
            contentDescription = tool.title,
            tint = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.size(24.dp), // 标准图标尺寸
        )

        // 🎯 右侧文字区域
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(2.dp), // 标题与副标题间距
        ) {
            // 主标题
            Text(
                text = tool.title,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium,
                ),
                color = MaterialTheme.colorScheme.onSurface,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )

            // 副标题（如果有）- JSON规范：gray颜色
            tool.subtitle?.let { subtitle ->
                Text(
                    text = subtitle,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )
            }
        }
    }
}

// === 向后兼容的别名 ===
/**
 * 向后兼容别名 - 逐步迁移现有代码
 */
@Composable
internal fun ToolsPanel(
    isVisible: Boolean,
    tools: List<GymBroTool>,
    onToolClick: (String) -> Unit,
    modifier: Modifier = Modifier,
    themeState: com.example.gymbro.designSystem.components.extras.ThemeState? = null,
) {
    ChatGPTStyleToolsPanel(
        isVisible = isVisible,
        tools = tools, // 使用传入的工具列表
        onToolClick = onToolClick,
        modifier = modifier,
        animationDuration = MotionDurations.Short,
    )
}

// === 预览组件 ===

@GymBroPreview
@Composable
private fun ChatGPTStyleToolsPanelPreview() {
    GymBroTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background),
            contentAlignment = Alignment.BottomCenter,
        ) {
            ChatGPTStyleToolsPanel(
                isVisible = true,
                tools = sampleTools, // 使用示例工具列表
                onToolClick = { /* Preview */ },
            )
        }
    }
}

@GymBroPreview
@Composable
private fun ChatGPTBottomSheetPreview() {
    GymBroTheme {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background.copy(alpha = 0.5f)),
            contentAlignment = Alignment.BottomCenter,
        ) {
            ChatGPTBottomSheet(
                tools = sampleTools,
                onToolClick = { /* Preview */ },
            )
        }
    }
}

@GymBroPreview
@Composable
private fun ChatGPTToolItemPreview() {
    GymBroTheme {
        Surface(
            color = MaterialTheme.colorScheme.surface,
        ) {
            Column {
                ChatGPTToolItem(
                    tool = GymBroTool(
                        id = "test1",
                        icon = Icons.Default.Hub,
                        title = "搜索连接器",
                        functionCallName = "test1",
                    ),
                    onClick = { /* Preview */ },
                )

                ChatGPTToolItem(
                    tool = GymBroTool(
                        id = "test2",
                        icon = Icons.Default.Speed,
                        title = "运行深度分析",
                        subtitle = "3分钟",
                        functionCallName = "test2",
                    ),
                    onClick = { /* Preview */ },
                )
            }
        }
    }
}

@GymBroPreview
@Composable
private fun ChatGPTStyleToolsPanelDarkThemePreview() {
    GymBroTheme(darkTheme = true) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.background),
            contentAlignment = Alignment.BottomCenter,
        ) {
            ChatGPTStyleToolsPanel(
                isVisible = true,
                tools = sampleTools, // 使用示例工具列表
                onToolClick = { /* Preview */ },
            )
        }
    }
}

package com.example.gymbro.data.coach.session.mapper

import com.example.gymbro.data.local.entity.ChatRaw
import com.example.gymbro.data.local.entity.ChatSessionEntity
import com.example.gymbro.domain.coach.model.ChatSession
import com.example.gymbro.domain.coach.model.CoachMessage
import timber.log.Timber

/**
 * ChatSessionMapper - Entity ↔ Domain 转换
 *
 * 基于 promtjson.md 文档要求，提供清晰的数据转换
 * 分离数据层和领域层的关注点
 */
object ChatSessionMapper {

    /**
     * ChatSessionEntity 转 Domain
     */
    fun ChatSessionEntity.toDomain(messages: List<CoachMessage> = emptyList()): ChatSession {
        // 🔥 【扩展函数冲突修复】避免递归调用，直接实现转换逻辑
        return ChatSession(
            id = this.id,
            userId = this.userId,
            title = this.title,
            createdAt = kotlinx.datetime.Instant.fromEpochMilliseconds(this.createdAt),
            lastActiveAt = kotlinx.datetime.Instant.fromEpochMilliseconds(this.lastActiveAt),
            isActive = this.isActive,
            status = when (this.status) {
                0 -> ChatSession.SessionStatus.ACTIVE
                1 -> ChatSession.SessionStatus.ARCHIVED
                2 -> ChatSession.SessionStatus.DELETED
                else -> ChatSession.SessionStatus.ACTIVE
            },
            messageCount = this.messageCount,
            messages = messages,
            summary = this.summary,
            metadata = this.metadata?.let {
                try {
                    kotlinx.serialization.json.Json.decodeFromString<Map<String, String>>(it)
                } catch (e: Exception) {
                    emptyMap()
                }
            } ?: emptyMap(),
        )
    }

    /**
     * Domain 转 ChatSessionEntity
     */
    fun ChatSession.toEntity(): ChatSessionEntity {
        return ChatSessionEntity.fromDomain(this)
    }

    /**
     * ChatRaw 转 Domain CoachMessage
     */
    fun ChatRaw.toDomainMessage(): CoachMessage {
        return when (this.role) {
            "user" -> CoachMessage.UserMessage(
                id = this.messageId, // 使用 messageId 而不是数据库 id
                content = this.content,
                timestamp = this.timestamp,
            )
            "assistant" -> {
                // 🔥 修复：优先使用专用的finalMarkdown字段，fallback到metadata
                val finalMarkdownContent =
                    this.finalMarkdown
                        ?: this.metadata["finalMarkdown"] as? String

                Timber
                    .tag("HISTORY-FIX")
                    .d(
                        "🔄 [修复] ChatRaw转换: messageId=${this.messageId}, finalMarkdown=${finalMarkdownContent?.length ?: 0}字符",
                    )

                CoachMessage.AiMessage(
                    id = this.messageId, // 使用 messageId 而不是数据库 id
                    content = this.content,
                    timestamp = this.timestamp,
                    finalMarkdown = finalMarkdownContent,
                    rawTokens = this.thinkingNodes, // 从thinkingNodes恢复rawTokens
                )
            }
            else -> throw IllegalArgumentException("Unknown role: ${this.role}")
        }
    }

    /**
     * Domain CoachMessage 转 ChatRaw
     */
    fun CoachMessage.toChatRaw(sessionId: String): ChatRaw {
        return when (this) {
            is CoachMessage.UserMessage -> ChatRaw(
                messageId = this.id, // 使用 messageId 字段
                sessionId = sessionId,
                role = "user",
                content = this.content,
                timestamp = this.timestamp,
                metadata = mapOf(
                    "messageType" to "user_message",
                    "userMessageId" to this.id,
                ),
            )
            is CoachMessage.AiMessage -> {
                Timber
                    .tag("HISTORY-FIX")
                    .d(
                        "🔄 [修复] CoachMessage转ChatRaw: messageId=${this.id}, finalMarkdown=${this.finalMarkdown?.length ?: 0}字符",
                    )

                ChatRaw(
                    messageId = this.id, // 使用 messageId 字段
                    sessionId = sessionId,
                    role = "assistant",
                    content = this.content,
                    timestamp = this.timestamp,
                    metadata = buildMap<String, Any> {
                        put("messageType", "ai_response")
                        put("aiMessageId", <EMAIL>)
                        // 🔥 保留metadata备份，但主要使用专用字段
                        <EMAIL>?.let { put("finalMarkdown", it) }
                    },
                    thinkingNodes = this.rawTokens, // 保存rawTokens到thinkingNodes
                    finalMarkdown = this.finalMarkdown, // 🔥 修复：使用专用的finalMarkdown字段
                )
            }
        }
    }

    /**
     * 批量转换 ChatRaw 列表
     */
    fun List<ChatRaw>.toDomainMessages(): List<CoachMessage> {
        return this.map { it.toDomainMessage() }
    }

    /**
     * 批量转换 CoachMessage 列表
     */
    fun List<CoachMessage>.toChatRaws(sessionId: String): List<ChatRaw> {
        return this.map { it.toChatRaw(sessionId) }
    }
}

package com.example.gymbro.core.ai.prompt.registry

import android.content.Context
import com.example.gymbro.core.ai.prompt.config.PromptConfigManager
import com.example.gymbro.core.ai.prompt.manager.PromptMode
import com.example.gymbro.core.ai.prompt.model.PromptSuite
import com.example.gymbro.core.ai.prompt.structure.SystemLayer
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * PromptRegistry - 系统提示词热切换核心
 *
 * 核心功能：
 * 1. 管理系统提示词JSON配置的热切换
 * 2. 本地JSON缓存，离线可用
 * 3. 与PromptConfigManager协作，实现统一配置管理
 *
 * 设计原则：
 * - 系统提示词 → JSON文件存储，支持热切换
 * - 用户层和上下文 → builder内实时更新
 * - 职责分离：静态配置 vs 动态数据
 * - 配置统一：PromptConfigManager控制Registry
 */
@Singleton
class PromptRegistry
@Inject
constructor(
    @ApplicationContext private val context: Context,
    private val promptConfigManager: PromptConfigManager,
) {
    companion object {
        private const val DEFAULT_PROMPT_ID = "standard" // 🔥 修改：使用标准配置作为默认
        private const val PROMPTS_DIR = "prompts"
    }

    // Current state
    private val _currentId = MutableStateFlow(DEFAULT_PROMPT_ID)
    val currentId: StateFlow<String> = _currentId.asStateFlow()

    // Cache for loaded prompt configs
    private val cache = mutableMapOf<String, PromptConfig>()

    // 🔥 【并发控制修复】添加同步锁防止文件系统竞态条件
    private val syncLock = Mutex()

    // JSON处理器
    private val json =
        Json {
            ignoreUnknownKeys = true
            isLenient = true
        }

    init {
        // 初始化时复制assets文件到私有目录
        copyAssetsIfNeeded()

        // 🔥 新功能：从PromptConfigManager同步当前模式
        val currentMode = promptConfigManager.currentMode.value
        val correspondingId = mapModeToId(currentMode)
        _currentId.value = correspondingId

        // 预加载默认配置
        loadAndCache(correspondingId)

        // 🔥 强制调试：重新加载并验证配置
        Timber.i("🔍 PromptRegistry初始化完成，开始验证配置...")
        val config = getCurrentConfig()
        Timber.i("📄 当前配置: ID=${config.id}, DisplayName=${config.displayName}")
        Timber.i("📄 SystemPrompt长度: ${config.systemPrompt.length}字符")
        Timber.i("📄 SystemPrompt前200字符: ${config.systemPrompt.take(200)}")

        // 🔥 强制重新加载一次，清除可能的缓存问题
        Timber.i("🔄 强制重新加载${correspondingId}配置...")
        val reloadedConfig = reloadConfig(correspondingId)
        Timber.i("📄 重新加载后: ID=${reloadedConfig.id}, DisplayName=${reloadedConfig.displayName}")
        Timber.i("📄 重新加载后SystemPrompt长度: ${reloadedConfig.systemPrompt.length}字符")
    }

    /**
     * 🔥 新方法：将PromptMode映射到配置ID
     */
    private fun mapModeToId(mode: String): String =
        when (mode) {
            "standard" -> "standard"
            "layered" -> "layered"
            "pipeline" -> "pipeline"
            "blank" -> "blank"
            else -> "standard"
        }

    /**
     * 切换到指定的prompt配置
     * 🔥 增强：同步更新PromptConfigManager
     */
    fun switch(id: String) {
        try {
            val config = getConfig(id)
            _currentId.value = id

            // 🔥 同步更新PromptConfigManager
            promptConfigManager.switchMode(id)

            Timber.i("🔄 Prompt切换成功: $id (${config.displayName})")
        } catch (e: Exception) {
            Timber.e(e, "❌ Prompt切换失败: $id")
        }
    }

    /**
     * 兼容现有PromptMode枚举的切换方法
     * 🔥 修改：与PromptConfigManager协作
     */
    fun switchMode(mode: PromptMode) {
        val id =
            when (mode) {
                PromptMode.LAYERED -> "layered"
                PromptMode.PIPELINE -> "pipeline"
                PromptMode.STANDARD -> "standard"
                PromptMode.BLANK -> "blank"
            }
        switch(id)
    }

    /**
     * 🔥 新方法：响应PromptConfigManager的模式变化
     */
    fun syncWithConfigManager() {
        val currentConfigMode = promptConfigManager.currentMode.value
        val correspondingId = mapModeToId(currentConfigMode)
        if (_currentId.value != correspondingId) {
            _currentId.value = correspondingId
            Timber.i("🔄 Registry同步ConfigManager模式: $currentConfigMode → $correspondingId")
        }
    }

    /**
     * 获取当前激活的prompt配置
     */
    fun getCurrentConfig(): PromptConfig = getConfig(_currentId.value)

    /**
     * 获取当前系统提示词
     * 🔥 增强：每次获取前都检查文件是否最新
     */
    fun getSystemPrompt(): String {
        // 🔥 新增：每次获取前都检查是否需要更新
        val currentId = _currentId.value

        // 🔥 强制清除缓存并重新检查
        cache.remove(currentId)
        Timber.i("🧹 强制清除${currentId}缓存，重新加载最新版本")

        // 🔥 新增：强制对比assets和本地文件
        debugCompareAssetsAndLocal(currentId)

        checkAndUpdateSingleConfig(currentId)

        return getCurrentConfig().systemPrompt
    }

    /**
     * 获取当前完整的提示词内容 - 用于监控系统
     */
    fun getCurrentPrompt(): String = getSystemPrompt()

    /**
     * 获取当前模式ID
     *
     * @since 618重构 - 兼容LayeredPromptBuilder
     */
    fun getCurrentMode(): String = _currentId.value

    /**
     * 🔥 新增：获取完整的 Prompt 套件
     * 基于 promtjson.md 文档要求实现
     *
     * @return PromptSuite 包含系统层、配置、能力和约束的完整套件
     */
    fun getSuite(): PromptSuite {
        val config = getCurrentConfig()
        val systemLayer = createSystemLayerFromConfig(config)

        return PromptSuite(
            systemLayer = systemLayer,
            config = config,
            capabilities = config.capabilities,
            constraints = config.constraints,
        )
    }

    /**
     * 获取指定ID的prompt配置
     */
    fun getConfig(id: String): PromptConfig = cache[id] ?: loadAndCache(id)

    /**
     * 🔥 新增：强制重新加载指定配置（用于调试）
     */
    fun reloadConfig(id: String): PromptConfig {
        cache.remove(id) // 清除缓存

        // 🔥 新增：强制删除本地文件，重新从assets复制
        val localFile = File(context.filesDir, "$PROMPTS_DIR/$id.json")
        if (localFile.exists()) {
            localFile.delete()
            Timber.i("🗑️ 删除本地文件，强制重新复制: ${localFile.absolutePath}")
        }

        return loadAndCache(id)
    }

    /**
     * 🔥 新增：检查并更新单个配置文件（快速检查）
     * @param configId 配置ID
     * @return 是否有更新
     */
    fun checkAndUpdateSingleConfig(configId: String): Boolean {
        return try {
            val fileName = "$configId.json"
            val targetFile = File(context.filesDir, "$PROMPTS_DIR/$fileName")

            // 如果本地文件不存在，直接复制
            if (!targetFile.exists()) {
                Timber.d("📄 本地文件不存在，复制: $fileName")
                copyAssetFileWithVersionCheck(fileName)
                return true
            }

            // 快速检查：比较文件修改时间和大小
            val assetsContent =
                context.assets.open("prompts/$fileName").use {
                    it.readBytes().toString(Charsets.UTF_8)
                }
            val localContent = targetFile.readText()

            if (assetsContent.hashCode() != localContent.hashCode()) {
                Timber.i("🔄 检测到${configId}配置变更，更新中...")
                copyAssetFileWithVersionCheck(fileName)

                // 清除该配置的缓存
                cache.remove(configId)
                Timber.i("🧹 已清除${configId}的缓存")
                return true
            }

            false
        } catch (e: Exception) {
            Timber.e(e, "❌ 检查单个配置失败: $configId")
            false
        }
    }

    /**
     * 🔥 新增：检查并更新所有过期的配置文件
     * @return 更新报告
     */
    fun checkAndUpdateAllConfigs(): String =
        buildString {
            appendLine("🔍 开始检查所有配置文件版本...")

            val configFiles = listOf("standard", "layered", "pipeline", "blank")
            var updatedCount = 0

            configFiles.forEach { configId ->
                try {
                    val fileName = "$configId.json"
                    val targetFile = File(context.filesDir, "$PROMPTS_DIR/$fileName")

                    if (targetFile.exists()) {
                        val localContent = targetFile.readText()
                        val assetsContent =
                            context.assets.open("prompts/$fileName").use {
                                it.readBytes().toString(Charsets.UTF_8)
                            }

                        val localHash = localContent.hashCode()
                        val assetsHash = assetsContent.hashCode()

                        if (localHash != assetsHash) {
                            appendLine("⚠️ $configId: 版本不一致")
                            appendLine("  本地哈希: $localHash")
                            appendLine("  Assets哈希: $assetsHash")

                            // 执行更新
                            copyAssetFileWithVersionCheck(fileName)
                            updatedCount++
                            appendLine("  ✅ 已更新为最新版本")
                        } else {
                            appendLine("✅ $configId: 版本一致")
                        }
                    } else {
                        appendLine("⚠️ $configId: 本地文件不存在，将创建")
                        copyAssetFileWithVersionCheck(fileName)
                        updatedCount++
                    }
                } catch (e: Exception) {
                    appendLine("❌ $configId: 检查失败 - ${e.message}")
                }
            }

            appendLine("\n📊 检查完成：")
            appendLine("  总文件数: ${configFiles.size}")
            appendLine("  更新文件数: $updatedCount")

            if (updatedCount > 0) {
                // 清除所有缓存，强制重新加载
                cache.clear()
                appendLine("  🧹 已清除所有配置缓存")
            }
        }

    /**
     * 🔥 新增：清除所有缓存，强制下次重新加载
     */
    fun clearAllCaches() {
        cache.clear()
        Timber.i("🧹 已清除所有PromptRegistry缓存")
    }

    /**
     * 🔥 【并发控制修复】强制同步所有assets文件
     *
     * 修复说明：添加同步锁防止并发文件操作导致的AssertionError
     */
    suspend fun forceSyncAllAssets(): String =
        syncLock.withLock {
            return@withLock buildString {
                appendLine("🔄 开始强制同步所有assets文件...")

                try {
                    // 🔥 【关键修复】安全的目录清理逻辑
                    val promptsDir = File(context.filesDir, PROMPTS_DIR)

                    // 检查并处理现有路径
                    if (promptsDir.exists()) {
                        if (promptsDir.isDirectory) {
                            // 如果是目录，安全删除
                            promptsDir.deleteRecursively()
                            appendLine("🗑️ 已清空本地prompts目录")
                        } else {
                            // 如果是文件，先删除文件
                            promptsDir.delete()
                            appendLine("🗑️ 删除了同名文件")
                        }
                    }

                    // 确保目录创建成功
                    if (!promptsDir.mkdirs() && !promptsDir.exists()) {
                        appendLine("❌ 创建prompts目录失败")
                        return@withLock toString()
                    }
                    appendLine("📁 重新创建prompts目录")

                    // 清空缓存
                    cache.clear()
                    appendLine("🧹 清空配置缓存")

                    // 重新复制所有assets文件
                    copyAssetsIfNeeded()
                    appendLine("📦 重新复制assets文件")

                    // 重新加载standard配置
                    val standardConfig = loadAndCache("standard")
                    appendLine("✅ 重新加载standard配置:")
                    appendLine("  ID: ${standardConfig.id}")
                    appendLine("  DisplayName: ${standardConfig.displayName}")
                    appendLine("  Role: ${standardConfig.role}")
                    appendLine("  SystemPrompt长度: ${standardConfig.systemPrompt.length}字符")

                    // 验证是否为fallback
                    val isFallback = standardConfig.displayName == "默认配置"
                    appendLine("  是否为fallback: $isFallback")

                    if (!isFallback) {
                        appendLine("🎉 同步成功，已加载完整的standard.json配置！")
                    } else {
                        appendLine("⚠️ 仍在使用fallback配置，可能需要检查assets文件")
                    }
                } catch (e: Exception) {
                    appendLine("❌ 同步过程出错: ${e.message}")
                    Timber.e(e, "强制同步assets文件失败")
                }
            }
        }

    /**
     * 🔥 新增：获取所有已加载配置的状态
     */
    fun getLoadedConfigsStatus(): String =
        buildString {
            appendLine("📊 PromptRegistry状态报告:")
            appendLine("当前激活: ${_currentId.value}")
            appendLine("已缓存配置: ${cache.keys.joinToString(", ")}")
            cache.forEach { (id, config) ->
                appendLine("  - $id: ${config.displayName} (${config.systemPrompt.length}字符)")
            }
        }

    /**
     * 获取所有可用的prompt配置
     */
    fun getAvailableConfigs(): List<PromptConfig> = cache.values.toList()

    /**
     * 检查当前配置是否启用思考模式
     */
    fun isThinkingEnabled(): Boolean = getCurrentConfig().enableThinking ?: false

    /**
     * 获取思考格式模板
     */
    fun getThinkingFormat(): String? = getCurrentConfig().thinkingFormat

    // ==================== 私有方法 ====================

    private fun loadAndCache(id: String): PromptConfig {
        try {
            // 🔥 新增：每次加载前都检查版本一致性
            Timber.d("🔍 检查配置版本一致性: $id")
            copyAssetFileWithVersionCheck("$id.json")

            val file = File(context.filesDir, "$PROMPTS_DIR/$id.json")

            Timber.d("🔍 尝试加载配置: $id，路径: ${file.absolutePath}")
            Timber.d("🔍 文件存在检查: ${file.exists()}")

            if (!file.exists()) {
                Timber.w("⚠️ 配置文件不存在，尝试从assets复制: $id.json")
                // 尝试从assets复制
                copyAssetFile("$id.json")
            }

            if (!file.exists()) {
                Timber.e("❌ 配置文件复制失败，使用fallback: $id")
                val fallbackConfig = createFallbackConfig(id)
                cache[id] = fallbackConfig
                Timber.w("🔄 使用fallback配置: ${fallbackConfig.systemPrompt.length}字符")
                return fallbackConfig
            }

            val jsonText = file.readText()
            Timber.d("📄 读取JSON内容: ${jsonText.length}字符")
            Timber.d("📄 JSON前100字符: ${jsonText.take(100)}")

            // 🔥 新增：兼容性处理 - 如果JSON中role字段缺失但有system字段，自动转换
            val config =
                try {
                    json.decodeFromString<PromptConfig>(jsonText)
                } catch (e: Exception) {
                    if (e.message?.contains("role") == true && e.message?.contains("missing") == true) {
                        Timber.w("⚠️ 检测到role字段缺失，尝试兼容性转换...")

                        // 解析为通用JsonObject进行字段转换
                        val jsonObject = json.parseToJsonElement(jsonText).jsonObject.toMutableMap()

                        // 如果没有role但有system，进行转换
                        if (!jsonObject.containsKey("role") && jsonObject.containsKey("system")) {
                            jsonObject["role"] = jsonObject["system"]!!
                            Timber.i("✅ 已转换system字段为role字段")
                        } else if (!jsonObject.containsKey("role")) {
                            // 如果都没有，添加默认role
                            jsonObject["role"] = kotlinx.serialization.json.JsonPrimitive("专业健身AI助手GymBro")
                            Timber.i("✅ 已添加默认role字段")
                        }

                        // 重新序列化并解析
                        val fixedJsonText = json.encodeToString(
                            kotlinx.serialization.json.JsonObject(jsonObject),
                        )
                        json.decodeFromString<PromptConfig>(fixedJsonText)
                    } else {
                        throw e
                    }
                }

            // 🔥 验证配置完整性
            if (config.systemPrompt.isBlank()) {
                Timber.w("⚠️ 配置${id}的systemPrompt为空")
            } else {
                Timber.d("✅ 配置${id}的systemPrompt长度: ${config.systemPrompt.length}字符")
                Timber.d("✅ systemPrompt前100字符: ${config.systemPrompt.take(100)}")
            }

            cache[id] = config
            Timber.i("📄 加载prompt配置成功: $id (${config.displayName})")
            return config
        } catch (e: Exception) {
            Timber.e(e, "❌ 加载prompt配置失败: $id")
            // 返回默认配置作为fallback
            val fallbackConfig = createFallbackConfig(id)
            cache[id] = fallbackConfig
            Timber.w("🔄 异常fallback配置: ${fallbackConfig.systemPrompt.length}字符")
            return fallbackConfig
        }
    }

    private fun copyAssetsIfNeeded() {
        try {
            val promptsDir = File(context.filesDir, PROMPTS_DIR)
            if (!promptsDir.exists()) {
                promptsDir.mkdirs()
            }

            // 🔥 修复：复制核心prompt文件，确保JSON配置完整加载
            val assetFiles =
                listOf(
                    "standard.json",
                    "layered.json",
                    "pipeline.json",
                    "blank.json",
                )
            assetFiles.forEach { fileName ->
                // 🔥 新增：每次都检查版本一致性
                copyAssetFileWithVersionCheck(fileName)
            }

            Timber.i("📄 Prompt文件复制完成，共${assetFiles.size}个文件")
        } catch (e: Exception) {
            Timber.e(e, "❌ 复制assets prompt文件失败")
        }
    }

    /**
     * 🔥 新增：带版本检查的文件复制方法
     * 比较assets和本地文件内容，如果不一致则更新
     */
    private fun copyAssetFileWithVersionCheck(fileName: String) {
        try {
            val targetFile = File(context.filesDir, "$PROMPTS_DIR/$fileName")

            // 读取assets中的最新内容
            val assetsContent =
                context.assets.open("prompts/$fileName").use {
                    it.readBytes().toString(Charsets.UTF_8)
                }

            // 如果本地文件存在，比较内容
            if (targetFile.exists()) {
                val localContent = targetFile.readText()

                // 比较内容哈希值
                val assetsHash = assetsContent.hashCode()
                val localHash = localContent.hashCode()

                if (assetsHash != localHash) {
                    Timber.w("⚠️ 检测到${fileName}版本不一致，需要更新")
                    Timber.d("  Assets哈希: $assetsHash")
                    Timber.d("  本地哈希: $localHash")

                    // 备份旧文件（可选）
                    val backupFile = File(targetFile.parentFile, "$fileName.backup")
                    targetFile.copyTo(backupFile, overwrite = true)
                    Timber.d("📄 已备份旧文件到: ${backupFile.name}")

                    // 更新为新版本
                    targetFile.writeText(assetsContent)
                    Timber.i("✅ 已更新${fileName}为最新版本")

                    // 清除该配置的缓存
                    val configId = fileName.removeSuffix(".json")
                    cache.remove(configId)
                    Timber.i("🧹 已清除${configId}的缓存")
                } else {
                    Timber.d("✅ ${fileName}版本一致，无需更新")
                }
            } else {
                // 文件不存在，直接复制
                Timber.d("📄 首次复制文件: $fileName")
                targetFile.parentFile?.mkdirs()
                targetFile.writeText(assetsContent)
                Timber.i("✅ 成功复制prompt文件: $fileName (${assetsContent.length}字节)")
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ 复制prompt文件异常: $fileName")
            // 不抛出异常，允许继续处理其他文件
        }
    }

    private fun copyAssetFile(fileName: String) {
        try {
            val targetFile = File(context.filesDir, "$PROMPTS_DIR/$fileName")
            Timber.d("🔍 准备复制文件: $fileName -> ${targetFile.absolutePath}")

            if (!targetFile.exists()) {
                Timber.d("📄 开始复制assets文件: prompts/$fileName")

                // 检查assets文件是否存在
                val assetList = context.assets.list("prompts") ?: emptyArray()
                Timber.d("📂 Assets prompts目录文件列表: ${assetList.joinToString(", ")}")

                if (!assetList.contains(fileName)) {
                    Timber.e("❌ Assets中不存在文件: $fileName")
                    return
                }

                context.assets.open("prompts/$fileName").use { input ->
                    targetFile.outputStream().use { output ->
                        input.copyTo(output)
                    }
                }

                // 验证文件复制成功
                if (targetFile.exists() && targetFile.length() > 0) {
                    Timber.i("✅ 成功复制prompt文件: $fileName (${targetFile.length()}字节)")
                } else {
                    Timber.e("❌ 文件复制失败或为空: $fileName")
                }
            } else {
                Timber.d("📄 文件已存在: $fileName (${targetFile.length()}字节)")
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ 复制prompt文件异常: $fileName")
        }
    }

    /**
     * 🔥 新增：调试对比assets和本地文件
     */
    private fun debugCompareAssetsAndLocal(configId: String) {
        try {
            val fileName = "$configId.json"

            // 读取assets文件
            val assetsContent =
                context.assets.open("prompts/$fileName").use {
                    it.readBytes().toString(Charsets.UTF_8)
                }

            // 读取本地文件
            val localFile = File(context.filesDir, "$PROMPTS_DIR/$fileName")
            val localContent =
                if (localFile.exists()) {
                    localFile.readText()
                } else {
                    "本地文件不存在"
                }

            // 解析版本信息
            val assetsVersion =
                try {
                    val assetsJson = json.parseToJsonElement(assetsContent).jsonObject
                    (assetsJson["description"] as? kotlinx.serialization.json.JsonPrimitive)?.content ?: "未知版本"
                } catch (e: Exception) {
                    "解析失败"
                }

            val localVersion =
                try {
                    if (localFile.exists()) {
                        val localJson = json.parseToJsonElement(localContent).jsonObject
                        (localJson["description"] as? kotlinx.serialization.json.JsonPrimitive)?.content ?: "未知版本"
                    } else {
                        "文件不存在"
                    }
                } catch (e: Exception) {
                    "解析失败"
                }

            // 输出对比结果
            Timber.e("🔍 [DEBUG] 文件对比结果:")
            Timber.e("📁 Assets版本: $assetsVersion")
            Timber.e("💾 本地版本: $localVersion")
            Timber.e("📊 Assets哈希: ${assetsContent.hashCode()}")
            Timber.e("📊 本地哈希: ${localContent.hashCode()}")
            Timber.e("📏 Assets长度: ${assetsContent.length}")
            Timber.e("📏 本地长度: ${localContent.length}")

            if (assetsContent.hashCode() != localContent.hashCode()) {
                Timber.e("⚠️ [DEBUG] 文件内容不一致！需要强制更新")

                // 强制删除本地文件并重新复制
                if (localFile.exists()) {
                    localFile.delete()
                    Timber.e("🗑️ [DEBUG] 已删除旧的本地文件")
                }

                localFile.writeText(assetsContent)
                Timber.e("📄 [DEBUG] 已强制复制assets文件到本地")
            } else {
                Timber.e("✅ [DEBUG] 文件内容一致")
            }
        } catch (e: Exception) {
            Timber.e(e, "❌ [DEBUG] 文件对比失败")
        }
    }

    /**
     * 🔥 新增：从配置创建 SystemLayer
     * 将 JSON 配置转换为 SystemLayer 对象
     */
    private fun createSystemLayerFromConfig(config: PromptConfig): SystemLayer =
        SystemLayer(
            role = config.role,
            capabilities = config.capabilities,
            constraints = config.constraints,
            outputFormat = config.outputFormat,
            systemPrompt = config.systemPrompt, // 🔥 使用JSON配置的systemPrompt
        )

    private fun createFallbackConfig(id: String): PromptConfig =
        PromptConfig(
            id = id,
            displayName = "默认配置",
            description = "fallback配置",
            version = "1.0.0",
            systemPrompt = "你是专业健身AI助手GymBro。请回答用户的健身相关问题。",
            outputFormat = "简洁明了的回答",
            constraints = listOf("保持专业且易懂的语言"),
            capabilities = listOf("回答健身相关问题"),
            role = "专业健身AI助手GymBro",
            enableThinking = false,
        )
}

/**
 * Prompt配置数据类
 */
@Serializable
data class PromptConfig(
    val id: String,
    val displayName: String,
    val description: String,
    val version: String,
    val protocols: List<String> = emptyList(), // 🔥 新增：协议列表
    val systemPrompt: String,
    val outputFormat: String = "简洁明了的回答", // 🔥 修复：提供默认值
    val role: String = "专业健身AI助手GymBro", // 🔥 修复：提供默认值，解决MissingFieldException
    val capabilities: List<String>,
    val constraints: List<String>,
    val tools: List<PromptTool> = emptyList(), // 🔥 新增：工具列表
    val enableThinking: Boolean? = false,
    val thinkingFormat: String? = null,
)

/**
 * 工具配置数据类
 */
@Serializable
data class PromptTool(
    val name: String,
    val params: List<String>,
)

package com.example.gymbro.domain.workout.usecase.session

import com.example.gymbro.core.error.types.ModernResult
import com.example.gymbro.core.error.types.business.BusinessErrors
import com.example.gymbro.core.logging.Logger
import com.example.gymbro.domain.exercise.model.ExerciseSet
import com.example.gymbro.domain.workout.repository.SessionRepository
import io.mockk.MockKAnnotations
import io.mockk.coVerify
import io.mockk.impl.annotations.MockK
import kotlinx.coroutines.test.StandardTestDispatcher
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import kotlin.test.assertTrue

/**
 * RecordExerciseSetUseCase 单元测试
 *
 * 遵循GymBro测试规范：
 * ✔️ 测试UseCase的核心业务逻辑
 * ✔️ Mock所有外部依赖
 * ✔️ 验证ModernResult返回类型
 * ✔️ 测试参数验证和错误处理
 */
class RecordExerciseSetUseCaseTest {

    @MockK
    private lateinit var sessionRepository: SessionRepository

    @MockK
    private lateinit var logger: Logger

    private val testDispatcher = StandardTestDispatcher()
    private lateinit var useCase: RecordExerciseSetUseCase

    @Before
    fun setup() {
        MockKAnnotations.init(this)
        useCase = RecordExerciseSetUseCase(
            sessionRepository = sessionRepository,
            dispatcher = testDispatcher,
            logger = logger,
        )
    }

    @Test
    fun `execute should return error for blank sessionId`() = runTest(testDispatcher) {
        // Given
        val params = RecordExerciseSetUseCase.Params(
            sessionId = "",
            performedExerciseId = "exercise-123",
            set = createSampleExerciseSet(),
        )

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)
        coVerify(exactly = 0) { sessionRepository.getSessionById(any()) }
    }

    @Test
    fun `execute should return error for blank exerciseId`() = runTest(testDispatcher) {
        // Given
        val params = RecordExerciseSetUseCase.Params(
            sessionId = "session-123",
            performedExerciseId = "",
            set = createSampleExerciseSet(),
        )

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)
        coVerify(exactly = 0) { sessionRepository.getSessionById(any()) }
    }

    @Test
    fun `execute should return not implemented error for valid parameters`() = runTest(testDispatcher) {
        // Given
        val params = RecordExerciseSetUseCase.Params(
            sessionId = "session-123",
            performedExerciseId = "exercise-456",
            set = createSampleExerciseSet(),
        )

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)
        val errorMessage = (result.error as BusinessErrors.BusinessError).uiMessage?.toString()
        assertTrue(errorMessage?.contains("功能暂未实现") == true)
        coVerify(exactly = 0) { sessionRepository.getSessionById(any()) }
    }

    @Test
    fun `execute should handle valid parameters with proper validation`() = runTest(testDispatcher) {
        // Given
        val params = RecordExerciseSetUseCase.Params(
            sessionId = "session-123",
            performedExerciseId = "exercise-456",
            set = createSampleExerciseSet(),
        )

        // When
        val result = useCase.execute(params)

        // Then
        // 由于功能暂未实现，应该返回错误但不是参数验证错误
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)

        // 确保参数验证通过了（没有返回参数验证错误）
        val errorMessage = (result.error as BusinessErrors.BusinessError).uiMessage?.toString()
        assertTrue(errorMessage?.contains("功能暂未实现") == true)
        assertTrue(!errorMessage!!.contains("不能为空"))
    }

    @Test
    fun `execute should handle exception gracefully`() = runTest(testDispatcher) {
        // Given
        val params = RecordExerciseSetUseCase.Params(
            sessionId = "session-123",
            performedExerciseId = "exercise-456",
            set = createSampleExerciseSet(),
        )

        // 这个测试验证异常处理机制是否正常工作
        // 由于当前实现总是返回"功能暂未实现"错误，我们验证这个行为

        // When
        val result = useCase.execute(params)

        // Then
        assertTrue(result is ModernResult.Error)
        assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)
        val businessError = result.error as BusinessErrors.BusinessError
        assertTrue(businessError.operationName == "recordExerciseSet")
    }

    @Test
    fun `execute should validate all required parameters`() = runTest(testDispatcher) {
        // Given - 测试所有参数组合
        val validSet = createSampleExerciseSet()

        val testCases = listOf(
            // 空sessionId
            RecordExerciseSetUseCase.Params("", "exercise-123", validSet),
            // 空exerciseId
            RecordExerciseSetUseCase.Params("session-123", "", validSet),
            // 空白sessionId
            RecordExerciseSetUseCase.Params("   ", "exercise-123", validSet),
            // 空白exerciseId
            RecordExerciseSetUseCase.Params("session-123", "   ", validSet),
        )

        // When & Then
        testCases.forEach { params ->
            val result = useCase.execute(params)
            assertTrue(result is ModernResult.Error, "应该返回错误: $params")
            assertTrue(
                (result as ModernResult.Error).error is BusinessErrors.BusinessError,
                "应该是业务错误: $params",
            )
        }
    }

    @Test
    fun `execute should handle different ExerciseSet configurations`() = runTest(testDispatcher) {
        // Given
        val exerciseSets = listOf(
            createSampleExerciseSet(weight = 100.0, reps = 10),
            createSampleExerciseSet(weight = 0.0, reps = 20), // 自重训练
            createSampleExerciseSet(weight = 50.5, reps = 8), // 小数重量
            createSampleExerciseSet(weight = 200.0, reps = 1), // 大重量低次数
        )

        // When & Then
        exerciseSets.forEach { exerciseSet ->
            val params = RecordExerciseSetUseCase.Params(
                sessionId = "session-123",
                performedExerciseId = "exercise-456",
                set = exerciseSet,
            )

            val result = useCase.execute(params)

            // 所有情况都应该返回"功能暂未实现"错误，而不是参数验证错误
            assertTrue(result is ModernResult.Error)
            assertTrue((result as ModernResult.Error).error is BusinessErrors.BusinessError)
            val errorMessage = (result.error as BusinessErrors.BusinessError).uiMessage?.toString()
            assertTrue(errorMessage?.contains("功能暂未实现") == true)
        }
    }

    // === 辅助方法 ===

    private fun createSampleExerciseSet(
        weight: Double = 100.0,
        reps: Int = 10,
    ): ExerciseSet {
        return ExerciseSet(
            id = "set-123",
            exerciseId = "exercise-456",
            order = 1,
            weight = weight,
            reps = reps,
            duration = null,
            distance = null,
            restSeconds = 60,
            rpe = 8,
            notes = null,
            isCompleted = true,
            completedAt = System.currentTimeMillis(),
        )
    }
}
package com.example.gymbro.features.coach.aicoach.internal.components.input

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.example.gymbro.designSystem.preview.GymBroPreview
import com.example.gymbro.designSystem.theme.GymBroTheme
import com.example.gymbro.designSystem.theme.tokens.Tokens
import com.example.gymbro.features.coach.aicoach.AiCoachContract
import com.example.gymbro.features.coach.aicoach.getDisplayName

// 注意：API提供商显示名称逻辑已统一到AiCoachContract.ApiProvider.getDisplayName()扩展函数

@Stable
private fun getPromptModeDisplayName(mode: String): String =
    when (mode) {
        "standard" -> "标准"
        "layered" -> "分层"
        "pipeline" -> "流水线"
        "blank" -> "空白"
        else -> "未知"
    }

/**
 * 🎨 ChatGPT风格操作工具栏 - 简化优化版
 *
 * 🎯 设计哲学：
 * - 完全模仿ChatGPT的底部操作栏设计
 * - 移除复杂装饰和过度动画
 * - Token化所有设计规格
 * - 性能优先，避免高频重组
 * - 简洁优雅的交互体验
 *
 * 🔧 优化要点：
 * - 移除液态玻璃等复杂背景效果
 * - 简化动画，只保留必要的fade效果
 * - 使用记忆化避免不必要的重组
 * - Token化图标、间距、圆角规格
 * - 固定布局避免测量开销
 *
 * 🚨 性能策略：
 * - @Stable注解确保重组优化
 * - remember缓存交互状态
 * - 避免复杂的动画计算
 * - 直接使用主题颜色，减少计算
 */

/**
 * 🎨 ChatGPT风格操作工具栏主组件 - 性能优化版
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
internal fun ActionToolbar(
    state: AiCoachContract.State,
    onIntent: (AiCoachContract.Intent) -> Unit,
    modifier: Modifier = Modifier,
    themeState: com.example.gymbro.designSystem.components.extras.ThemeState? = null,
) {
    val selectedImages = state.inputState.selectedImages

    // 🚀 性能优化：记忆化颜色计算
    val iconTint = remember {
        derivedStateOf {
            Color(0xFF6B7280) // 固定的中性色，避免主题重组
        }
    }.value

    // 🔥 简化布局：只需要基础容器，点击外部关闭已移到Screen级别
    Box(modifier = modifier.fillMaxWidth()) {
        // 🚨 图片预览区域 - 简化动画
        AnimatedVisibility(
            visible = selectedImages.isNotEmpty(),
            enter = fadeIn(tween(200)),
            exit = fadeOut(tween(150)),
        ) {
            ChatGPTImagePreviewRow(
                images = selectedImages,
                onRemoveImage = { imageUrl ->
                    onIntent(AiCoachContract.Intent.OnRemoveImageClick(imageUrl))
                },
            )
        }

        // 🎯 主操作栏
        ChatGPTActionRow(
            state = state,
            onIntent = onIntent,
            iconTint = iconTint,
        )

        // 🚀 工具面板现在由Portal层级处理，此处无需渲染
        // 工具面板的显示/隐藏由AiCoachScreen的Portal组件统一管理
    }
}

/**
 * 🎨 ChatGPT风格图片预览行 - 简化版
 */
@Composable
private fun ChatGPTImagePreviewRow(
    images: List<String>,
    onRemoveImage: (String) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyRow(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .heightIn(max = 80.dp) // 🚨 固定最大高度
            .padding(vertical = Tokens.Spacing.Small),
        horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
        contentPadding = PaddingValues(horizontal = Tokens.Spacing.Medium),
    ) {
        items(images) { imageUrl ->
            ChatGPTImagePreviewItem(
                imageUrl = imageUrl,
                onRemove = { onRemoveImage(imageUrl) },
            )
        }
    }
}

/**
 * 🎨 ChatGPT风格图片预览项 - Token化版
 */
@Composable
private fun ChatGPTImagePreviewItem(
    imageUrl: String,
    onRemove: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .size(Tokens.Icon.TouchTarget) // Token化尺寸
            .clip(RoundedCornerShape(Tokens.Radius.Small)),
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(imageUrl)
                .crossfade(true)
                .build(),
            contentDescription = "Selected Image",
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize(),
        )

        // 🎯 简化移除按钮
        IconButton(
            onClick = onRemove,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .size(Tokens.Icon.Standard), // Token化尺寸
        ) {
            Surface(
                shape = CircleShape,
                color = MaterialTheme.colorScheme.surface.copy(alpha = 0.9f),
                modifier = Modifier.size(Tokens.Icon.Small),
            ) {
                Icon(
                    imageVector = Icons.Default.Close,
                    contentDescription = "Remove",
                    tint = MaterialTheme.colorScheme.onSurface,
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(2.dp),
                )
            }
        }
    }
}

/**
 * 🎨 ChatGPT风格主操作行 - 核心实现（容器内优化版）
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
private fun ChatGPTActionRow(
    state: AiCoachContract.State,
    onIntent: (AiCoachContract.Intent) -> Unit,
    iconTint: Color,
    modifier: Modifier = Modifier,
) {
    // 🔥 使用Box来支持图片选择器的相对定位，移除外部点击关闭以避免按钮移动
    Box(
        modifier = modifier
            .fillMaxWidth(),
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = Tokens.Spacing.XSmall), // 🔧 减少垂直padding，适配容器内使用
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            // 🎯 左侧操作按钮 - 🔥 只保留按钮，图片选择器移到Screen级别
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(Tokens.Spacing.Small),
            ) {
                // 🔥 图片按钮 - 只负责触发事件，不包含面板
                ChatGPTImagePickerButton(
                    onImagePickerClick = {
                        onIntent(AiCoachContract.Intent.ShowImagePicker)
                    },
                    iconTint = iconTint,
                )

                // 🔥 工具按钮 - 简化设计
                ChatGPTActionButton(
                    icon = Icons.Default.AutoAwesome,
                    contentDescription = "使用工具",
                    tint = iconTint,
                    onClick = {
                        onIntent(AiCoachContract.Intent.OnToolsClick)
                    },
                )
            }

            // 🎯 中央模型信息 - 使用完整功能ModelInfoBar
            ModelInfoBar(
                modelDisplayName = state.currentApiProvider.getDisplayName(),
                promptModeDisplayName = getPromptModeDisplayName(state.currentPromptMode),
                modifier = Modifier.weight(1f),
            )

            // 🎯 右侧发送按钮 - 使用完整功能的ChatGPT风格组件
            ChatGPTDynamicSendButton(
                sendButtonState = state.inputState.sendButtonState,
                onSend = { onIntent(AiCoachContract.Intent.SendMessage(state.inputState.text)) },
                onVoice = { onIntent(AiCoachContract.Intent.StartVoiceInput) },
                onStop = { onIntent(AiCoachContract.Intent.OnStopGenerationClick) },
                enabled = state.canSendMessage, // 🔥 修复：添加enabled状态控制
            )
        }
    }
}

/**
 * 🎨 ChatGPT风格操作按钮 - 统一组件
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
internal fun ChatGPTActionButton(
    icon: ImageVector,
    contentDescription: String,
    tint: Color,
    onClick: () -> Unit,
    onLongClick: (() -> Unit)? = null,
    modifier: Modifier = Modifier,
) {
    Box(
        modifier = modifier
            .size(Tokens.Icon.TouchTarget) // Token化触摸目标
            .clip(CircleShape)
            .combinedClickable(
                onClick = onClick,
                onLongClick = onLongClick,
            ),
        contentAlignment = Alignment.Center,
    ) {
        Icon(
            imageVector = icon,
            contentDescription = contentDescription,
            tint = tint,
            modifier = Modifier.size(Tokens.Icon.Standard), // Token化图标尺寸
        )
    }
}

// ChatGPTModelInfo函数已移除，现在使用独立的ModelInfoBar组件

// 🚀 ChatGPTToolsOverlay已移除 - 现在使用Portal层级架构
// 工具面板覆盖逻辑已迁移到PortalToolsPanel.kt中实现

// 🎯 图片选择器组件已移动到 ImagePickerComponents.kt 文件中

// === 向后兼容的别名函数已移除 ===
// 现在统一使用独立的ModelInfoBar组件 (从ModelInfoBar.kt导入)

// === 预览组件 ===

@GymBroPreview
@Composable
private fun ActionToolbarPreview() {
    GymBroTheme {
        val mockState = AiCoachContract.State(
            inputState = AiCoachContract.InputState(
                text = "How to build muscle?",
                placeholder = "询问任何问题",
                sendButtonState = AiCoachContract.SendButtonState.SEND,
            ),
            isLoading = false,
        )

        Surface(
            color = MaterialTheme.colorScheme.background,
        ) {
            ActionToolbar(
                state = mockState,
                onIntent = { /* no-op */ },
                modifier = Modifier.padding(Tokens.Spacing.Medium),
            )
        }
    }
}

@GymBroPreview
@Composable
private fun ChatGPTActionRowPreview() {
    GymBroTheme {
        val mockState = AiCoachContract.State(
            inputState = AiCoachContract.InputState(
                text = "",
                placeholder = "询问任何问题",
                sendButtonState = AiCoachContract.SendButtonState.VOICE,
            ),
            isLoading = false,
        )

        Surface(
            color = MaterialTheme.colorScheme.surface,
            shape = RoundedCornerShape(Tokens.Radius.Large),
        ) {
            ChatGPTActionRow(
                state = mockState,
                onIntent = { /* no-op */ },
                iconTint = Color(0xFF6B7280),
                modifier = Modifier.padding(Tokens.Spacing.Medium),
            )
        }
    }
}

// 🎯 图片选择器预览组件已移动到 ImagePickerComponents.kt 文件中

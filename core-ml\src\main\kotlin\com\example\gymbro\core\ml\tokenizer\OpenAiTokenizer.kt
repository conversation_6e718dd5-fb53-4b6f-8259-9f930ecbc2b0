package com.example.gymbro.core.ml.tokenizer

import com.example.gymbro.core.ai.tokenizer.ModelTypes
import com.example.gymbro.core.ai.tokenizer.TokenizerService
import com.example.gymbro.core.ai.tokenizer.TokenizerType
import com.knuddels.jtokkit.Encodings
import com.knuddels.jtokkit.api.Encoding
import com.knuddels.jtokkit.api.EncodingType
import com.knuddels.jtokkit.api.ModelType
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * OpenAI Tokenizer 包装类
 *
 * 使用 JTokkit 库提供真实的 OpenAI tokenizer 功能，支持准确的 token 计算
 * 包含降级策略，当 JTokkit 失败时回退到字符估算
 *
 * 支持的编码类型：
 * - cl100k_base: GPT-4, GPT-3.5-turbo, text-embedding-ada-002
 * - p50k_base: Codex models, text-davinci-002, text-davinci-003
 * - r50k_base: GPT-3 models like davinci
 * - o200k_base: GPT-4o models
 */
@Singleton
class OpenAiTokenizer @Inject constructor() : TokenizerService {

    companion object {
        /**
         * 默认编码类型
         * 使用 cl100k_base，兼容 GPT-4 和 GPT-3.5-turbo
         */
        private val DEFAULT_ENCODING_TYPE = EncodingType.CL100K_BASE

        /**
         * 字符到 token 的粗略比例
         * 用于降级策略的估算
         */
        private const val CHARS_PER_TOKEN_FALLBACK = 4

        /**
         * 最大文本长度限制
         * 防止过长文本导致性能问题
         */
        private const val MAX_TEXT_LENGTH = 100_000
    }

    private val encodingRegistry = Encodings.newDefaultEncodingRegistry()
    private val defaultEncoding: Encoding by lazy {
        try {
            encodingRegistry.getEncoding(DEFAULT_ENCODING_TYPE)
        } catch (e: Exception) {
            Timber.w(e, "Failed to initialize default encoding, will use fallback")
            null
        } ?: createFallbackEncoding()
    }

    /**
     * 实现 TokenizerService 接口 - 计算文本的 token 数量
     *
     * @param text 输入文本
     * @param modelType 可选的模型类型标识
     * @return token 数量
     */
    override fun countTokens(text: String, modelType: String?): Int {
        if (text.isEmpty()) return 0

        // 🔥 【UTF-8修复】清理非UTF-8字符，防止JTokkit崩溃
        val cleanedText = cleanUtf8Text(text)

        // 防止过长文本
        val processedText =
            if (cleanedText.length > MAX_TEXT_LENGTH) {
                Timber.w("Text too long (${cleanedText.length} chars), truncating to $MAX_TEXT_LENGTH")
                cleanedText.take(MAX_TEXT_LENGTH)
            } else {
                cleanedText
            }

        return try {
            val encoding = getEncodingForModelString(modelType)
            val tokens = encoding.encode(processedText)
            val tokenCount = tokens.size()

            // 🔥 【调试】记录成功的Token计算
            Timber.v("🎯 [TokenCount] JTokkit成功: $tokenCount tokens for ${processedText.length} chars")

            tokenCount
        } catch (e: Exception) {
            Timber.w(
                e,
                "🚨 [TokenCount] JTokkit失败，回退到估算: ${e.message}",
            )
            val estimatedCount = estimateTokensByCharacters(processedText)
            Timber.v("🎯 [TokenCount] 估算结果: $estimatedCount tokens for ${processedText.length} chars")
            estimatedCount
        }
    }

    /**
     * 实现 TokenizerService 接口 - 对文本进行编码，返回 token IDs
     *
     * @param text 输入文本
     * @param modelType 可选的模型类型标识
     * @return token IDs 列表
     */
    override fun encode(text: String, modelType: String?): List<Int> {
        if (text.isEmpty()) return emptyList()

        return try {
            val encoding = getEncodingForModelString(modelType)
            val tokens = encoding.encode(text)
            tokens.toArray().toList()
        } catch (e: Exception) {
            Timber.w(e, "Failed to encode text using JTokkit")
            emptyList()
        }
    }

    /**
     * 实现 TokenizerService 接口 - 对 token IDs 进行解码，返回文本
     *
     * @param tokenIds token IDs 列表
     * @param modelType 可选的模型类型标识
     * @return 解码后的文本
     */
    override fun decode(tokenIds: List<Int>, modelType: String?): String {
        if (tokenIds.isEmpty()) return ""

        return try {
            val encoding = getEncodingForModelString(modelType)
            val intArrayList = com.knuddels.jtokkit.api.IntArrayList()
            tokenIds.forEach { intArrayList.add(it) }
            encoding.decode(intArrayList)
        } catch (e: Exception) {
            Timber.w(e, "Failed to decode tokens using JTokkit")
            ""
        }
    }

    /**
     * 实现 TokenizerService 接口 - 截断文本到指定的 token 数量
     *
     * @param text 输入文本
     * @param maxTokens 最大 token 数量
     * @param modelType 可选的模型类型标识
     * @return 截断后的文本
     */
    override fun truncateToTokenLimit(text: String, maxTokens: Int, modelType: String?): String {
        if (text.isEmpty() || maxTokens <= 0) return ""

        val currentTokens = countTokens(text, modelType)
        if (currentTokens <= maxTokens) return text

        return try {
            val encoding = getEncodingForModelString(modelType)
            val tokens = encoding.encode(text)

            // 截断到指定数量
            val truncatedTokens = tokens.toArray().take(maxTokens)
            val intArrayList = com.knuddels.jtokkit.api.IntArrayList()
            truncatedTokens.forEach { intArrayList.add(it) }
            encoding.decode(intArrayList)
        } catch (e: Exception) {
            Timber.w(e, "Failed to truncate using JTokkit, using character estimation")
            truncateByCharacterEstimation(text, maxTokens)
        }
    }

    /**
     * 实现 TokenizerService 接口 - 检查 tokenizer 是否可用
     *
     * @return true 如果 tokenizer 正常工作
     */
    override fun isAvailable(): Boolean {
        return try {
            val testEncoding = encodingRegistry.getEncoding(DEFAULT_ENCODING_TYPE)
            val testTokens = testEncoding.encode("test")
            testTokens.size() > 0
        } catch (e: Exception) {
            Timber.w(e, "JTokkit is not available")
            false
        }
    }

    /**
     * 实现 TokenizerService 接口 - 获取 tokenizer 类型
     *
     * @return tokenizer 类型
     */
    override fun getType(): TokenizerType = TokenizerType.OPENAI_TOKENIZER

    /**
     * 检查 JTokkit 是否可用（向后兼容方法）
     *
     * @return true 如果 JTokkit 正常工作
     */
    fun isJTokkitAvailable(): Boolean = isAvailable()

    /**
     * 获取指定模型的编码器（字符串版本）
     */
    private fun getEncodingForModelString(modelType: String?): Encoding {
        return if (modelType != null) {
            try {
                val jtokkitModelType = convertStringToModelType(modelType)
                if (jtokkitModelType != null) {
                    encodingRegistry.getEncodingForModel(jtokkitModelType)
                } else {
                    Timber.w("Unknown model type: $modelType, using default")
                    defaultEncoding
                }
            } catch (e: Exception) {
                Timber.w(e, "Failed to get encoding for model $modelType, using default")
                defaultEncoding
            }
        } else {
            defaultEncoding
        }
    }

    /**
     * 将字符串模型类型转换为 JTokkit ModelType
     */
    private fun convertStringToModelType(modelType: String): ModelType? {
        return when (modelType) {
            ModelTypes.GPT_4 -> ModelType.GPT_4
            ModelTypes.GPT_4_TURBO -> ModelType.GPT_4_TURBO
            ModelTypes.GPT_3_5_TURBO -> ModelType.GPT_3_5_TURBO
            ModelTypes.TEXT_EMBEDDING_ADA_002 -> ModelType.TEXT_EMBEDDING_ADA_002
            ModelTypes.TEXT_DAVINCI_003 -> ModelType.TEXT_DAVINCI_003
            else -> null
        }
    }

    /**
     * 创建降级编码器
     * 简化实现，避免复杂的 JTokkit API 问题
     */
    private fun createFallbackEncoding(): Encoding {
        // 使用默认编码器作为降级策略
        return try {
            encodingRegistry.getEncoding(EncodingType.CL100K_BASE)
        } catch (e: Exception) {
            Timber.w(e, "Failed to create fallback encoding, using null")
            throw IllegalStateException("Cannot create any encoding", e)
        }
    }

    /**
     * 🔥 【改进】基于字符数估算 token 数量（降级策略）
     *
     * 使用更准确的估算算法：
     * - 英文：约4字符/token
     * - 中文：约1.5字符/token
     * - 代码：约3字符/token
     * - 标点符号：约6字符/token
     */
    private fun estimateTokensByCharacters(text: String): Int {
        if (text.isEmpty()) return 0

        var tokenCount = 0
        var i = 0

        while (i < text.length) {
            val char = text[i]
            when {
                // 中文字符 (CJK)
                char.code in 0x4E00..0x9FFF -> {
                    tokenCount += 1
                    i += 1
                }
                // 英文单词
                char.isLetter() -> {
                    var wordLength = 0
                    while (i < text.length && text[i].isLetterOrDigit()) {
                        wordLength++
                        i++
                    }
                    tokenCount += maxOf(1, wordLength / 4)
                }
                // 数字
                char.isDigit() -> {
                    var numLength = 0
                    while (i < text.length && (text[i].isDigit() || text[i] == '.')) {
                        numLength++
                        i++
                    }
                    tokenCount += maxOf(1, numLength / 3)
                }
                // 代码块标识符
                char == '`' -> {
                    var codeLength = 0
                    while (i < text.length && text[i] == '`') {
                        codeLength++
                        i++
                    }
                    // 代码块内容按3字符/token估算
                    if (codeLength >= 3) {
                        val codeEnd = text.indexOf("```", i)
                        if (codeEnd != -1) {
                            val codeContent = text.substring(i, codeEnd)
                            tokenCount += maxOf(1, codeContent.length / 3)
                            i = codeEnd + 3
                        } else {
                            tokenCount += 1
                            i++
                        }
                    } else {
                        tokenCount += 1
                    }
                }
                // 标点符号和空格
                else -> {
                    tokenCount += 1
                    i += 1
                }
            }
        }

        return maxOf(1, tokenCount)
    }

    /**
     * 基于字符估算截断文本（降级策略）
     */
    private fun truncateByCharacterEstimation(text: String, maxTokens: Int): String {
        val maxChars = maxTokens * CHARS_PER_TOKEN_FALLBACK
        return if (text.length <= maxChars) {
            text
        } else {
            text.take(maxChars) + "..."
        }
    }

    /**
     * 🔥 【UTF-8修复】清理非UTF-8字符，防止JTokkit崩溃
     *
     * 移除或替换可能导致JTokkit库抛出AssertionError的非UTF-8字符
     */
    private fun cleanUtf8Text(text: String): String =
        try {
            // 使用StringBuilder进行高效的字符过滤
            val cleaned = StringBuilder(text.length)

            for (char in text) {
                when {
                    // 保留基本的可打印字符
                    char.isLetterOrDigit() -> cleaned.append(char)
                    char.isWhitespace() -> cleaned.append(char)
                    // 保留常用标点符号
                    char in ".,!?;:()[]{}\"'-_+=*&^%$#@~`|\\/<>" -> cleaned.append(char)
                    // 保留中文字符范围
                    char.code in 0x4E00..0x9FFF -> cleaned.append(char)
                    // 保留其他Unicode字母和数字
                    Character.isUnicodeIdentifierPart(char) -> cleaned.append(char)
                    // 跳过控制字符和其他可能有问题的字符
                    else -> {
                        // 可选：记录被跳过的字符（仅在调试时启用）
                        // Timber.v("Skipping potentially problematic character: ${char.code}")
                    }
                }
            }

            cleaned.toString()
        } catch (e: Exception) {
            Timber.w(e, "Error cleaning UTF-8 text, using original")
            // 如果清理过程出错，返回原文本，让上层的try-catch处理
            text
        }
}

/**
 * OpenAI 模型类型扩展
 * 提供常用模型的快捷访问
 */
object OpenAiModels {
    val GPT_4 = ModelType.GPT_4
    val GPT_4_TURBO = ModelType.GPT_4_TURBO
    val GPT_3_5_TURBO = ModelType.GPT_3_5_TURBO
    val TEXT_EMBEDDING_ADA_002 = ModelType.TEXT_EMBEDDING_ADA_002
    val TEXT_DAVINCI_003 = ModelType.TEXT_DAVINCI_003
}

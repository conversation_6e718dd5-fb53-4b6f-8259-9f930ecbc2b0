package com.example.gymbro.domain.thinkingbox.model

/**
 * 思考消息实体
 */
data class ThinkingMessageEntity(
    val messageId: String,
    val status: String,
    val startedAt: Long,
    val finishedAt: Long?,
    val durationMs: Long?,
    val tokenCount: Int?,
)

/**
 * 思考阶段实体
 */
data class ThinkingPhaseEntity(
    val messageId: String,
    val phaseId: String,
    val title: String,
    val content: String,
    val complete: Boolean,
)

/**
 * 思考最终内容实体
 */
data class ThinkingFinalEntity(
    val messageId: String,
    val markdown: String,
)

/**
 * 完整思考历史（用于回放）
 */
data class ThinkingHistoryComplete(
    val message: ThinkingMessageEntity,
    val phases: List<ThinkingPhaseEntity>,
    val final: ThinkingFinalEntity?,
)